<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>add_stepMotor_action</class>
 <widget class="QDialog" name="add_stepMotor_action">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>775</width>
    <height>119</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <property name="styleSheet">
   <string notr="true">background-color: rgb(255, 170, 127);</string>
  </property>
  <widget class="QLabel" name="label">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>50</y>
     <width>121</width>
     <height>19</height>
    </rect>
   </property>
   <property name="text">
    <string>speed/转速</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="speedLineEdit">
   <property name="geometry">
    <rect>
     <x>100</x>
     <y>50</y>
     <width>113</width>
     <height>25</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(255, 255, 255);</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_3">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>80</y>
     <width>71</width>
     <height>19</height>
    </rect>
   </property>
   <property name="text">
    <string>運行時間</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="durationLineEdit">
   <property name="geometry">
    <rect>
     <x>100</x>
     <y>80</y>
     <width>113</width>
     <height>25</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(255, 255, 255);</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_4">
   <property name="geometry">
    <rect>
     <x>220</x>
     <y>50</y>
     <width>69</width>
     <height>19</height>
    </rect>
   </property>
   <property name="text">
    <string>%</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_5">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>10</y>
     <width>121</width>
     <height>19</height>
    </rect>
   </property>
   <property name="text">
    <string>Name</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="device_name">
   <property name="geometry">
    <rect>
     <x>60</x>
     <y>10</y>
     <width>113</width>
     <height>25</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(255, 255, 255);</string>
   </property>
   <property name="readOnly">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QLabel" name="label_6">
   <property name="geometry">
    <rect>
     <x>550</x>
     <y>10</y>
     <width>61</width>
     <height>19</height>
    </rect>
   </property>
   <property name="text">
    <string>启动电机</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_10">
   <property name="geometry">
    <rect>
     <x>220</x>
     <y>80</y>
     <width>69</width>
     <height>19</height>
    </rect>
   </property>
   <property name="text">
    <string>S</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pushButton_add_to_mainwindow">
   <property name="geometry">
    <rect>
     <x>560</x>
     <y>80</y>
     <width>93</width>
     <height>28</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(225, 225, 225);</string>
   </property>
   <property name="text">
    <string>增加</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pushButton_2">
   <property name="geometry">
    <rect>
     <x>660</x>
     <y>80</y>
     <width>93</width>
     <height>28</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(225, 225, 225);</string>
   </property>
   <property name="text">
    <string>删除</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_8">
   <property name="geometry">
    <rect>
     <x>190</x>
     <y>10</y>
     <width>41</width>
     <height>19</height>
    </rect>
   </property>
   <property name="text">
    <string>Type</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="device_name_2">
   <property name="geometry">
    <rect>
     <x>230</x>
     <y>10</y>
     <width>113</width>
     <height>25</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(255, 255, 255);</string>
   </property>
   <property name="readOnly">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QPushButton" name="pushButton_Addaction">
   <property name="geometry">
    <rect>
     <x>560</x>
     <y>50</y>
     <width>191</width>
     <height>28</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(255, 255, 127);</string>
   </property>
   <property name="text">
    <string>pushButton_addaction</string>
   </property>
  </widget>
  <widget class="QComboBox" name="startComboBox">
   <property name="geometry">
    <rect>
     <x>620</x>
     <y>10</y>
     <width>87</width>
     <height>25</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(255, 255, 255);</string>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
