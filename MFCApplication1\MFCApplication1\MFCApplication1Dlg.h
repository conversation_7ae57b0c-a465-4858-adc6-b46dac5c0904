﻿
// MFCApplication1Dlg.h: 头文件
//

#pragma once


// CMFCApplication1Dlg 对话框
class CMFCApplication1Dlg : public CDialogEx
{
// 构造
public:
	CMFCApplication1Dlg(CWnd* pParent = nullptr);	// 标准构造函数

// 对话框数据
#ifdef AFX_DESIGN_TIME
	enum { IDD = IDD_MFCAPPLICATION1_DIALOG };
#endif

	protected:
	virtual void DoDataExchange(CDataExchange* pDX);	// DDX/DDV 支持


// 实现
protected:
	HICON m_hIcon;

	// 生成的消息映射函数
	virtual BOOL OnInitDialog();
	afx_msg void OnSysCommand(UINT nID, LPARAM lParam);
	afx_msg void OnPaint();
	afx_msg HCURSOR OnQueryDragIcon();
	DECLARE_MESSAGE_MAP()
	void commandhandler(CString name, int srtn);
public:
	afx_msg void OnEnChangeEdit1();
	afx_msg void OnBtnClick1();
	afx_msg void OnBtnClick2();
	afx_msg void OnbtnClick3();
	afx_msg void OnbtnClick4();
	afx_msg void OnbtnClick5();
	afx_msg void OnbtnClick6();
	short rtn;
	short core ;
	short status;
	int HomeMode;
	double m_editHome_HighSpeed;
	double m_editHome_LowSpeed;
	double Home_Acc;
	double Home_Vel;
	double Home_indexVel;
	long m_editHome_Dec;
	short m_editHome_Check;
	short m_editHome_AutoZeroPos;
	long m_editHome_MotorStopDelay;
	int m_edit_dac;
	short axis;
	unsigned short homeStatus;
	//THomeStatus homeStatus;
	bool bStop = false;
	short bFlagAlarm = FALSE;// 伺服报警标志
	short bFlagMError = FALSE;// 跟随误差越限标志
	short bFlagPosLimit = FALSE;// 正限位触发标志
	short bFlagNegLimit = FALSE;// 负限位触发标志
	short bFlagSmoothStoop = FALSE;//平滑停止标志
	short bFlagAbruptStop = FALSE;//急停标志
	short bFlagServoOn = FALSE;//伺服开启标志
	short bFlagMotion = FALSE;//规划器运动标志
	double prfpos;// 规划位置
	double encpos;//实际位置
	double prfvel;// 规划速度
	double prfacc;// 规划加速度
	long IPrfMode;// 运动模式
	char chPrfMode[20];// 运动模式，字符串变量
	long IAxisStatus;// 轴状态
	short jogvel;
	long sts;
	long pSts;
	short run;// 坐标系运动状态查询变量
	long segment;// 坐标系运动完成段查询变量
	long space;// 坐标系的缓存区剩余空间查询变量
	short pMode;
	double pValue;
	afx_msg void OnTimer(UINT_PTR nIDEvent);
	void UpdateFlags();
	void UpdatePrfMode();
	afx_msg void OnbtnClick7();
	afx_msg void OnbtnClick8();
	afx_msg void OnEnChangeEdit5();
	afx_msg void OnBnClickedButton9();
	afx_msg void OnBnClickedButton10();
	afx_msg void OnBnClickedButton11();
	afx_msg void OnBnClickedButton12();
	afx_msg void OnBnClickedButton13();
	afx_msg void OnBnClickedButton14();
	afx_msg void OnBnClickedButton15();
};
