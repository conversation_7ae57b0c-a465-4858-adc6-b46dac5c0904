<?xml version="1.0"?>
<EtherCATConfig>
	<Config>
		<Master AdapterIndex="0" IoUpdateFreq="4" StackDebugLevel="0">
			<Info>
				<Name>GOOGOL EtherCAT Master</Name>
				<Destination>FFFFFFFFFFFF</Destination>
				<Source>000000000000</Source>
				<EtherType>A488</EtherType>
			</Info>
		</Master>
		<Slave>
			<Info>
				<Name>GSHD_EC CiA402 Motion Control</Name>
				<VendorId>1717</VendorId>
				<ProductCode>1026</ProductCode>
				<RevisionNo>268435457</RevisionNo>
				<SerialNo>-1</SerialNo>
				<DevType>1</DevType>
				<NChannel>1</NChannel>
				<UseLrdLwr>0</UseLrdLwr>
			</Info>
			<ProcessData PdoAssign="true" PdoConfig="true">
				<Sm2>
					<Type>Outputs</Type>
					<DefaultSize>0</DefaultSize>
					<StartAddress>4352</StartAddress>
					<ControlByte>100</ControlByte>
					<Enable>true</Enable>
					<Pdo>5632</Pdo>
					<Pdo>5633</Pdo>
					<Pdo>5634</Pdo>
					<Pdo>5635</Pdo>
				</Sm2>
				<Sm3>
					<Type>Inputs</Type>
					<DefaultSize>0</DefaultSize>
					<StartAddress>5120</StartAddress>
					<ControlByte>32</ControlByte>
					<Enable>true</Enable>
					<Pdo>6656</Pdo>
					<Pdo>6657</Pdo>
					<Pdo>6658</Pdo>
					<Pdo>6659</Pdo>
				</Sm3>
				<RxPdo Fixed="false" Mandatory="false" Sm="2" Virtual="false">
					<Index>#x1600</Index>
					<Name LcId="1033">RXPDO 1</Name>
					<Entry Fixed="false">
						<Index>#x6040</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Controlword</Name>
						<Comment>object 0x6040:0</Comment>
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x607a</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Target position</Name>
						<Comment>object 0x607A:0</Comment>
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="false" Mandatory="false" Sm="2" Virtual="false">
					<Index>#x1601</Index>
					<Name LcId="1033">RXPDO 2</Name>
					<Entry Fixed="false">
						<Index>#x60ff</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Target velocity</Name>
						<Comment>object 0x60FF:0</Comment>
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6060</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>8</BitLen>
						<Name LcId="1033">Modes Of operation</Name>
						<Comment>object 0x6060:0</Comment>
						<DataType>USINT</DataType>
						<DefaultValue>8</DefaultValue>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="false" Mandatory="false" Sm="2" Virtual="false">
					<Index>#x1602</Index>
					<Name LcId="1033">RXPDO 3</Name>
					<Entry Fixed="false">
						<Index>#x60b2</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Torque offset</Name>
						<Comment>object 0x60B2:0</Comment>
						<DataType>INT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6071</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Target torque</Name>
						<Comment>object 0x6071:0</Comment>
						<DataType>INT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="false" Mandatory="false" Sm="2" Virtual="false">
					<Index>#x1603</Index>
					<Name LcId="1033">RXPDO 4</Name>
					<Entry Fixed="false">
						<Index>#x60fe</Index>
						<SubIndex>#x1</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Physical outputs</Name>
						<Comment>object 0x60FE:1</Comment>
						<DataType>UDINT</DataType>
						<DefaultValue>2</DefaultValue>
					</Entry>
				</RxPdo>
				<TxPdo Fixed="false" Mandatory="false" Sm="3" Virtual="false">
					<Index>#x1a00</Index>
					<Name LcId="1033">TXPDO 1</Name>
					<Entry Fixed="false">
						<Index>#x6041</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Statusword</Name>
						<Comment>object 0x6041:0</Comment>
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6064</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Position actual value</Name>
						<Comment>object 0x6064:0</Comment>
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="false" Mandatory="false" Sm="3" Virtual="false">
					<Index>#x1a01</Index>
					<Name LcId="1033">TXPDO 2</Name>
					<Entry Fixed="false">
						<Index>#x606c</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Velocity actual value</Name>
						<Comment>object 0x606C:0</Comment>
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6077</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Torque Actual Value</Name>
						<Comment>object 0x6077:0</Comment>
						<DataType>INT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="false" Mandatory="false" Sm="3" Virtual="false">
					<Index>#x1a02</Index>
					<Name LcId="1033">TXPDO 3</Name>
					<Entry Fixed="false">
						<Index>#x603f</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Error code</Name>
						<Comment>object 0x603F:0</Comment>
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6061</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>8</BitLen>
						<Name LcId="1033">Modes Of operation display</Name>
						<Comment>object 0x6061:0</Comment>
						<DataType>USINT</DataType>
						<DefaultValue>8</DefaultValue>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="false" Mandatory="false" Sm="3" Virtual="false">
					<Index>#x1a03</Index>
					<Name LcId="1033">TXPDO 4</Name>
					<Entry Fixed="false">
						<Index>#x60fd</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Digital inputs</Name>
						<Comment>object 0x606D:0</Comment>
						<DataType>UDINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
				</TxPdo>
			</ProcessData>
			<Mailbox>
				<Send>
					<Start>4096</Start>
					<Length>128</Length>
				</Send>
				<Recv>
					<Start>4224</Start>
					<Length>128</Length>
				</Recv>
				<Protocol>CoE</Protocol>
				<CoE PdoCheck="0">
					<InitCmds>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear sm pdos 0x1c12</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7186</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear sm pdos 0x1c13</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7187</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear pdo 0x1a00 entriesX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>1</SubIndex>
							<Data>10004160</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>2</SubIndex>
							<Data>20006460</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entry countX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>0</SubIndex>
							<Data>02</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear pdo 0x1a01 entriesX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6657</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a01 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6657</Index>
							<SubIndex>1</SubIndex>
							<Data>20006C60</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a01 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6657</Index>
							<SubIndex>2</SubIndex>
							<Data>10007760</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a01 entry countX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6657</Index>
							<SubIndex>0</SubIndex>
							<Data>02</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear pdo 0x1a02 entriesX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6658</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a02 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6658</Index>
							<SubIndex>1</SubIndex>
							<Data>10003F60</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a02 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6658</Index>
							<SubIndex>2</SubIndex>
							<Data>08006160</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a02 entry countX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6658</Index>
							<SubIndex>0</SubIndex>
							<Data>02</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear pdo 0x1a03 entriesX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6659</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a03 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6659</Index>
							<SubIndex>1</SubIndex>
							<Data>2000FD60</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a03 entry countX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6659</Index>
							<SubIndex>0</SubIndex>
							<Data>01</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear pdo 0x1600 entriesX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>1</SubIndex>
							<Data>10004060</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>2</SubIndex>
							<Data>20007A60</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entry countX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>0</SubIndex>
							<Data>02</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear pdo 0x1601 entriesX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5633</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1601 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5633</Index>
							<SubIndex>1</SubIndex>
							<Data>2000FF60</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1601 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5633</Index>
							<SubIndex>2</SubIndex>
							<Data>08006060</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1601 entry countX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5633</Index>
							<SubIndex>0</SubIndex>
							<Data>02</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear pdo 0x1602 entriesX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5634</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1602 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5634</Index>
							<SubIndex>1</SubIndex>
							<Data>1000B260</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1602 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5634</Index>
							<SubIndex>2</SubIndex>
							<Data>10007160</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1602 entry countX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5634</Index>
							<SubIndex>0</SubIndex>
							<Data>02</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear pdo 0x1603 entriesX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5635</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1603 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5635</Index>
							<SubIndex>1</SubIndex>
							<Data>2001FE60</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1603 entry countX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5635</Index>
							<SubIndex>0</SubIndex>
							<Data>01</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdos 0x1C12:01 index</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7186</Index>
							<SubIndex>1</SubIndex>
							<Data>0016</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdos 0x1C12:02 index</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7186</Index>
							<SubIndex>2</SubIndex>
							<Data>0116</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdos 0x1C12:03 index</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7186</Index>
							<SubIndex>3</SubIndex>
							<Data>0216</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdos 0x1C12:04 index</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7186</Index>
							<SubIndex>4</SubIndex>
							<Data>0316</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1C12 count</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7186</Index>
							<SubIndex>0</SubIndex>
							<Data>04</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdos 0x1C13:01 index</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7187</Index>
							<SubIndex>1</SubIndex>
							<Data>001A</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdos 0x1C13:02 index</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7187</Index>
							<SubIndex>2</SubIndex>
							<Data>011A</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdos 0x1C13:03 index</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7187</Index>
							<SubIndex>3</SubIndex>
							<Data>021A</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdos 0x1C13:04 index</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7187</Index>
							<SubIndex>4</SubIndex>
							<Data>031A</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1C13 count</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7187</Index>
							<SubIndex>0</SubIndex>
							<Data>04</Data>
						</InitCmd>
						<InitCmd Fixed="false">
							<Transition>PS</Transition>
							<Comment>Opmode</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>24672</Index>
							<SubIndex>0</SubIndex>
							<Data>08</Data>
						</InitCmd>
						<InitCmd Fixed="false">
							<Transition>PS</Transition>
							<Comment>Cycle-time</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>24770</Index>
							<SubIndex>1</SubIndex>
							<Data>01</Data>
						</InitCmd>
						<InitCmd Fixed="false">
							<Transition>PS</Transition>
							<Comment>Cycle-exponent</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>24770</Index>
							<SubIndex>2</SubIndex>
							<Data>FD</Data>
						</InitCmd>
					</InitCmds>
				</CoE>
			</Mailbox>
			<DC>
				<AssignActivate>768</AssignActivate>
				<CycleTime0>1000000</CycleTime0>
				<CycleTime1>0</CycleTime1>
				<ShiftTime0>0</ShiftTime0>
				<ShiftTime1>0</ShiftTime1>
			</DC>
			<IOKeep>
				<Enable>0</Enable>
				<Type>sdo</Type>
			</IOKeep>
		</Slave>
	</Config>
</EtherCATConfig>
