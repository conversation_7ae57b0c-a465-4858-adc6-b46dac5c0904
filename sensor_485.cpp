#include "sensor_485.h"
#include "ui_sensor_485.h"
#include <QMessageBox>
#include <QSerialPortInfo>
#include <QDebug>

Sensor_485::Sensor_485(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::Sensor_485),
    serialPort(new QSerialPort(this))
{
    ui->setupUi(this);
    this->setWindowFlags(Qt::Window); 
    connect(serialPort, &QSerialPort::readyRead, this, &Sensor_485::readData);
    connect(ui->pushButton_RefreshPorts, &QPushButton::clicked, this, &Sensor_485::on_pushButton_RefreshPorts_clicked);
    updateComPorts();



    // 設置 lineEdit_485BackMassae 為只讀
    ui->lineEdit_485BackMassae->setReadOnly(true);
}

Sensor_485::~Sensor_485()
{
    if (serialPort->isOpen()) {
        serialPort->close();
    }
    delete ui;
}

void Sensor_485::updateComPorts()
{
    ui->comboBox_485COMPORT->clear();
    const auto infos = QSerialPortInfo::availablePorts();
    for (const QSerialPortInfo &info : infos) {
        ui->comboBox_485COMPORT->addItem(info.portName());
    }
}

void Sensor_485::initializeSerialPort()
{
    serialPort->setPortName(ui->comboBox_485COMPORT->currentText());
    serialPort->setBaudRate(ui->lineEdit_485BaudRate->text().toInt());

    // 設置數據位
    switch (ui->lineEdit_485DATABITS->text().toInt()) {
    case 5: serialPort->setDataBits(QSerialPort::Data5); break;
    case 6: serialPort->setDataBits(QSerialPort::Data6); break;
    case 7: serialPort->setDataBits(QSerialPort::Data7); break;
    case 8: serialPort->setDataBits(QSerialPort::Data8); break;
    default: serialPort->setDataBits(QSerialPort::Data8);
    }

    // 設置停止位
    if (ui->lineEdit_485STOPBITS->text().toInt() == 2)
        serialPort->setStopBits(QSerialPort::TwoStop);
    else
        serialPort->setStopBits(QSerialPort::OneStop);

    // 設置校驗位
    QString parity = ui->lineEdit_485PARITY->text().toLower();
    if (parity == "none") serialPort->setParity(QSerialPort::NoParity);
    else if (parity == "even") serialPort->setParity(QSerialPort::EvenParity);
    else if (parity == "odd") serialPort->setParity(QSerialPort::OddParity);
    else serialPort->setParity(QSerialPort::NoParity);

    // 設置流控制
    QString flowControl = ui->lineEdit_485FLOWCONTROL->text().toLower();
    if (flowControl == "hardware") serialPort->setFlowControl(QSerialPort::HardwareControl);
    else if (flowControl == "software") serialPort->setFlowControl(QSerialPort::SoftwareControl);
    else serialPort->setFlowControl(QSerialPort::NoFlowControl);
}

void Sensor_485::on_pushButton_485_Connect_clicked()
{
    if (serialPort->isOpen()) {
        serialPort->close();
        ui->pushButton_485_Connect->setText("Connect");
        ui->pushButton_485_Connect->setStyleSheet("");
        ui->lineEdit_485BackMassae->clear();
    } else {
        initializeSerialPort();
        if (serialPort->open(QIODevice::ReadWrite)) {
            ui->pushButton_485_Connect->setText("Disconnect");
            ui->pushButton_485_Connect->setStyleSheet("background-color: red;");
            ui->lineEdit_485BackMassae->clear();
            ui->lineEdit_485BackMassae->append("Connected successfully");
        } else {
            QMessageBox::critical(this, "Error", "Failed to open serial port: " + serialPort->errorString());
        }
    }
}

void Sensor_485::on_pushButton_485sendtest_clicked()
{
    if (!serialPort->isOpen()) {
        QMessageBox::warning(this, "Warning", "Serial port is not open");
        return;
    }

    QString inputText = ui->lineEdit_485send_test->text();
    QByteArray testData;

    // 強制輸出 CheckBox 狀態
    qDebug() << ">>> CheckBox State Before Check:" << ui->checkBox_HEX->isChecked();

    // 檢查是否為HEX格式
    if (ui->checkBox_HEX->isChecked()) {
        qDebug() << ">>> Entering HEX processing block."; // 確認進入了 HEX 分支
        // HEX格式處理
        QString hexString = inputText.remove(" "); // 移除空格

        if (hexString.length() % 2 != 0) {
            QMessageBox::warning(this, "Warning", "十六進制格式錯誤：字符數必須為偶數");
            return;
        }

        bool ok;
        testData.clear(); // 確保 testData 是空的
        for(int i = 0; i < hexString.length(); i += 2) {
            QString byteStr = hexString.mid(i, 2);
            uint8_t byte = byteStr.toUInt(&ok, 16);
            if(ok) {
                testData.append(static_cast<char>(byte));
            } else {
                QMessageBox::warning(this, "Warning", "無效的十六進制格式: " + byteStr); // 提示哪個字節出錯
                return;
            }
        }
        qDebug() << ">>> HEX Data Prepared:" << testData.toHex(); // 輸出準備好的 HEX 數據
    } else {
        qDebug() << ">>> Entering ASCII processing block."; // 確認進入了 ASCII 分支
        // ASCII格式處理
        testData = inputText.toUtf8();
        qDebug() << ">>> ASCII Data Prepared:" << testData.toHex(); // 輸出準備好的 ASCII 數據 (以HEX顯示方便對比)
    }

    qDebug() << ">>> Attempting to write data:" << testData.toHex(); // 輸出最終要寫入的數據
    serialPort->write(testData);
    qDebug() << ">>> Data written.";
}

void Sensor_485::readData()
{
    QByteArray data = serialPort->readAll();
    QString displayString;

    if (ui->checkBox_HEX->isChecked()) {
        // 如果勾選了 HEX，則將數據轉換為十六進制字符串（帶空格）
        displayString = QString::fromLatin1(data.toHex(' '));
    } else {
        // 否則，按 UTF-8 解碼
        displayString = QString::fromUtf8(data);
    }

    // 將處理後的字符串附加到顯示框
    ui->lineEdit_485BackMassae->append(displayString);
}


void Sensor_485::on_pushButton_RefreshPorts_clicked()
{
    updateComPorts();
}



QString Sensor_485::getName() const
{
    return ui->lineEdit_485Name->text();
}

void Sensor_485::setName(const QString& name)
{
    if (ui->lineEdit_485Name->text() != name) {
        ui->lineEdit_485Name->setText(name);
        emit nameChanged(name);
    }
}

// 在这里添加新的方法

bool Sensor_485::isConnected() const
{
    return serialPort->isOpen();
}

bool Sensor_485::sendCommand(const QString& command)
{
    if (!isConnected()) {
        return false;
    }
    QByteArray data = command.toUtf8();
    qint64 bytesWritten = serialPort->write(data);
    return bytesWritten == data.size();
}


bool Sensor_485::sendHexCommand(const QByteArray& hexData)
{
    if (!isConnected()) {
        return false;
    }
    qint64 bytesWritten = serialPort->write(hexData);
    serialPort->flush(); // 添加这行
    return bytesWritten == hexData.size();
}
