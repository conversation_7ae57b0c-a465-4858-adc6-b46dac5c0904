<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Sensor_485</class>
 <widget class="QDialog" name="Sensor_485">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>923</width>
    <height>654</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <property name="styleSheet">
   <string notr="true">background-color: rgb(229, 229, 229);</string>
  </property>
  <widget class="QWidget" name="horizontalLayoutWidget">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>50</y>
     <width>821</width>
     <height>31</height>
    </rect>
   </property>
   <layout class="QHBoxLayout" name="horizontalLayout">
    <item>
     <spacer name="verticalSpacer_4">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <widget class="QLabel" name="label">
      <property name="font">
       <font>
        <bold>true</bold>
       </font>
      </property>
      <property name="text">
       <string>串行端口</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QComboBox" name="comboBox_485COMPORT">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
     </widget>
    </item>
    <item>
     <spacer name="verticalSpacer_3">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <widget class="QLabel" name="label_2">
      <property name="font">
       <font>
        <bold>true</bold>
       </font>
      </property>
      <property name="text">
       <string>波特率</string>
      </property>
     </widget>
    </item>
    <item>
     <spacer name="verticalSpacer_2">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <widget class="QLineEdit" name="lineEdit_485BaudRate">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
     </widget>
    </item>
    <item>
     <spacer name="verticalSpacer">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <widget class="QLabel" name="label_7">
      <property name="font">
       <font>
        <bold>true</bold>
       </font>
      </property>
      <property name="text">
       <string>数据位</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLineEdit" name="lineEdit_485DATABITS">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLabel" name="label_8">
      <property name="font">
       <font>
        <bold>true</bold>
       </font>
      </property>
      <property name="text">
       <string>停止位</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLineEdit" name="lineEdit_485STOPBITS">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLabel" name="label_9">
      <property name="font">
       <font>
        <bold>true</bold>
       </font>
      </property>
      <property name="text">
       <string>奇偶校验</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLineEdit" name="lineEdit_485PARITY">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QWidget" name="horizontalLayoutWidget_3">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>130</y>
     <width>821</width>
     <height>89</height>
    </rect>
   </property>
   <layout class="QHBoxLayout" name="horizontalLayout_3">
    <item>
     <widget class="QPushButton" name="pushButton_485_Connect">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(214, 214, 214);</string>
      </property>
      <property name="text">
       <string>Connect</string>
      </property>
     </widget>
    </item>
    <item>
     <spacer name="verticalSpacer_7">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <widget class="QLabel" name="label_connect">
      <property name="text">
       <string>OK</string>
      </property>
     </widget>
    </item>
    <item>
     <spacer name="verticalSpacer_8">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <spacer name="verticalSpacer_9">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <widget class="QLabel" name="label_6">
      <property name="text">
       <string>回传数据</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QTextEdit" name="lineEdit_485BackMassae">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QWidget" name="horizontalLayoutWidget_4">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>90</y>
     <width>821</width>
     <height>31</height>
    </rect>
   </property>
   <layout class="QHBoxLayout" name="horizontalLayout_4">
    <item>
     <widget class="QLabel" name="label_13">
      <property name="font">
       <font>
        <bold>true</bold>
       </font>
      </property>
      <property name="text">
       <string>流控制</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLineEdit" name="lineEdit_485FLOWCONTROL">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
     </widget>
    </item>
    <item>
     <spacer name="verticalSpacer_10">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <spacer name="verticalSpacer_11">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <widget class="QLabel" name="label_10">
      <property name="font">
       <font>
        <bold>true</bold>
       </font>
      </property>
      <property name="text">
       <string>从站地址</string>
      </property>
     </widget>
    </item>
    <item>
     <spacer name="verticalSpacer_12">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <widget class="QLineEdit" name="lineEdit_485SLAVEADDRESS">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
     </widget>
    </item>
    <item>
     <spacer name="verticalSpacer_13">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <widget class="QLabel" name="label_11">
      <property name="font">
       <font>
        <bold>true</bold>
       </font>
      </property>
      <property name="text">
       <string>超时设置</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLineEdit" name="lineEdit_485TIMEOUT">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLabel" name="label_12">
      <property name="font">
       <font>
        <bold>true</bold>
       </font>
      </property>
      <property name="text">
       <string>重试次数</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLineEdit" name="lineEdit_485RETRAY">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QWidget" name="horizontalLayoutWidget_2">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>10</y>
     <width>160</width>
     <height>31</height>
    </rect>
   </property>
   <layout class="QHBoxLayout" name="horizontalLayout_2">
    <item>
     <widget class="QLabel" name="label_3">
      <property name="font">
       <font>
        <bold>true</bold>
       </font>
      </property>
      <property name="text">
       <string>NAME:</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLineEdit" name="lineEdit_485Name">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QWidget" name="horizontalLayoutWidget_6">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>190</y>
     <width>821</width>
     <height>41</height>
    </rect>
   </property>
   <layout class="QHBoxLayout" name="horizontalLayout_6">
    <item>
     <widget class="QLabel" name="label_5">
      <property name="font">
       <font>
        <bold>true</bold>
       </font>
      </property>
      <property name="text">
       <string/>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLabel" name="label_14">
      <property name="font">
       <font>
        <bold>true</bold>
       </font>
      </property>
      <property name="text">
       <string>上限</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLineEdit" name="lineEdit">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLabel" name="label_15">
      <property name="font">
       <font>
        <bold>true</bold>
       </font>
      </property>
      <property name="text">
       <string>下限</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLineEdit" name="lineEdit_2">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QLabel" name="label_16">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>280</y>
     <width>141</width>
     <height>39</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <bold>true</bold>
    </font>
   </property>
   <property name="text">
    <string>超过上限时发出命令</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_17">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>410</y>
     <width>141</width>
     <height>39</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <bold>true</bold>
    </font>
   </property>
   <property name="text">
    <string>低过下限时发出命令</string>
   </property>
  </widget>
  <widget class="QScrollArea" name="scrollArea">
   <property name="geometry">
    <rect>
     <x>40</x>
     <y>320</y>
     <width>811</width>
     <height>87</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(85, 0, 0);</string>
   </property>
   <property name="widgetResizable">
    <bool>true</bool>
   </property>
   <widget class="QWidget" name="scrollAreaWidgetContents">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>809</width>
      <height>85</height>
     </rect>
    </property>
    <widget class="QTextEdit" name="textEdit">
     <property name="geometry">
      <rect>
       <x>13</x>
       <y>6</y>
       <width>781</width>
       <height>91</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgb(255, 255, 255);</string>
     </property>
    </widget>
   </widget>
  </widget>
  <widget class="QScrollArea" name="scrollArea_2">
   <property name="geometry">
    <rect>
     <x>40</x>
     <y>450</y>
     <width>811</width>
     <height>87</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(85, 0, 0);</string>
   </property>
   <property name="widgetResizable">
    <bool>true</bool>
   </property>
   <widget class="QWidget" name="scrollAreaWidgetContents_2">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>809</width>
      <height>85</height>
     </rect>
    </property>
    <widget class="QTextEdit" name="textEdit_2">
     <property name="geometry">
      <rect>
       <x>13</x>
       <y>6</y>
       <width>781</width>
       <height>91</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgb(255, 255, 255);</string>
     </property>
    </widget>
   </widget>
  </widget>
  <widget class="QPushButton" name="pushButton_RefreshPorts">
   <property name="geometry">
    <rect>
     <x>220</x>
     <y>10</y>
     <width>151</width>
     <height>28</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(255, 255, 127);</string>
   </property>
   <property name="text">
    <string>RefreshCOMPorts</string>
   </property>
  </widget>
  <widget class="QWidget" name="horizontalLayoutWidget_7">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>240</y>
     <width>821</width>
     <height>39</height>
    </rect>
   </property>
   <layout class="QHBoxLayout" name="horizontalLayout_7">
    <item>
     <widget class="QLabel" name="label_18">
      <property name="font">
       <font>
        <bold>true</bold>
       </font>
      </property>
      <property name="text">
       <string>发送测试数据</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLineEdit" name="lineEdit_485send_test">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QPushButton" name="pushButton_485sendtest">
      <property name="text">
       <string>send</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QLabel" name="label_4">
   <property name="geometry">
    <rect>
     <x>160</x>
     <y>590</y>
     <width>721</width>
     <height>19</height>
    </rect>
   </property>
   <property name="text">
    <string>請以     SENSOR185[Sensor_599  本頁面傳感器名稱 ]:   後面加要發的 HEX命令</string>
   </property>
  </widget>
  <widget class="QCheckBox" name="checkBox_HEX">
   <property name="geometry">
    <rect>
     <x>400</x>
     <y>10</y>
     <width>98</width>
     <height>23</height>
    </rect>
   </property>
   <property name="text">
    <string>HEX</string>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
