#include "sensor.h"
#include "ui_sensor.h"
#include <QtNetwork/QTcpSocket>
#include <QMessageBox>
#include <QPair>
#include <QDebug>


// 构造函数
Sensor::Sensor(QWidget *parent, int port)
    : QDialog(parent)
    , ui(new Ui::Sensor)
    , port(port)  // 初始化 port 成員變量
{
    ui->setupUi(this);




    tcpSocket = new QTcpSocket(this);

    connect(tcpSocket, &QTcpSocket::readyRead, this, &Sensor::onReadyRead);

    connect(ui->pushButton_Sensor_Connect, &QPushButton::clicked, this, &Sensor::on_pushButton_Sensor_Connect_clicked);

    connect(ui->pushButton_Sensor_SendMassage, &QPushButton::clicked, this, &Sensor::sendMassageToServer);

    // Set the port in the UI
    ui->lineEdit_Sensor_port->setText(QString::number(port));

    // Generate a unique name for the sensor
    sensorName = QString("Sensor_%1").arg(port);
    ui->lineEdit_3_sensorName->setText(sensorName);

    // 新增：當用戶輸入傳感器名稱時，發出信號
    connect(ui->lineEdit_3_sensorName, &QLineEdit::editingFinished, this, [this]() {
        sensorName = ui->lineEdit_3_sensorName->text();
        emit sensorCreated(sensorName, this);
        qDebug() << "Sensor name set to:" << sensorName;
    });

    // 立即發出 sensorCreated 信號
    emit sensorCreated(sensorName, this);
}



// 析构函数
Sensor::~Sensor()
{
    delete ui;
}

//加入名字識別
void Sensor::sendCommand(const QString& command)
{
    if (tcpSocket->state() == QAbstractSocket::ConnectedState) {
        tcpSocket->write(command.toUtf8());
    } else {
        qDebug() << "Sensor" << sensorName << "is not connected.";
    }
}

QString Sensor::getName() const
{
    return sensorName;
}

// TCP連接 Sensor.cpp
void Sensor::on_pushButton_Sensor_Connect_clicked()
{
    if (tcpSocket->state() == QAbstractSocket::UnconnectedState) {
        QString ip = ui->lineEdit_Sensor_Ip->text();
        int port = ui->lineEdit_Sensor_port->text().toInt();

        tcpSocket->connectToHost(ip, port);

        if (tcpSocket->waitForConnected(3000)) {
            ui->label_Sensor_Display_Connect->setStyleSheet("QLabel { color : green; }");
            ui->label_Sensor_Display_Connect->setText("连接成功");
        } else {
            ui->label_Sensor_Display_Connect->setStyleSheet("QLabel { color : red; }");
            ui->label_Sensor_Display_Connect->setText("连接失败：" + tcpSocket->errorString());
        }
    } else {
        qDebug() << "Already connected or connecting.";
    }
}
void Sensor::onReadyRead() {
    QByteArray data = tcpSocket->readAll();
    // 假设lineEdit_TcpBackMassae是您的QLineEdit的名称
    ui->textEdit_Sensor_BackMassae->setText(QString::fromUtf8(data));
}

void Sensor::sendMassageToServer() {
    // 从文本编辑框获取文本
    QString message = ui->textEdit_Sensor_SendMassae->toPlainText();
    // 将文本转换为UTF-8编码的字节数组，并发送
    tcpSocket->write(message.toUtf8());
}


void Sensor::sendHexCommand(const QByteArray& hexData)
{
    // 實現發送十六進制數據的邏輯
    // 這裡的具體實現取決於您的傳感器如何處理數據
    // 例如，如果您使用 QTcpSocket 進行通信：
    if (tcpSocket && tcpSocket->state() == QAbstractSocket::ConnectedState) {
        tcpSocket->write(hexData);
    } else {
        qDebug() << "Sensor not connected or socket not available";
    }
}
