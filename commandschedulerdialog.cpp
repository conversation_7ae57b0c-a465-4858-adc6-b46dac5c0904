#include "commandschedulerdialog.h"
#include "ui_commandschedulerdialog.h"
#include "mainwindow.h"

#include <QDateTimeEdit>
#include <QInputDialog>
#include <QTableWidgetItem>
#include <QVBoxLayout>
#include <QLabel>
#include <QDialogButtonBox>
#include <QMessageBox>

// 构造函数
CommandSchedulerDialog::CommandSchedulerDialog(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::CommandSchedulerDialog),
    mainWindow(qobject_cast<MainWindow*>(parent)) // 初始化 mainWindow
{
    ui->setupUi(this);

    // 设置表格的列数和标题
    ui->commandTableWidget->setColumnCount(2);
    ui->commandTableWidget->setHorizontalHeaderLabels(QStringList() << "命令" << "预定发送时间");

    // 连接按钮的信号和槽
    connect(ui->addCommandButton, &QPushButton::clicked, this, &CommandSchedulerDialog::on_addCommandButton_clicked);
    connect(ui->removeCommandButton, &QPushButton::clicked, this, &CommandSchedulerDialog::on_removeCommandButton_clicked);
    connect(ui->pushButton_adjusttime, &QPushButton::clicked, this, &CommandSchedulerDialog::adjustDelayTime);

    // 连接 MainWindow 的信号到 CommandSchedulerDialog 的槽
    if (mainWindow) {
        connect(mainWindow, &MainWindow::sendCommandsText, this, &CommandSchedulerDialog::handleCommandsTextRequest);
    }
}

// 析构函数
CommandSchedulerDialog::~CommandSchedulerDialog()
{
    delete ui;
}

// 添加命令按钮的槽函数
void CommandSchedulerDialog::on_addCommandButton_clicked()
{
    // 弹出对话框让用户输入命令
    bool ok;
    QString command = QInputDialog::getText(this, "添加命令", "命令：", QLineEdit::Normal, "", &ok);
    if (!ok || command.isEmpty())
        return;

    // 让用户选择预定发送时间
    QDateTime scheduledTime = QDateTime::currentDateTime();

    // 使用 QDateTimeEdit 让用户选择时间
    QDialog timeDialog(this);
    QVBoxLayout *layout = new QVBoxLayout(&timeDialog);
    QLabel *label = new QLabel("选择预定发送时间：", &timeDialog);
    QDateTimeEdit *dateTimeEdit = new QDateTimeEdit(QDateTime::currentDateTime(), &timeDialog);
    dateTimeEdit->setCalendarPopup(true); // 启用日历弹出窗口
    layout->addWidget(label);
    layout->addWidget(dateTimeEdit);
    QDialogButtonBox *buttonBox = new QDialogButtonBox(QDialogButtonBox::Ok | QDialogButtonBox::Cancel, &timeDialog);
    layout->addWidget(buttonBox);
    connect(buttonBox, &QDialogButtonBox::accepted, &timeDialog, &QDialog::accept);
    connect(buttonBox, &QDialogButtonBox::rejected, &timeDialog, &QDialog::reject);

    // 执行对话框并获取用户选择的时间
    if (timeDialog.exec() == QDialog::Accepted)
    {
        scheduledTime = dateTimeEdit->dateTime();
    }
    else
    {
        return;
    }

    // 在表格中添加新行
    int row = ui->commandTableWidget->rowCount();
    ui->commandTableWidget->insertRow(row);

    // 添加命令和预定发送时间到表格
    QTableWidgetItem *commandItem = new QTableWidgetItem(command);
    ui->commandTableWidget->setItem(row, 0, commandItem);

    QTableWidgetItem *timeItem = new QTableWidgetItem(scheduledTime.toString("yyyy-MM-dd hh:mm:ss"));
    ui->commandTableWidget->setItem(row, 1, timeItem);

    // 将预定发送时间存储在 item 的用户数据中
    timeItem->setData(Qt::UserRole, scheduledTime);
}

// 移除命令按钮的槽函数
void CommandSchedulerDialog::on_removeCommandButton_clicked()
{
    int row = ui->commandTableWidget->currentRow();
    if (row >= 0)
    {
        ui->commandTableWidget->removeRow(row);
    }
}

// 确认按钮的槽函数
void CommandSchedulerDialog::on_buttonBox_accepted()
{
    QList<QPair<QString, QDateTime>> commands;
    int rowCount = ui->commandTableWidget->rowCount();
    for (int i = 0; i < rowCount; ++i)
    {
        QString command = ui->commandTableWidget->item(i, 0)->text();
        QTableWidgetItem *timeItem = ui->commandTableWidget->item(i, 1);
        QDateTime scheduledTime = timeItem->data(Qt::UserRole).toDateTime();
        commands.append(qMakePair(command, scheduledTime));
    }
    // 发出信号，将命令列表传递给主窗口
    emit commandsScheduled(commands);
    accept();
}

// 处理来自 MainWindow 的命令文本请求的槽函数
void CommandSchedulerDialog::handleCommandsTextRequest() {
    if (mainWindow) {
        QString commandsText = mainWindow->getCommandsText();
        // 根据需求处理接收到的命令文本
        // 例如，显示在某个控件中或进行其他操作
        // 这里可以添加具体的处理逻辑
        // 例如：
        // ui->someTextEdit->setPlainText(commandsText);
    }
}

///////////////////////////////////adjusttime//////////////////////////////

void CommandSchedulerDialog::adjustDelayTime() {
    // 获取原始命令文本
    if (mainWindow) {
        QStringList commandLines = mainWindow->getCommandsText().split('\n');
        QList<int> originalDelays; // 存储原始延迟时间
        int totalOriginalDelay = 0; // 原始总延迟时间

        // 解析命令，提取延迟时间
        foreach (QString line, commandLines) {
            if (line.startsWith("延迟:")) {
                // 假设延迟命令格式为 "延迟:1000ms"
                QString delayStr = line.mid(3, line.length() - 5); // 提取数字部分，例如 "1000"
                int delay = delayStr.toInt();
                originalDelays.append(delay);
                totalOriginalDelay += delay;
            }
        }

        // 将原始总延迟时间显示到 lineEdit_realtime
        ui->lineEdit_realtime->setText(QString::number(totalOriginalDelay));

        // 获取用户输入的希望的总发送时间
        QString desiredTotalTimeStr = ui->lineEdit_hopetime->text();
        if (desiredTotalTimeStr.isEmpty()) {
            // 如果未输入希望时间，则直接返回
            QMessageBox::warning(this, "输入错误", "请输入希望的总发送时间。");
            return;
        }

        bool ok;
        int desiredTotalTime = desiredTotalTimeStr.toInt(&ok); // 转为整数
        if (!ok || desiredTotalTime <= 0) {
            // 输入无效，显示错误提示
            QMessageBox::warning(this, "输入错误", "请输入有效的希望总发送时间（正整数）。");
            return;
        }

        // 计算比例系数
        double ratio = static_cast<double>(desiredTotalTime) / totalOriginalDelay;

        // 调整延迟时间
        QList<int> newDelays;
        for (int delay : originalDelays) {
            int newDelay = static_cast<int>(delay * ratio);
            newDelays.append(newDelay);
        }

        // 更新命令中的延迟时间
        int delayIndex = 0;
        for (int i = 0; i < commandLines.size(); ++i) {
            QString line = commandLines[i];
            if (line.startsWith("延迟:")) {
                int newDelay = newDelays[delayIndex++];
                commandLines[i] = QString("延迟:%1ms").arg(newDelay);
            }
        }

        // 将更新后的命令显示到文本区域
        // 由于 `textEdit_Muti` 不在 `CommandSchedulerDialog` 中，通过 `MainWindow` 的 `setCommandsText` 方法更新
        mainWindow->setCommandsText(commandLines.join('\n'));

        // 计算新的总发送时间并更新到 lineEdit_realtime
        int newTotalTime = 0;
        for (int delay : newDelays) {
            newTotalTime += delay;
        }
        ui->lineEdit_realtime->setText(QString::number(newTotalTime));

        // 提示用户延迟时间已调整
        QMessageBox::information(this, "调整完成", "延迟时间已成功调整。");
    }
}
