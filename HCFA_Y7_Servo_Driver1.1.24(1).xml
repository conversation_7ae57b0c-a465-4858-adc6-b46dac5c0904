﻿<?xml version="1.0" encoding="utf-8"?>
<!-- edited with XMLSpy v2013 (http://www.altova.com) by  () -->
<EtherCATInfo xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="EtherCATInfo.xsd" Version="1.0">
  <Vendor>
    <Id>#x000116c7</Id>
    <Name>HCFA Co.,Ltd</Name>
    <ImageData16x14>424dd8020000000000003600000028000000100000000e00000001001800000000000000000000000000000000000000000000000000fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffefeffe1efdef2f7f0f2f8f1eaf3e9fafcf9cee5c9d6e8d2d8ead5f9fbf8fffffffbfcfbdaebd5c3ddbcecf6ecfffffffffeff8fc2878fc388b8dab07bbe7275b86790c386b5d7ac56a549b7d9b2eff6ed9bc99772b46761aa4e90c485ffffffffffffa5cf9e3b9a2b6daa5886a76b359622dff0ddffffff87be7c449c32aad1a1bddcba9fcd9893c58658a646f7faf6ffffffd6e8d05cac4dbbd6b5c3918c81a86685c37e89c17db8d9b14ba0387dba6fb6d8ada6d09d77b769489d35f3f9f3fffffffbfdfbd3e7ceeff9f0f0e5e3fff7fbc2e0bd9ac890dfeedbd2e7cea4ce9bc3dfbcbcdbb6aad2a1cce4c9ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
		</ImageData16x14>
  </Vendor>
  <Descriptions>
    <Groups>
      <Group SortOrder="0">
        <Type>AC Servo Driver</Type>
        <Name LcId="1033">HCFA-Y7-DRIVER</Name>
        <ImageData16x14>424dd8020000000000003600000028000000100000000e00000001001800000000000000000000000000000000000000000000000000fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffefeffe1efdef2f7f0f2f8f1eaf3e9fafcf9cee5c9d6e8d2d8ead5f9fbf8fffffffbfcfbdaebd5c3ddbcecf6ecfffffffffeff8fc2878fc388b8dab07bbe7275b86790c386b5d7ac56a549b7d9b2eff6ed9bc99772b46761aa4e90c485ffffffffffffa5cf9e3b9a2b6daa5886a76b359622dff0ddffffff87be7c449c32aad1a1bddcba9fcd9893c58658a646f7faf6ffffffd6e8d05cac4dbbd6b5c3918c81a86685c37e89c17db8d9b14ba0387dba6fb6d8ada6d09d77b769489d35f3f9f3fffffffbfdfbd3e7ceeff9f0f0e5e3fff7fbc2e0bd9ac890dfeedbd2e7cea4ce9bc3dfbcbcdbb6aad2a1cce4c9ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
				</ImageData16x14>
      </Group>
    </Groups>
    <Devices>
      <Device Physics="YY">
        <Type ProductCode="#x007E0402" RevisionNo="#x00000001">HCFA Y7 Servo Driver</Type>
        <Name LcId="1033">HCFA Y7 Servo Driver</Name>
        <Info>
          <StateMachine>
            <Timeout>
              <PreopTimeout>2000</PreopTimeout>
              <SafeopOpTimeout>9000</SafeopOpTimeout>
              <BackToInitTimeout>5000</BackToInitTimeout>
              <BackToSafeopTimeout>200</BackToSafeopTimeout>
            </Timeout>
          </StateMachine>
          <Mailbox>
            <Timeout>
              <RequestTimeout>100</RequestTimeout>
              <ResponseTimeout>4000</ResponseTimeout>
            </Timeout>
          </Mailbox>
        </Info>
        <GroupType>AC Servo Driver</GroupType>
        <Profile>
          <ChannelInfo>
            <ProfileNo>402</ProfileNo>
          </ChannelInfo>
          <Dictionary>
            <DataTypes>
              <DataType>
                <!--Std type (see ETG.1020)-->
                <Name>BIT2</Name>
                <BitSize>2</BitSize>
              </DataType>
              <DataType>
                <!--Std type (see ETG.1020)-->
                <Name>BOOL</Name>
                <BitSize>1</BitSize>
              </DataType>
              <DataType>
                <!--Std type (see ETG.1020)-->
                <Name>DINT</Name>
                <BitSize>32</BitSize>
              </DataType>
              <DataType>
                <!--Std type (see ETG.1020)-->
                <Name>INT</Name>
                <BitSize>16</BitSize>
              </DataType>
              <DataType>
                <!--Std type (see ETG.1020)-->
                <Name>UDINT</Name>
                <BitSize>32</BitSize>
              </DataType>
              <DataType>
                <!--Std type (see ETG.1020)-->
                <Name>UINT</Name>
                <BitSize>16</BitSize>
              </DataType>
              <DataType>
                <!--Std type (see ETG.1020)-->
                <Name>SINT</Name>
                <BitSize>8</BitSize>
              </DataType>
              <DataType>
                <!--Std type (see ETG.1020)-->
                <Name>USINT</Name>
                <BitSize>8</BitSize>
              </DataType>
              <DataType>
                <!--Std type (see ETG.1020)-->
                <Name>ARRAY [0..3] OF BYTE</Name>
                <BaseType>USINT</BaseType>
                <BitSize>32</BitSize>
                <ArrayInfo>
                  <LBound>0</LBound>
                  <Elements>4</Elements>
                </ArrayInfo>
              </DataType>
              <DataType>
                <!--Device Name (0x1008) type-->
                <Name>STRING(7)</Name>
                <BitSize>56</BitSize>
              </DataType>
              <DataType>
                <!--Hardware version string (0x1009)-->
                <Name>STRING(3)</Name>
                <BitSize>24</BitSize>
              </DataType>
              <DataType>
                <!--Stroe Parameters-->
                <Name>DT1010</Name>
                <BitSize>48</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of Entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Store Parameters</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <!--Ident object (0x1018) type-->
                <Name>DT1018</Name>
                <BitSize>144</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of Entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Vendor ID</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Product code</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>3</SubIdx>
                  <Name>Revision</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>80</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>4</SubIdx>
                  <Name>Serial number</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>112</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT1C00ARR</Name>
                <BaseType>USINT</BaseType>
                <BitSize>32</BitSize>
                <ArrayInfo>
                  <LBound>1</LBound>
                  <Elements>4</Elements>
                </ArrayInfo>
              </DataType>
              <DataType>
                <Name>DT1C00</Name>
                <BitSize>48</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of Entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <Name>Elements</Name>
                  <Type>DT1C00ARR</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <!--Error Setting object (0x10F1) type-->
                <Name>DT10F1</Name>
                <BitSize>64</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of Entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Local Error Reaction</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Sync Error Counter Limit</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <!--Datatype for SM2(Output) Synchronisation  Parameter-->
                <Name>DT1C32</Name>
                <BitSize>488</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of Entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Synchronization Type</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access WriteRestrictions="PreOP">rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Cycle Time</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>32</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>4</SubIdx>
                  <Name>Synchronization Types supported</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>96</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>5</SubIdx>
                  <Name>Minimum Cycle Time</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>112</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>6</SubIdx>
                  <Name>Calc and Copy Time</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>144</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>8</SubIdx>
                  <Name>Get Cycle Time</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>208</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>c</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>9</SubIdx>
                  <Name>Delay Time</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>224</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>c</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>10</SubIdx>
                  <Name>Sync0 Cycle Time</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>256</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>11</SubIdx>
                  <Name>SM-Event Missed</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>288</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>c</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>12</SubIdx>
                  <Name>Cycle Exceeded Counter</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>304</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>c</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>32</SubIdx>
                  <Name>Sync Error</Name>
                  <Type>BOOL</Type>
                  <BitSize>1</BitSize>
                  <BitOffs>480</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>c</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <!--Datatype for SM3(Input) Synchronisation  Parameter-->
                <Name>DT1C33</Name>
                <BitSize>488</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of Entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Synchronization Type</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access WriteRestrictions="PreOP">rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Cycle Time</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>32</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>4</SubIdx>
                  <Name>Synchronization Types supported</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>96</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>5</SubIdx>
                  <Name>Minimum Cycle Time</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>112</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>6</SubIdx>
                  <Name>Calc and Copy Time</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>144</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>8</SubIdx>
                  <Name>Get Cycle Time</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>208</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>c</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>9</SubIdx>
                  <Name>Delay Time</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>224</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>c</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>10</SubIdx>
                  <Name>Sync0 Cycle Time</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>256</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>11</SubIdx>
                  <Name>SM-Event Missed</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>288</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>c</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>12</SubIdx>
                  <Name>Cycle Exceeded Counter</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>304</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>c</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>32</SubIdx>
                  <Name>Sync Error</Name>
                  <Type>BOOL</Type>
                  <BitSize>1</BitSize>
                  <BitOffs>480</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>c</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT0800EN03</Name>
                <BaseType>USINT</BaseType>
                <BitSize>3</BitSize>
                <EnumInfo>
                  <Text>Signed presentation</Text>
                  <Enum>0</Enum>
                </EnumInfo>
                <EnumInfo>
                  <Text>Unsigned presentation</Text>
                  <Enum>1</Enum>
                </EnumInfo>
              </DataType>
              <DataType>
                <Name>DT160x</Name>
                <BitSize>304</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of Entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Mapping Entry 1</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Mapping Entry 2</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>3</SubIdx>
                  <Name>Mapping Entry 3</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>80</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>4</SubIdx>
                  <Name>Mapping Entry 4</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>112</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>5</SubIdx>
                  <Name>Mapping Entry 5</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>144</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>6</SubIdx>
                  <Name>Mapping Entry 6</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>176</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>7</SubIdx>
                  <Name>Mapping Entry 7</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>208</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>8</SubIdx>
                  <Name>Mapping Entry 8</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>240</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>9</SubIdx>
                  <Name>Mapping Entry 9</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>272</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT1600</Name>
                <BitSize>400</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of Entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access WriteRestrictions="PreOP">rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Mapping Entry 1</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Mapping Entry 2</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>3</SubIdx>
                  <Name>Mapping Entry 3</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>80</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>4</SubIdx>
                  <Name>Mapping Entry 4</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>112</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>5</SubIdx>
                  <Name>Mapping Entry 5</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>144</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>6</SubIdx>
                  <Name>Mapping Entry 6</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>176</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>7</SubIdx>
                  <Name>Mapping Entry 7</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>208</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>8</SubIdx>
                  <Name>Mapping Entry 8</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>240</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>9</SubIdx>
                  <Name>Mapping Entry 9</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>272</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>10</SubIdx>
                  <Name>Mapping Entry 10</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>304</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>11</SubIdx>
                  <Name>Mapping Entry 11</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>336</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>12</SubIdx>
                  <Name>Mapping Entry 12</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>368</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT1A00</Name>
                <BitSize>400</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of Entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access WriteRestrictions="PreOP">rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Mapping Entry 1</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Mapping Entry 2</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>3</SubIdx>
                  <Name>Mapping Entry 3</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>80</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>4</SubIdx>
                  <Name>Mapping Entry 4</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>112</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>5</SubIdx>
                  <Name>Mapping Entry 5</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>144</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>6</SubIdx>
                  <Name>Mapping Entry 6</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>176</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>7</SubIdx>
                  <Name>Mapping Entry 7</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>208</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>8</SubIdx>
                  <Name>Mapping Entry 8</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>240</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>9</SubIdx>
                  <Name>Mapping Entry 9</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>272</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>10</SubIdx>
                  <Name>Mapping Entry 10</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>304</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>11</SubIdx>
                  <Name>Mapping Entry 11</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>336</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>12</SubIdx>
                  <Name>Mapping Entry 12</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>368</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT1802</Name>
                <BitSize>24</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of Entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>7</SubIdx>
                  <Name>TxPDOState</Name>
                  <Type>BOOL</Type>
                  <BitSize>1</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <PdoMapping>T</PdoMapping>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>9</SubIdx>
                  <Name>TxPDO Toggle</Name>
                  <Type>BOOL</Type>
                  <BitSize>1</BitSize>
                  <BitOffs>17</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <PdoMapping>T</PdoMapping>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT1A0x</Name>
                <BitSize>368</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of Entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Mapping Entry 1</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Mapping Entry 2</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>3</SubIdx>
                  <Name>Mapping Entry 3</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>80</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>4</SubIdx>
                  <Name>Mapping Entry 4</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>112</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>5</SubIdx>
                  <Name>Mapping Entry 5</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>144</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>6</SubIdx>
                  <Name>Mapping Entry 6</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>176</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>7</SubIdx>
                  <Name>Mapping Entry 7</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>208</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>8</SubIdx>
                  <Name>Mapping Entry 8</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>240</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>9</SubIdx>
                  <Name>Mapping Entry 9</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>272</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>10</SubIdx>
                  <Name>Mapping Entry 10</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>304</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>11</SubIdx>
                  <Name>Mapping Entry 11</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>336</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT1C12ARR</Name>
                <BaseType>UINT</BaseType>
                <BitSize>16</BitSize>
                <ArrayInfo>
                  <LBound>1</LBound>
                  <Elements>1</Elements>
                </ArrayInfo>
              </DataType>
              <DataType>
                <Name>DT1C12</Name>
                <BitSize>32</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of Entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access WriteRestrictions="PreOP">rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <Name>Elements</Name>
                  <Type>DT1C12ARR</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access WriteRestrictions="PreOP">rw</Access>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT1C13ARR</Name>
                <BaseType>UINT</BaseType>
                <BitSize>16</BitSize>
                <ArrayInfo>
                  <LBound>1</LBound>
                  <Elements>1</Elements>
                </ArrayInfo>
              </DataType>
              <DataType>
                <Name>DT1C13</Name>
                <BitSize>32</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of Entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access WriteRestrictions="PreOP">rw</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <Name>Elements</Name>
                  <Type>DT1C13ARR</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access WriteRestrictions="PreOP">rw</Access>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT607D</Name>
                <BitSize>80</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of Entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Min position limit</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <PdoMapping>R</PdoMapping>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Max position limit</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <PdoMapping>R</PdoMapping>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT608F</Name>
                <BitSize>80</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of Entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Encoder increments</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <PdoMapping>T</PdoMapping>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Motor revolutions</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <PdoMapping>T</PdoMapping>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT6091</Name>
                <BitSize>80</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of Entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Numerator</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <PdoMapping>R</PdoMapping>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Denominator</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <PdoMapping>R</PdoMapping>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT6092</Name>
                <BitSize>80</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of Entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Numerator</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <PdoMapping>R</PdoMapping>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Denominator</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <PdoMapping>R</PdoMapping>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT6093</Name>
                <BitSize>80</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of Entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Position factor Numerator</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <PdoMapping>R</PdoMapping>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Position factor Feed constant</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <PdoMapping>R</PdoMapping>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT6094</Name>
                <BitSize>80</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of Entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Velocity encoder factor Numerator</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <PdoMapping>R</PdoMapping>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Velocity encoder factor Divisor</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <PdoMapping>R</PdoMapping>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT6095</Name>
                <BitSize>80</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of Entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Velocity factor1 Numerator</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <PdoMapping>R</PdoMapping>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Velocity factor1 Divisor</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <PdoMapping>R</PdoMapping>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT6097</Name>
                <BitSize>80</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of Entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Acceleration factor:Numerator </Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <PdoMapping>R</PdoMapping>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Acceleration factor:Divisor</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <PdoMapping>R</PdoMapping>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT6099</Name>
                <BitSize>80</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of Entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Speed during search for switch</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <PdoMapping>R</PdoMapping>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Speed during search for zero</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <PdoMapping>R</PdoMapping>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT60C1</Name>
                <BitSize>48</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of Entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Interpolation data record:x1</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <PdoMapping>R</PdoMapping>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT60C2</Name>
                <BitSize>32</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of Entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>ip time units </Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <PdoMapping>R</PdoMapping>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>ip time index </Name>
                  <Type>SINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>24</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <PdoMapping>R</PdoMapping>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT60FE</Name>
                <BitSize>80</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of Entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Physical outputs</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <PdoMapping>R</PdoMapping>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Bitmask</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <PdoMapping>R</PdoMapping>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT60E3</Name>
                <BitSize>512</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>SubIndex 000</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Support homing method 1</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Support homing method 2</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>32</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>3</SubIdx>
                  <Name>Support homing method 3</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>4</SubIdx>
                  <Name>Support homing method 4</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>64</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>5</SubIdx>
                  <Name>Support homing method 5</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>80</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>6</SubIdx>
                  <Name>Support homing method 6</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>96</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>7</SubIdx>
                  <Name>Support homing method 7</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>112</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>8</SubIdx>
                  <Name>Support homing method 8</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>128</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>9</SubIdx>
                  <Name>Support homing method 9</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>144</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>10</SubIdx>
                  <Name>Support homing method 10</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>160</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>11</SubIdx>
                  <Name>Support homing method 11</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>176</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>12</SubIdx>
                  <Name>Support homing method 12</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>192</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>13</SubIdx>
                  <Name>Support homing method 13</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>208</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>14</SubIdx>
                  <Name>Support homing method 14</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>224</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>15</SubIdx>
                  <Name>Support homing method 15</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>240</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>16</SubIdx>
                  <Name>Support homing method 16</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>256</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>17</SubIdx>
                  <Name>Support homing method 17</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>272</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>18</SubIdx>
                  <Name>Support homing method 18</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>288</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>19</SubIdx>
                  <Name>Support homing method 19</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>304</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>20</SubIdx>
                  <Name>Support homing method 20</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>320</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>21</SubIdx>
                  <Name>Support homing method 21</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>336</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>22</SubIdx>
                  <Name>Support homing method 22</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>352</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>23</SubIdx>
                  <Name>Support homing method 23</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>368</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>24</SubIdx>
                  <Name>Support homing method 24</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>384</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>25</SubIdx>
                  <Name>Support homing method 25</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>400</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>26</SubIdx>
                  <Name>Support homing method 26</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>416</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>27</SubIdx>
                  <Name>Support homing method 27</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>432</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>28</SubIdx>
                  <Name>Support homing method 28</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>448</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>29</SubIdx>
                  <Name>Support homing method 29</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>464</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>30</SubIdx>
                  <Name>Support homing method 30</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>480</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>31</SubIdx>
                  <Name>Support homing method 31</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>496</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                  </Flags>
                </SubItem>
              </DataType>
            </DataTypes>
            <Objects>
              <Object>
                <Index>#x1000</Index>
                <Name>Device type</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Info>
                  <DefaultData>00020192</DefaultData>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>m</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1001</Index>
                <Name>Error register</Name>
                <Type>USINT</Type>
                <BitSize>8</BitSize>
                <Info>
                  <DefaultData>00</DefaultData>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1008</Index>
                <Name>Device name</Name>
                <Type>STRING(7)</Type>
                <BitSize>56</BitSize>
                <Info>
                  <DefaultString>Y7-ECAT</DefaultString>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1009</Index>
                <Name>Hardware version</Name>
                <Type>STRING(3)</Type>
                <BitSize>24</BitSize>
                <Info>
                  <DefaultString>1.0</DefaultString>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x100a</Index>
                <Name>Software version</Name>
                <Type>STRING(3)</Type>
                <BitSize>24</BitSize>
                <Info>
                  <DefaultString>1.0</DefaultString>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1c00</Index>
                <Name>Sync manager type</Name>
                <Type>DT1C00</Type>
                <BitSize>48</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of Entries</Name>
                    <Info>
                      <DefaultData>04</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>SubIndex 001</Name>
                    <Info>
                      <DefaultData>01</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>SubIndex 002</Name>
                    <Info>
                      <DefaultData>02</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>SubIndex 003</Name>
                    <Info>
                      <DefaultData>03</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>SubIndex 004</Name>
                    <Info>
                      <DefaultData>04</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1018</Index>
                <Name>Identity</Name>
                <Type>DT1018</Type>
                <BitSize>144</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of Entries</Name>
                    <Info>
                      <DefaultData>04</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Vendor ID</Name>
                    <Info>
                      <DefaultData>c7160100</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Product code</Name>
                    <Info>
                      <DefaultData>007E0402</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Revision</Name>
                    <Info>
                      <DefaultData>00000001</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Serial number</Name>
                    <Info>
                      <DefaultData>00000001</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x10F1</Index>
                <Name>Error Settings</Name>
                <Type>DT10F1</Type>
                <BitSize>64</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of Entries</Name>
                    <Info>
                      <DefaultData>02</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Local Error Reaction</Name>
                    <Info>
                      <DefaultData>01000000</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Sync Error Counter Limit</Name>
                    <Info>
                      <DefaultData>0400</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1c12</Index>
                <Name>RxPDO assign</Name>
                <Type>DT1C12</Type>
                <BitSize>32</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of Entries</Name>
                    <Info>
                      <DefaultData>02</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>SubIndex 001</Name>
                    <Info>
                      <DefaultData>0116</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access WriteRestrictions="PreOP">rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1c13</Index>
                <Name>TxPDO assign</Name>
                <Type>DT1C13</Type>
                <BitSize>32</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of Entries</Name>
                    <Info>
                      <DefaultData>02</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>SubIndex 001</Name>
                    <Info>
                      <DefaultData>001a</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access WriteRestrictions="PreOP">rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1c32</Index>
                <Name>SM output parameter</Name>
                <Type>DT1C32</Type>
                <BitSize>488</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of Entries</Name>
                    <Info>
                      <DefaultData>20</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Synchronization Type</Name>
                    <Info>
                      <DefaultData>0001</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Cycle Time</Name>
                    <Info>
                      <DefaultData>00000000</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Synchronization Types supported</Name>
                    <Info>
                      <DefaultData>8007</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Minimum Cycle Time</Name>
                    <Info>
                      <DefaultData>00000000</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Calc and Copy Time</Name>
                    <Info>
                      <DefaultData>00000000</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Get Cycle Time</Name>
                    <Info>
                      <DefaultData>0000</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Delay Time</Name>
                    <Info>
                      <DefaultData>00000000</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Sync0 Cycle Time</Name>
                    <Info>
                      <DefaultData>00000000</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>SM-Event Missed</Name>
                    <Info>
                      <DefaultData>0000</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Cycle Exceeded Counter</Name>
                    <Info>
                      <DefaultData>0000</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Sync Error</Name>
                    <Info>
                      <DefaultData>00</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1c33</Index>
                <Name>SM input parameter</Name>
                <Type>DT1C33</Type>
                <BitSize>488</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of Entries</Name>
                    <Info>
                      <DefaultData>20</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Synchronization Type</Name>
                    <Info>
                      <DefaultData>2200</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Cycle Time</Name>
                    <Info>
                      <DefaultData>00000000</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Synchronization Types supported</Name>
                    <Info>
                      <DefaultData>0780</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Minimum Cycle Time</Name>
                    <Info>
                      <DefaultData>00000000</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Calc and Copy Time</Name>
                    <Info>
                      <DefaultData>00000000</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Get Cycle Time</Name>
                    <Info>
                      <DefaultData>0000</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Delay Time</Name>
                    <Info>
                      <DefaultData>00000000</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Sync0 Cycle Time</Name>
                    <Info>
                      <DefaultData>00000000</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>SM-Event Missed</Name>
                    <Info>
                      <DefaultData>0000</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Cycle Exceeded Counter</Name>
                    <Info>
                      <DefaultData>0000</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Sync Error</Name>
                    <Info>
                      <DefaultData>00</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1600</Index>
                <Name>1st RxPDO-Mapping</Name>
                <Type>DT1600</Type>
                <BitSize>400</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of Entries</Name>
                    <Info>
                      <DefaultValue>4</DefaultValue>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 1</Name>
                    <Info>
                      <DefaultData>10004060</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 2</Name>
                    <Info>
                      <DefaultData>08006060</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 3</Name>
                    <Info>
                      <DefaultData>20007A60</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 4</Name>
                    <Info>
                      <DefaultData>1000B860</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1601</Index>
                <Name>2nd RxPDO-Mapping</Name>
                <Type>DT1600</Type>
                <BitSize>400</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of Entries</Name>
                    <Info>
                      <DefaultValue>7</DefaultValue>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 1</Name>
                    <Info>
                      <DefaultData>10004060</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 2</Name>
                    <Info>
                      <DefaultData>08006060</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 3</Name>
                    <Info>
                      <DefaultData>10007160</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 4</Name>
                    <Info>
                      <DefaultData>20007A60</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 5</Name>
                    <Info>
                      <DefaultData>20008060</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 6</Name>
                    <Info>
                      <DefaultData>2000FF60</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1602</Index>
                <Name>3rd RxPDO-Mapping</Name>
                <Type>DT1600</Type>
                <BitSize>400</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of Entries</Name>
                    <Info>
                      <DefaultValue>6</DefaultValue>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 1</Name>
                    <Info>
                      <DefaultData>10004060</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 2</Name>
                    <Info>
                      <DefaultData>08006060</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 3</Name>
                    <Info>
                      <DefaultData>10007260</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 4</Name>
                    <Info>
                      <DefaultData>20007A60</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 5</Name>
                    <Info>
                      <DefaultData>1000B860</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 6</Name>
                    <Info>
                      <DefaultData>2000FF60</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1603</Index>
                <Name>4th RxPDO-Mapping</Name>
                <Type>DT1600</Type>
                <BitSize>400</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of Entries</Name>
                    <Info>
                      <DefaultValue>8</DefaultValue>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 1</Name>
                    <Info>
                      <DefaultData>10004060</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 2</Name>
                    <Info>
                      <DefaultData>08006060</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 3</Name>
                    <Info>
                      <DefaultData>10007160</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 4</Name>
                    <Info>
                      <DefaultData>10007260</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 5</Name>
                    <Info>
                      <DefaultData>20007A60</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 6</Name>
                    <Info>
                      <DefaultData>20008060</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 7</Name>
                    <Info>
                      <DefaultData>1000B860</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 8</Name>
                    <Info>
                      <DefaultData>2000FF60</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1604</Index>
                <Name>5th RxPDO-Mapping</Name>
                <Type>DT1600</Type>
                <BitSize>400</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of Entries</Name>
                    <Info>
                      <DefaultValue>8</DefaultValue>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 1</Name>
                    <Info>
                      <DefaultData>10004060</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 2</Name>
                    <Info>
                      <DefaultData>20007A60</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 3</Name>
                    <Info>
                      <DefaultData>2000FF60</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 4</Name>
                    <Info>
                      <DefaultData>10007160</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 5</Name>
                    <Info>
                      <DefaultData>08006060</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 6</Name>
                    <Info>
                      <DefaultData>1000B860</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 7</Name>
                    <Info>
                      <DefaultData>1000E060</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 8</Name>
                    <Info>
                      <DefaultData>1000E160</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1a00</Index>
                <Name>1st TxPDO-Mapping</Name>
                <Type>DT1A00</Type>
                <BitSize>400</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of Entries</Name>
                    <Info>
                      <DefaultValue>8</DefaultValue>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 1</Name>
                    <Info>
                      <DefaultData>10003F60</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 2</Name>
                    <Info>
                      <DefaultData>10004160</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 3</Name>
                    <Info>
                      <DefaultData>20006460</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 4</Name>
                    <Info>
                      <DefaultData>08006160</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 5</Name>
                    <Info>
                      <DefaultData>1000B960</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 6</Name>
                    <Info>
                      <DefaultData>2000BA60</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 7</Name>
                    <Info>
                      <DefaultData>2000F460</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 8</Name>
                    <Info>
                      <DefaultData>2000FD60</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1a01</Index>
                <Name>2nd TxPDO-Mapping</Name>
                <Type>DT1A00</Type>
                <BitSize>400</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of Entries</Name>
                    <Info>
                      <DefaultValue>10</DefaultValue>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 1</Name>
                    <Info>
                      <DefaultData>10003F60</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 2</Name>
                    <Info>
                      <DefaultData>10004160</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 3</Name>
                    <Info>
                      <DefaultData>08006160</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 4</Name>
                    <Info>
                      <DefaultData>20006460</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 5</Name>
                    <Info>
                      <DefaultData>20006C60</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 6</Name>
                    <Info>
                      <DefaultData>10007760</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 7</Name>
                    <Info>
                      <DefaultData>1000B960</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 8</Name>
                    <Info>
                      <DefaultData>2000BA60</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 9</Name>
                    <Info>
                      <DefaultData>2000BB60</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 10</Name>
                    <Info>
                      <DefaultData>2000FD60</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1a02</Index>
                <Name>3rd TxPDO-Mapping</Name>
                <Type>DT1A00</Type>
                <BitSize>400</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of Entries</Name>
                    <Info>
                      <DefaultValue>9</DefaultValue>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 1</Name>
                    <Info>
                      <DefaultData>10003F60</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 2</Name>
                    <Info>
                      <DefaultData>10004160</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 3</Name>
                    <Info>
                      <DefaultData>08006160</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 4</Name>
                    <Info>
                      <DefaultData>20006460</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 5</Name>
                    <Info>
                      <DefaultData>20006C60</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 6</Name>
                    <Info>
                      <DefaultData>10007760</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 7</Name>
                    <Info>
                      <DefaultData>1000B960</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 8</Name>
                    <Info>
                      <DefaultData>2000BA60</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 9</Name>
                    <Info>
                      <DefaultData>2000FD60</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1a03</Index>
                <Name>4th TxPDO-Mapping</Name>
                <Type>DT1A00</Type>
                <BitSize>400</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of Entries</Name>
                    <Info>
                      <DefaultValue>9</DefaultValue>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 1</Name>
                    <Info>
                      <DefaultData>10003F60</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 2</Name>
                    <Info>
                      <DefaultData>10004160</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 3</Name>
                    <Info>
                      <DefaultData>08006160</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 4</Name>
                    <Info>
                      <DefaultData>20006460</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 5</Name>
                    <Info>
                      <DefaultData>20006C60</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 6</Name>
                    <Info>
                      <DefaultData>10007760</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 7</Name>
                    <Info>
                      <DefaultData>1000B960</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 8</Name>
                    <Info>
                      <DefaultData>2000BA60</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 9</Name>
                    <Info>
                      <DefaultData>2000FD60</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1a04</Index>
                <Name>5th TxPDO-Mapping</Name>
                <Type>DT1A00</Type>
                <BitSize>400</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of Entries</Name>
                    <Info>
                      <DefaultValue>11</DefaultValue>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 1</Name>
                    <Info>
                      <DefaultData>10003F60</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 2</Name>
                    <Info>
                      <DefaultData>10004160</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 3</Name>
                    <Info>
                      <DefaultData>08006160</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 4</Name>
                    <Info>
                      <DefaultData>20006460</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 5</Name>
                    <Info>
                      <DefaultData>20006C60</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 6</Name>
                    <Info>
                      <DefaultData>10007760</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 7</Name>
                    <Info>
                      <DefaultData>2000F460</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 8</Name>
                    <Info>
                      <DefaultData>1000B960</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 9</Name>
                    <Info>
                      <DefaultData>2000BA60</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 10</Name>
                    <Info>
                      <DefaultData>2000BC60</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Mapping Entry 11</Name>
                    <Info>
                      <DefaultData>2000FD60</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x2000</Index>
                <Name>Basic Function Select Switch 0</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Info>
                  <DefaultData>0000</DefaultData>
                </Info>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2001</Index>
                <Name>Application Function Select Switch 1</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Info>
                  <DefaultData>0000</DefaultData>
                </Info>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2002</Index>
                <Name>Application Function Select Switch 2</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2006</Index>
                <Name>Application Function Select Switch 6</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2007</Index>
                <Name>Application Function Select Switch 7</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2008</Index>
                <Name>Application Function Select Switch 8</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2009</Index>
                <Name>Application Function Select Switch 9</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x200A</Index>
                <Name>Application Function Select Switch A</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x200B</Index>
                <Name>Application Function Select Switch B</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x200C</Index>
                <Name>Application Function Select Switch C</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x200D</Index>
                <Name>Application Function Select Switch D</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x200E</Index>
                <Name>Application Function Select Switch E</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x200F</Index>
                <Name>Application Function Select Switch F</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2010</Index>
                <Name>Axis address for USB communication</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2021</Index>
                <Name>DC Bus Switch</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2022</Index>
                <Name>Overtravel Release Method</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2040</Index>
                <Name>Mode Switch</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2080</Index>
                <Name>Application Function Select Switch 80</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2081</Index>
                <Name>Application Function Select Switch 81</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2100</Index>
                <Name>Speed Loop Gain</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2101</Index>
                <Name>Speed Loop Integral Time Constant</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2102</Index>
                <Name>Position Loop Gain</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2103</Index>
                <Name>Moment of Inertia Ratio</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Info>
                  <MinValue>#x0000</MinValue>
                  <MaxValue>#x4E20</MaxValue>
                  <DefaultValue>#x0064</DefaultValue>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>m</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x2104</Index>
                <Name>2nd Speed Loop Gain</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2105</Index>
                <Name>2nd Speed Loop Integral Time Constant</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2106</Index>
                <Name>2nd Position Loop Gain</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2109</Index>
                <Name>Feedforward Gain</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x210A</Index>
                <Name>Feedforward Filter Time Constant</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x210B</Index>
                <Name>Application Function for Gain Select Switch</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x210C</Index>
                <Name>Mode Switch (torque reference)</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x210D</Index>
                <Name>Mode Switch (speed reference)</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x210E</Index>
                <Name>Mode Switch (acceleration)</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x210F</Index>
                <Name>Mode Switch (position error pulse)</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x211F</Index>
                <Name>Position Integral Time Constant</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2121</Index>
                <Name>Friction Compensation Gain</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2122</Index>
                <Name>2nd Gain for Friction Compensation</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2123</Index>
                <Name>Friction Compensation Coefficient</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2124</Index>
                <Name>Friction Compensation Frequency Correction</Name>
                <Type>INT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2125</Index>
                <Name>Friction Compensation Gain Correction</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2131</Index>
                <Name>Gain Switching Time 1</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2132</Index>
                <Name>Gain Switching Time 2</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2135</Index>
                <Name>Gain Switching Waiting Time 1</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2136</Index>
                <Name>Gain Switching Waiting Time 2</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2139</Index>
                <Name>Automatic Gain Changeover Related Switch 1</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x213D</Index>
                <Name>Current Gain Level</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2140</Index>
                <Name>Model Following Control Related Switch</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2141</Index>
                <Name>Model Following Control Gain</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2142</Index>
                <Name>Model Following Control Gain Compensation</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2143</Index>
                <Name>Model Following Control Bias(Forward Direction)</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2144</Index>
                <Name>Model Following Control Bias(Reverse Direction)</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2145</Index>
                <Name>Vibration Suppression 1 Frequency A</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2146</Index>
                <Name>Vibration Suppression 1 Frequency B</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2147</Index>
                <Name>Model Following Control Speed Feedforward Compensation</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2148</Index>
                <Name>2nd Model Following Control Gain</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2149</Index>
                <Name>2nd Model Following Control Gain Compensation</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x214A</Index>
                <Name>Vibration Suppression 2 Frequency</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x214B</Index>
                <Name>Vibration Suppression 2 Compensation</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x214F</Index>
                <Name>Selection related to control</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2160</Index>
                <Name>Anti-Resonance Control Related Switch</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2161</Index>
                <Name>Anti-Resonance Frequency</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2162</Index>
                <Name>Anti-Resonance Gain Compensation</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2163</Index>
                <Name>Anti-Resonance Damping Gain</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2164</Index>
                <Name>Anti-Resonance Filter Time Constant 1 Compensation</Name>
                <Type>INT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2165</Index>
                <Name>Anti-Resonance Filter Time Constant 2 Compensation</Name>
                <Type>INT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2166</Index>
                <Name>A type vibration control dumping gain 2</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2170</Index>
                <Name>Tuning-less Function Related Switch</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2181</Index>
                <Name>Mode Switch (speed reference for Linear type)</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2182</Index>
                <Name>Mode Switch (acceleration for Linear type)</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2190</Index>
                <Name>PN190</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2200</Index>
                <Name>PN200</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2205</Index>
                <Name>Multi-turn Limit Setting</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2207</Index>
                <Name>Position Control Function Switch</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x220A</Index>
                <Name>Number of External Scale Pitch</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x220E</Index>
                <Name>Electronic Gear Ratio (Numerator)</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>ro</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2210</Index>
                <Name>Electronic Gear Ratio (Denominator)</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>ro</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2212</Index>
                <Name>Encoder Output Pulses</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2216</Index>
                <Name>Pcmd Exp Filter</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2217</Index>
                <Name>Pcmd Move Averge Filter</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2218</Index>
                <Name>Command Pulse Input Multiplier</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x222A</Index>
                <Name>Fully-closed Control Selection Switch</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2230</Index>
                <Name>Backlash Compensation Direction</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2231</Index>
                <Name>Backlash Compensation Value</Name>
                <Type>DINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2240</Index>
                <Name>PN240</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2233</Index>
                <Name>Backlash Compensation Time Constant</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2281</Index>
                <Name>Encoder Output Resolution</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2282</Index>
                <Name>Linear Scale pitch</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2284</Index>
                <Name>Scale Pluse</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x22D0</Index>
                <Name>PN2D0</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2300</Index>
                <Name>Speed Command Input Gain</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2304</Index>
                <Name>JOG Speed</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2305</Index>
                <Name>Soft Start Acceleration Time</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2306</Index>
                <Name>Soft Start Deceleration Time</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2307</Index>
                <Name>Speed Command Filter Time Constante</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2308</Index>
                <Name>Speed F/B filter time constant</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x230A</Index>
                <Name>Deceleration time when off servo and compulsion stops</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x230B</Index>
                <Name>Zero Keep Time</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x230C</Index>
                <Name>Speed feedforward moving average time</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2310</Index>
                <Name>Vibration Detection Switch</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2311</Index>
                <Name>Vibration Detection Sensibility</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2312</Index>
                <Name>Vibration Detection Level</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2316</Index>
                <Name>The motor is maximum speed. </Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x231A</Index>
                <Name>Deceleration ramp time</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2324</Index>
                <Name>Moment of Inertia Calculating Start Level</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2383</Index>
                <Name>JOG Speed for Linear type (Linear type)</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2384</Index>
                <Name>Vibration Detection Level   (Linear type)</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2385</Index>
                <Name>Motor max speed (Linear type)</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2400</Index>
                <Name>Torque Command Input Gain</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2401</Index>
                <Name>Torque Reference Filter Time Constant</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2402</Index>
                <Name>Forward Torque Limit</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2403</Index>
                <Name>Reverse Torque Limit</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2404</Index>
                <Name>Forward External Torque Limit</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2405</Index>
                <Name>Reverse External Torque Limit</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2406</Index>
                <Name>Emergency Stop Torque</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2407</Index>
                <Name>Speed Limit during Torque Control</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2408</Index>
                <Name>Torque Related Function Switch</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2409</Index>
                <Name>1st Notch Filter Frequency</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x240A</Index>
                <Name>1st Notch Filter Q Value</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x240B</Index>
                <Name>1st Notch Filter Depth</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x240C</Index>
                <Name>2nd Notch Filter Frequency</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x240D</Index>
                <Name>2nd Notch Filter Q Value</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x240E</Index>
                <Name>2nd Notch Filter Depth</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x240F</Index>
                <Name>2nd Torque Reference Filter Frequency</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2410</Index>
                <Name>2nd Torque Reference Filter Q Value</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2412</Index>
                <Name>1st Step 2nd Torque Reference Filter Time Constant</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2415</Index>
                <Name>Torque Filter Time Constant</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2416</Index>
                <Name>Switch 2 of function related to torque</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2417</Index>
                <Name>Frequency of the third step notch filter</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2418</Index>
                <Name>The third step notch filter Q value</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2419</Index>
                <Name>Depth of the third step notch filter</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x241A</Index>
                <Name>Frequency of the fourth step notch filter</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x241B</Index>
                <Name>The fourth step notch filter Q value</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x241C</Index>
                <Name>Depth of the fourth step notch filter</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x241D</Index>
                <Name>Frequency of the fifth step notch filter</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x241E</Index>
                <Name>The fifth step notch filter Q value</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x241F</Index>
                <Name>Depth of the fifth step notch filter</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2423</Index>
                <Name>Speed ripple amends switch</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2424</Index>
                <Name>Torque Limit at Main Circuit Voltage Drop</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2425</Index>
                <Name>Release Time for Torque Limit at Main Circuit Voltage Drop</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2426</Index>
                <Name>Torque feedforward moving average time</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2427</Index>
                <Name>Speed Ripple Compensation Enable Speed</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2456</Index>
                <Name>Sweep Torque Reference Amplitude</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2460</Index>
                <Name>Notch Filter Adjustment Switch</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2476</Index>
                <Name>Grivate Torque</Name>
                <Type>INT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2480</Index>
                <Name>Speed Limit during Force Control (Linear type)</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2481</Index>
                <Name>Polarity Detection Speed loop Gain</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2482</Index>
                <Name>Polarity Detection Speed loop Integral time</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2483</Index>
                <Name>Forward Force Limit(Linear type)</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2484</Index>
                <Name>Reverse Force Limit(Linear type)</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2485</Index>
                <Name>Polarity Detection Reference speed(Linear type)</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2486</Index>
                <Name>Polarity Detection Acceleration</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2487</Index>
                <Name>Polarity Detection Constant Speed time</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2488</Index>
                <Name>Polarity Detection Reference waiting time</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x248E</Index>
                <Name>Polarity Detection travel range(Linear type)</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2490</Index>
                <Name>Polarity Detection Load level</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2493</Index>
                <Name>Polarity Detection Command Speed</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2494</Index>
                <Name>Polarity Detection Moving Range</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2495</Index>
                <Name>Polarity Detection Confirmation Force Reference</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2498</Index>
                <Name>Polarity Detection Allowable Error range</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x249F</Index>
                <Name>Speed Ripple Compensation Enable Speed</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2501</Index>
                <Name>Zero Clamp Speed Level</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2502</Index>
                <Name>Rotation Detection Level</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2503</Index>
                <Name>Speed Coincidence Signal Output Width</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2506</Index>
                <Name>Brake Reference - Servo OFF Delay Time</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2507</Index>
                <Name>Brake Reference Output Speed Level</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2508</Index>
                <Name>Waiting Time for Brake Signal When Motor Running</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2509</Index>
                <Name>Instantaneous Power Cut Hold time</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x250A</Index>
                <Name>Input Signal Selection 1</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x250B</Index>
                <Name>Input Signal Selection 2</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x250C</Index>
                <Name>Input Signal Selection 3</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x250D</Index>
                <Name>Input Signal Selection 4</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x250E</Index>
                <Name>Output Signal Selection 1</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x250F</Index>
                <Name>Output Signal Selection 2</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2510</Index>
                <Name>Output Signal Selection 3</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2511</Index>
                <Name>Input Signal Selection 5</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2512</Index>
                <Name>Output Signal Reversal Selling</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2513</Index>
                <Name>Output signal selection 4</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2515</Index>
                <Name>Input signal selection 6</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2517</Index>
                <Name>ECAT Force Do Output</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2518</Index>
                <Name>Safety Module-Related Parameters</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x251B</Index>
                <Name>Excessive Error Level Between Servomotor and Load Positions</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x251E</Index>
                <Name>Excessive Position Error Warning Level</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2520</Index>
                <Name>Excessive Position Error Alarm Level</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2522</Index>
                <Name>Positioning Completed Width</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2524</Index>
                <Name>NEAR Signal Width</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2526</Index>
                <Name>When it is on, it is a positional deflection excessive alarm level of the servo. </Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2528</Index>
                <Name>When it is on, it is a positional deflection excessive warning level of the servo. </Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2529</Index>
                <Name>Speed Limit Level at Servo ON</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x252A</Index>
                <Name>Multiplier per One Fully-closed Rotation</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x252B</Index>
                <Name>Overload Warning Level</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x252C</Index>
                <Name>Derating of Base Current at Detecting Overload of Motor</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x252D</Index>
                <Name>Single Phase Default Power</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x252F</Index>
                <Name>PN52F</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2530</Index>
                <Name>Program JOG Operation Related Switch</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2531</Index>
                <Name>Program JOG Movement Distance</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2533</Index>
                <Name>Program JOG Movement Speed</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2534</Index>
                <Name>Program JOG Acceleration Time</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2535</Index>
                <Name>Program JOG Waiting Time</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2536</Index>
                <Name>Number of Times of Program JOG Movement</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2548</Index>
                <Name>Specified Alarm Number for Tracing</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2550</Index>
                <Name>Analog Monitor 1 Offset Voltage</Name>
                <Type>INT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2551</Index>
                <Name>Analog Monitor 2 Offset Voltage</Name>
                <Type>INT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2552</Index>
                <Name>Analog Monitor Magnification (xl)</Name>
                <Type>INT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2553</Index>
                <Name>Analog Monitor Magnification (x2)</Name>
                <Type>INT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x255A</Index>
                <Name>Power consumption monitor unit time</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2560</Index>
                <Name>Remained Vibration Detection Width</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2561</Index>
                <Name>Overshoot Detection Level</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2581</Index>
                <Name>Zero Speed Level (Linear type)</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2582</Index>
                <Name>Speed Coincidence Signal Output Width (Linear type)</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2583</Index>
                <Name>Brake Reference Output Speed Level (Linear type)</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2584</Index>
                <Name>Speed Limit Level at Servo ON (Linear type)</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2585</Index>
                <Name>Program JOG Movement Speed (Linear type)</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2586</Index>
                <Name>Motor Running Air-cooling Ratio(Linear type)</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2587</Index>
                <Name>Polarity Detection Execution Selection for Absolute Linear Encoder</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2600</Index>
                <Name>Regenerative Resistor Capacity</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2601</Index>
                <Name>DB resistance capacity</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2602</Index>
                <Name>Enc Type</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2603</Index>
                <Name>The resurrection resistance</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2604</Index>
                <Name>DB resistance</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2605</Index>
                <Name>Enc Value</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2606</Index>
                <Name>Enc com length</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2607</Index>
                <Name>Enc type second</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2608</Index>
                <Name>Enc ratio second</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2609</Index>
                <Name>BK Select</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x260A</Index>
                <Name>PG Out Select</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x260B</Index>
                <Name>Sencond Enc Resolution Type</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x260C</Index>
                <Name>Enc Over Flow</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x260D</Index>
                <Name>PN60D</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x260E</Index>
                <Name>PN60E</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x260F</Index>
                <Name>PN60F</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2610</Index>
                <Name>Position Compare Switch</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2611</Index>
                <Name>Position Compare First</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2613</Index>
                <Name>Position Compare Sencond</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2615</Index>
                <Name>Position Compare Third</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2617</Index>
                <Name>Position Compare Fourth</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2619</Index>
                <Name>Position Compare Time First</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x261A</Index>
                <Name>Position Compare Time Second</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x261B</Index>
                <Name>Position Compare Time Third</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x261C</Index>
                <Name>Position Compare Time Fourth</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x261D</Index>
                <Name>PN61D</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x261F</Index>
                <Name>PN61F</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2620</Index>
                <Name>bb on diff0</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2621</Index>
                <Name>Safety function application selection switch</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2622</Index>
                <Name>Internal deceleration of active mode constant A</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2623</Index>
                <Name>Internal deceleration of active mode constant B</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2624</Index>
                <Name>Internal deceleration of active mode stop speed</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2625</Index>
                <Name>Active mode stop continuance time</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2626</Index>
                <Name>Active mode release position deflection level</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2628</Index>
                <Name>Active mode release speed instruction level</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2629</Index>
                <Name>PN629</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x262D</Index>
                <Name>PN62D</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x262F</Index>
                <Name>PN62F</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2630</Index>
                <Name>PN630</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2631</Index>
                <Name>PN631</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2632</Index>
                <Name>PN632</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2633</Index>
                <Name>PN633</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2634</Index>
                <Name>PN634</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2635</Index>
                <Name>PN635</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2637</Index>
                <Name>PN637</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2639</Index>
                <Name>PN639</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x263B</Index>
                <Name>PN63B</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x263D</Index>
                <Name>PN63D</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2770</Index>
                <Name>Un003MotorAngle</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>m</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x2772</Index>
                <Name>Reservation parameters</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2774</Index>
                <Name>Reservation parameters</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2776</Index>
                <Name>Reservation parameters</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2778</Index>
                <Name>Reservation parameters</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2779</Index>
                <Name>Reservation parameters</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x277B</Index>
                <Name>Reservation parameters</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x277C</Index>
                <Name>Reservation parameters</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x277E</Index>
                <Name>Reservation parameters</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x277F</Index>
                <Name>Reservation parameters</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2780</Index>
                <Name>Reservation parameters</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2781</Index>
                <Name>Function Switch0</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2782</Index>
                <Name>SyncLostWindow</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2786</Index>
                <Name>SiteAddressAlias</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2787</Index>
                <Name>Function Switch1</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2788</Index>
                <Name>SencondEncPosActualSingleValue</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2789</Index>
                <Name>EncodeRatio</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>ro</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x278a</Index>
                <Name>ModuleFunctionPosLimit</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2790</Index>
                <Name>EcatDemandSwitch0</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2791</Index>
                <Name>EcatDemandSwitch1</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2792</Index>
                <Name>EcatDemandSwitch2</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2793</Index>
                <Name>EcatDemandSwitch3</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x2794</Index>
                <Name>SecEncPosActualValue</Name>
                <Type>DINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x2795</Index>
                <Name>Un009MotorLoad</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x2796</Index>
                <Name>Un00ARegenePerLoad</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x2797</Index>
                <Name>Un00BDbrxPerPower</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x2798</Index>
                <Name>First Enc Single Value</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x2799</Index>
                <Name>Ecat Function 0</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x279a</Index>
                <Name>Ecat Function 1</Name>
                <Type>DINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x279b</Index>
                <Name>Ecat Function 2</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x279c</Index>
                <Name>Ecat Function 3</Name>
                <Type>INT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x279d</Index>
                <Name>Ecat Function 4</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x279e</Index>
                <Name>Ecat Function 5</Name>
                <Type>DINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x279f</Index>
                <Name>Ecat Function 6</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x27a0</Index>
                <Name>Encoder Temperature Alarm Value</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x27a1</Index>
                <Name>EcatDemandSwitch5</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x27a2</Index>
                <Name>EcatDemandSwitch6</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x27a3</Index>
                <Name>EcatDemandSwitch7</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x27a4</Index>
                <Name>EcatDemandSwitch8</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x27a5</Index>
                <Name>Ecat Analog1</Name>
                <Type>INT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x27a6</Index>
                <Name>Ecat Analog2</Name>
                <Type>INT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x603F</Index>
                <Name>Error Code</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Info>
                  <MinValue>#x0000</MinValue>
                  <MaxValue>#xFFFF</MaxValue>
                  <DefaultValue>#x0000</DefaultValue>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6040</Index>
                <Name>Control Word</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Info>
                  <MinValue>#x0000</MinValue>
                  <MaxValue>#xFFFF</MaxValue>
                  <DefaultValue>#x0000</DefaultValue>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>m</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6041</Index>
                <Name>Status Word</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Info>
                  <MinValue>#x0000</MinValue>
                  <MaxValue>#xFFFF</MaxValue>
                  <DefaultValue>#x0000</DefaultValue>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>m</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x605A</Index>
                <Name>Quick stop option code</Name>
                <Type>INT</Type>
                <BitSize>16</BitSize>
                <Info>
                  <MinValue>#x0000</MinValue>
                  <MaxValue>#x0007</MaxValue>
                  <DefaultValue>#x0001</DefaultValue>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>m</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x605D</Index>
                <Name>Halt option code</Name>
                <Type>INT</Type>
                <BitSize>16</BitSize>
                <Info>
                  <MinValue>#x8000</MinValue>
                  <MaxValue>#x7FFF</MaxValue>
                  <DefaultValue>#x0001</DefaultValue>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>m</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6060</Index>
                <Name>Modes of operation </Name>
                <Type>SINT</Type>
                <BitSize>8</BitSize>
                <Info>
                  <MinValue>#x00</MinValue>
                  <MaxValue>#x0A</MaxValue>
                  <DefaultValue>#x00</DefaultValue>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>m</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6061</Index>
                <Name>Modes of operation display </Name>
                <Type>SINT</Type>
                <BitSize>8</BitSize>
                <Info>
                  <MinValue>#x00</MinValue>
                  <MaxValue>#x0A</MaxValue>
                  <DefaultValue>#x00</DefaultValue>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>m</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6062</Index>
                <Name>Position demand value </Name>
                <Type>DINT</Type>
                <BitSize>32</BitSize>
                <Info>
                  <MinValue>#x80000000</MinValue>
                  <MaxValue>#x7FFFFFFF</MaxValue>
                  <DefaultValue>#x00000000</DefaultValue>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6063</Index>
                <Name>Position actual internal value</Name>
                <Type>DINT</Type>
                <BitSize>32</BitSize>
                <Info>
                  <MinValue>#x80000000</MinValue>
                  <MaxValue>#x7FFFFFFF</MaxValue>
                  <DefaultValue>#x00000000</DefaultValue>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6064</Index>
                <Name>Position actual value</Name>
                <Type>DINT</Type>
                <BitSize>32</BitSize>
                <Info>
                  <MinValue>#x80000000</MinValue>
                  <MaxValue>#x7FFFFFFF</MaxValue>
                  <DefaultValue>#x00000000</DefaultValue>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6065</Index>
                <Name>Following error window </Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Info>
                  <MinValue>#x80000000</MinValue>
                  <MaxValue>#x7FFFFFFF</MaxValue>
                  <DefaultValue>#x000186A0</DefaultValue>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6067</Index>
                <Name>Position window</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Info>
                  <MinValue>#x00000000</MinValue>
                  <MaxValue>#x7FFFFFFF</MaxValue>
                  <DefaultValue>#x000002DE</DefaultValue>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6068</Index>
                <Name>Position window time</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Info>
                  <MinValue>#x0000</MinValue>
                  <MaxValue>#xFFFF</MaxValue>
                  <DefaultValue>#x0010</DefaultValue>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x606C</Index>
                <Name>Velocity actual value </Name>
                <Type>DINT</Type>
                <BitSize>32</BitSize>
                <Info>
                  <MinValue>#x80000000</MinValue>
                  <MaxValue>#x7FFFFFFF</MaxValue>
                  <DefaultValue>#x00000000</DefaultValue>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>m</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x606D</Index>
                <Name>Velocity window </Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Info>
                  <MinValue>#x0000</MinValue>
                  <MaxValue>#xFFFF</MaxValue>
                  <DefaultValue>#x000A</DefaultValue>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x606E</Index>
                <Name>Velocity window time</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Info>
                  <MinValue>#x0000</MinValue>
                  <MaxValue>#xFFFF</MaxValue>
                  <DefaultValue>#x0000</DefaultValue>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6071</Index>
                <Name>Target torque </Name>
                <Type>INT</Type>
                <BitSize>16</BitSize>
                <Info>
                  <MinValue>#x0000</MinValue>
                  <MaxValue>#x0FA0</MaxValue>
                  <DefaultValue>#x000A</DefaultValue>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>m</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6072</Index>
                <Name>Max torque</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Info>
                  <MinValue>#x0000</MinValue>
                  <MaxValue>#x0FA0</MaxValue>
                  <DefaultValue>#x056C</DefaultValue>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6074</Index>
                <Name>Torque demand value </Name>
                <Type>INT</Type>
                <BitSize>16</BitSize>
                <Info>
                  <MinValue>#xF060</MinValue>
                  <MaxValue>#x0FA0</MaxValue>
                  <DefaultValue>#x0000</DefaultValue>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6076</Index>
                <Name>Motor rated torque</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Info>
                  <MinValue>#x00000000</MinValue>
                  <MaxValue>#x0000FFFF</MaxValue>
                  <DefaultValue>#x0000FFFF</DefaultValue>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6077</Index>
                <Name>Torque actual value </Name>
                <Type>INT</Type>
                <BitSize>16</BitSize>
                <Info>
                  <MinValue>#xF060</MinValue>
                  <MaxValue>#x0FA0</MaxValue>
                  <DefaultValue>#x0000</DefaultValue>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x607A</Index>
                <Name>Target position</Name>
                <Type>DINT</Type>
                <BitSize>32</BitSize>
                <Info>
                  <MinValue>#x80000000</MinValue>
                  <MaxValue>#x7FFFFFFF</MaxValue>
                  <DefaultValue>#x00000000</DefaultValue>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>m</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x607C</Index>
                <Name>Home offset</Name>
                <Type>DINT</Type>
                <BitSize>32</BitSize>
                <Info>
                  <MinValue>#x80000000</MinValue>
                  <MaxValue>#x7FFFFFFF</MaxValue>
                  <DefaultValue>#x00000000</DefaultValue>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x607D</Index>
                <Name>Software position limit</Name>
                <Type>DT607D</Type>
                <BitSize>80</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of Entries</Name>
                    <Info>
                      <DefaultValue>#x02</DefaultValue>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Min position limit</Name>
                    <Info>
                      <MinValue>#x80000000</MinValue>
                      <MaxValue>#x7FFFFFFF</MaxValue>
                      <DefaultValue>#x80000001</DefaultValue>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Max position limit</Name>
                    <Info>
                      <MinValue>#x80000000</MinValue>
                      <MaxValue>#x7FFFFFFF</MaxValue>
                      <DefaultValue>#x7FFFFFFF</DefaultValue>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x607E</Index>
                <Name>Polarity </Name>
                <Type>USINT</Type>
                <BitSize>8</BitSize>
                <Info>
                  <MinValue>#x00</MinValue>
                  <MaxValue>#x01</MaxValue>
                  <DefaultValue>#x00</DefaultValue>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x607F</Index>
                <Name>Max profile velocity </Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Info>
                  <MinValue>#x00000000</MinValue>
                  <MaxValue>#xFFFFFFFF</MaxValue>
                  <DefaultValue>#x00000BB8</DefaultValue>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6080</Index>
                <Name>Max motor speed </Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Info>
                  <MinValue>#x00000000</MinValue>
                  <MaxValue>#xFFFFFFFF</MaxValue>
                  <DefaultValue>#x00001194</DefaultValue>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6081</Index>
                <Name>Profile velocity </Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Info>
                  <MinValue>#x00000000</MinValue>
                  <MaxValue>#xFFFFFFFF</MaxValue>
                  <DefaultValue>#x00000064</DefaultValue>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6083</Index>
                <Name>Profile acceleration</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Info>
                  <MinValue>#x00000000</MinValue>
                  <MaxValue>#xFFFFFFFF</MaxValue>
                  <DefaultValue>#x00000064</DefaultValue>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>m</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6084</Index>
                <Name>Profile deceleration </Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Info>
                  <MinValue>#x00000000</MinValue>
                  <MaxValue>#xFFFFFFFF</MaxValue>
                  <DefaultValue>#x00000064</DefaultValue>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>m</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6085</Index>
                <Name>Quick stop deceleration </Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Info>
                  <MinValue>#x00000000</MinValue>
                  <MaxValue>#xFFFFFFFF</MaxValue>
                  <DefaultValue>#x00000005</DefaultValue>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>m</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6086</Index>
                <Name>Motion profile type</Name>
                <Type>INT</Type>
                <BitSize>16</BitSize>
                <Info>
                  <MinValue>#x8000</MinValue>
                  <MaxValue>#x7FFF</MaxValue>
                  <DefaultValue>#x0000</DefaultValue>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>m</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6087</Index>
                <Name>Torque slope </Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Info>
                  <MinValue>#x00000000</MinValue>
                  <MaxValue>#xFFFFFFFF</MaxValue>
                  <DefaultValue>#xFFFFFFFF</DefaultValue>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>m</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6091</Index>
                <Name>Gear Ratio</Name>
                <Type>DT6091</Type>
                <BitSize>80</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of Entries</Name>
                    <Info>
                      <DefaultValue>#x02</DefaultValue>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Numerator</Name>
                    <Info>
                      <MinValue>#x00000000</MinValue>
                      <MaxValue>#xFFFFFFFF</MaxValue>
                      <DefaultValue>#x00000001</DefaultValue>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Denominator</Name>
                    <Info>
                      <MinValue>#x00000000</MinValue>
                      <MaxValue>#xFFFFFFFF</MaxValue>
                      <DefaultValue>#x00000001</DefaultValue>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x6098</Index>
                <Name>Homing method</Name>
                <Type>SINT</Type>
                <BitSize>8</BitSize>
                <Info>
                  <MinValue>#x00</MinValue>
                  <MaxValue>#x23</MaxValue>
                  <DefaultValue>#x01</DefaultValue>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6099</Index>
                <Name>Homing speeds</Name>
                <Type>DT6099</Type>
                <BitSize>80</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of Entries</Name>
                    <Info>
                      <DefaultValue>#x02</DefaultValue>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Speed during search for switch</Name>
                    <Info>
                      <MinValue>#x00000000</MinValue>
                      <MaxValue>#xFFFFFFFF</MaxValue>
                      <DefaultValue>#x00000064</DefaultValue>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Speed during search for zero</Name>
                    <Info>
                      <MinValue>#x00000000</MinValue>
                      <MaxValue>#xFFFFFFFF</MaxValue>
                      <DefaultValue>#x0000000A</DefaultValue>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x609A</Index>
                <Name>Homing acceleration</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Info>
                  <MinValue>#x00000000</MinValue>
                  <MaxValue>#xFFFFFFFF</MaxValue>
                  <DefaultValue>#x00000000</DefaultValue>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x60B0</Index>
                <Name>Postion Offset</Name>
                <Type>DINT</Type>
                <BitSize>32</BitSize>
                <Info>
                  <MinValue>#x80000000</MinValue>
                  <MaxValue>#x7FFFFFFF</MaxValue>
                  <DefaultValue>#x00000000</DefaultValue>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x60B1</Index>
                <Name>Velocity Offset</Name>
                <Type>DINT</Type>
                <BitSize>32</BitSize>
                <Info>
                  <MinValue>#x80000000</MinValue>
                  <MaxValue>#x7FFFFFFF</MaxValue>
                  <DefaultValue>#x00000000</DefaultValue>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x60B2</Index>
                <Name>Torque Offset</Name>
                <Type>INT</Type>
                <BitSize>16</BitSize>
                <Info>
                  <MinValue>#x8000</MinValue>
                  <MaxValue>#x7FFF</MaxValue>
                  <DefaultValue>#x0000</DefaultValue>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x60B8</Index>
                <Name>Touch Probe Function</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Info>
                  <MinValue>#x0000</MinValue>
                  <MaxValue>#xFFFF</MaxValue>
                  <DefaultValue>#x0000</DefaultValue>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x60B9</Index>
                <Name>Touch Probe Status</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Info>
                  <MinValue>#x0000</MinValue>
                  <MaxValue>#xFFFF</MaxValue>
                  <DefaultValue>#x0000</DefaultValue>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x60BA</Index>
                <Name>Touch Probe1 Pos Value</Name>
                <Type>DINT</Type>
                <BitSize>32</BitSize>
                <Info>
                  <MinValue>#x80000000</MinValue>
                  <MaxValue>#x7FFFFFFF</MaxValue>
                  <DefaultValue>#x00000000</DefaultValue>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x60BB</Index>
                <Name>Touch Probe1 Neg Value</Name>
                <Type>DINT</Type>
                <BitSize>32</BitSize>
                <Info>
                  <MinValue>#x80000000</MinValue>
                  <MaxValue>#x7FFFFFFF</MaxValue>
                  <DefaultValue>#x00000000</DefaultValue>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x60BC</Index>
                <Name>Touch Probe2 Pos Value</Name>
                <Type>DINT</Type>
                <BitSize>32</BitSize>
                <Info>
                  <MinValue>#x80000000</MinValue>
                  <MaxValue>#x7FFFFFFF</MaxValue>
                  <DefaultValue>#x00000000</DefaultValue>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x60BD</Index>
                <Name>Touch Probe2 Neg Value</Name>
                <Type>DINT</Type>
                <BitSize>32</BitSize>
                <Info>
                  <MinValue>#x80000000</MinValue>
                  <MaxValue>#x7FFFFFFF</MaxValue>
                  <DefaultValue>#x00000000</DefaultValue>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x60C2</Index>
                <Name>Interpolation time period</Name>
                <Type>DT60C2</Type>
                <BitSize>32</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of Entries</Name>
                    <Info>
                      <DefaultValue>#x02</DefaultValue>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>ip time units </Name>
                    <Info>
                      <MinValue>#x00</MinValue>
                      <MaxValue>#xFF</MaxValue>
                      <DefaultValue>#x01</DefaultValue>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>ip time index </Name>
                    <Info>
                      <MinValue>#x80</MinValue>
                      <MaxValue>#x7F</MaxValue>
                      <DefaultValue>#xFD</DefaultValue>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x60D5</Index>
                <Name>Touch Probe1 Pos1 Pos Count</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Info>
                  <MinValue>#x0000</MinValue>
                  <MaxValue>#xFFFF</MaxValue>
                  <DefaultValue>#x0000</DefaultValue>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x60D6</Index>
                <Name>Touch Probe1 Neg1 Pos Count</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Info>
                  <MinValue>#x0000</MinValue>
                  <MaxValue>#xFFFF</MaxValue>
                  <DefaultValue>#x0000</DefaultValue>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x60D7</Index>
                <Name>Touch Probe2 Pos1 Pos Count</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Info>
                  <MinValue>#x0000</MinValue>
                  <MaxValue>#xFFFF</MaxValue>
                  <DefaultValue>#x0000</DefaultValue>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x60D8</Index>
                <Name>Touch Probe2 Neg1 Pos Count</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Info>
                  <MinValue>#x0000</MinValue>
                  <MaxValue>#xFFFF</MaxValue>
                  <DefaultValue>#x0000</DefaultValue>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x60E0</Index>
                <Name>Positive torque limit</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Info>
                  <MinValue>#x0000</MinValue>
                  <MaxValue>#x0FA0</MaxValue>
                  <DefaultValue>#x0000</DefaultValue>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x60E1</Index>
                <Name>Negative torque limit</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Info>
                  <MinValue>#x0000</MinValue>
                  <MaxValue>#x0FA0</MaxValue>
                  <DefaultValue>#x0000</DefaultValue>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x60E3</Index>
                <Name>Support homing method</Name>
                <Type>DT60E3</Type>
                <BitSize>512</BitSize>
                <Info>
                  <SubItem>
                    <Name>SubIndex 000</Name>
                    <Info>
                      <MinValue>#x00</MinValue>
                      <MaxValue>#x1F</MaxValue>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Support homing method 1</Name>
                    <Info>
                      <DefaultData>0301</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Support homing method 2</Name>
                    <Info>
                      <DefaultData>0302</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Support homing method 3</Name>
                    <Info>
                      <DefaultData>0303</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Support homing method 4</Name>
                    <Info>
                      <DefaultData>0304</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Support homing method 5</Name>
                    <Info>
                      <DefaultData>0305</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Support homing method 6</Name>
                    <Info>
                      <DefaultData>0306</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Support homing method 7</Name>
                    <Info>
                      <DefaultData>0307</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Support homing method 8</Name>
                    <Info>
                      <DefaultData>0308</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Support homing method 9</Name>
                    <Info>
                      <DefaultData>0309</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Support homing method 10</Name>
                    <Info>
                      <DefaultData>030A</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Support homing method 11</Name>
                    <Info>
                      <DefaultData>030B</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Support homing method 12</Name>
                    <Info>
                      <DefaultData>030C</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Support homing method 13</Name>
                    <Info>
                      <DefaultData>030D</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Support homing method 14</Name>
                    <Info>
                      <DefaultData>030E</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Support homing method 15</Name>
                    <Info>
                      <DefaultData>030F</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Support homing method 16</Name>
                    <Info>
                      <DefaultData>0310</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Support homing method 17</Name>
                    <Info>
                      <DefaultData>0311</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Support homing method 18</Name>
                    <Info>
                      <DefaultData>0312</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Support homing method 19</Name>
                    <Info>
                      <DefaultData>0313</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Support homing method 20</Name>
                    <Info>
                      <DefaultData>0314</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Support homing method 21</Name>
                    <Info>
                      <DefaultData>0315</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Support homing method 22</Name>
                    <Info>
                      <DefaultData>0316</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Support homing method 23</Name>
                    <Info>
                      <DefaultData>0317</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Support homing method 24</Name>
                    <Info>
                      <DefaultData>0318</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Support homing method 25</Name>
                    <Info>
                      <DefaultData>0319</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Support homing method 26</Name>
                    <Info>
                      <DefaultData>031A</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Support homing method 27</Name>
                    <Info>
                      <DefaultData>031B</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Support homing method 28</Name>
                    <Info>
                      <DefaultData>031C</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Support homing method 29</Name>
                    <Info>
                      <DefaultData>031D</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Support homing method 30</Name>
                    <Info>
                      <DefaultData>031E</DefaultData>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Support homing method 31</Name>
                    <Info>
                      <DefaultData>031F</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                </Flags>
              </Object>
              <Object>
                <Index>#x60F4</Index>
                <Name>Following error actual value</Name>
                <Type>DINT</Type>
                <BitSize>32</BitSize>
                <Info>
                  <MinValue>#x80000000</MinValue>
                  <MaxValue>#x7FFFFFFF</MaxValue>
                  <DefaultValue>#x00000000</DefaultValue>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x60FC</Index>
                <Name>Position demand internal value</Name>
                <Type>DINT</Type>
                <BitSize>32</BitSize>
                <Info>
                  <MinValue>#x00000000</MinValue>
                  <MaxValue>#xFFFFFFFF</MaxValue>
                  <DefaultValue>#x00000000</DefaultValue>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x60FD</Index>
                <Name>Digital inputs</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Info>
                  <MinValue>#x00000000</MinValue>
                  <MaxValue>#xFFFFFFFF</MaxValue>
                  <DefaultValue>#x00000000</DefaultValue>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x60FE</Index>
                <Name>Digital outputs</Name>
                <Type>DT60FE</Type>
                <BitSize>80</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of Entries</Name>
                    <Info>
                      <DefaultValue>#x02</DefaultValue>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Physical outputs</Name>
                    <Info>
                      <MinValue>#x00000000</MinValue>
                      <MaxValue>#xFFFFFFFF</MaxValue>
                      <DefaultValue>#x00000000</DefaultValue>
                    </Info>
                  </SubItem>
                  <SubItem>
                    <Name>Bitmask</Name>
                    <Info>
                      <MinValue>#x00000000</MinValue>
                      <MaxValue>#xFFFFFFFF</MaxValue>
                      <DefaultValue>#x00000000</DefaultValue>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x60FF</Index>
                <Name>Target velocity</Name>
                <Type>DINT</Type>
                <BitSize>32</BitSize>
                <Info>
                  <MinValue>#x00000000</MinValue>
                  <MaxValue>#xFFFFFFFF</MaxValue>
                  <DefaultValue>#x00000000</DefaultValue>
                </Info>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6502</Index>
                <Name>Supported drive modes</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Info>
                  <MinValue>#x00000000</MinValue>
                  <MaxValue>#xFFFFFFFF</MaxValue>
                  <DefaultValue>#x00000000</DefaultValue>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x213F</Index>
                <Name>Servo Error Code</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Info>
                  <MinValue>#x0000</MinValue>
                  <MaxValue>#xFFFF</MaxValue>
                  <DefaultValue>#x0000</DefaultValue>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
            </Objects>
          </Dictionary>
        </Profile>
        <Fmmu>Outputs</Fmmu>
        <Fmmu>Inputs</Fmmu>
        <Fmmu>MBoxState</Fmmu>
        <Sm MinSize="34" MaxSize="128" DefaultSize="128" StartAddress="#x1000" ControlByte="#x26" Enable="1">MBoxOut</Sm>
        <Sm MinSize="34" MaxSize="128" DefaultSize="128" StartAddress="#x1400" ControlByte="#x22" Enable="1">MBoxIn</Sm>
        <Sm MinSize="0" MaxSize="128" StartAddress="#x1800" ControlByte="#x64" Enable="1">Outputs</Sm>
        <Sm MinSize="0" MaxSize="128" StartAddress="#x2000" ControlByte="#x20" Enable="1">Inputs</Sm>
        <RxPdo Fixed="false" Sm="2">
          <Index>#x1600</Index>
          <Name>1st RxPdo mapping</Name>
          <Exclude>#x1601</Exclude>
          <Exclude>#x1602</Exclude>
          <Exclude>#x1603</Exclude>
          <Exclude>#x1604</Exclude>
          <Entry>
            <Index>#x6040</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Control Word</Name>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6060</Index>
            <SubIndex>0</SubIndex>
            <BitLen>8</BitLen>
            <Name>Modes of operation </Name>
            <DataType>SINT</DataType>
          </Entry>
          <Entry>
            <Index>#x607A</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Target position</Name>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x60B8</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Touch Probe Function</Name>
            <DataType>UINT</DataType>
          </Entry>
        </RxPdo>
        <RxPdo Fixed="true">
          <Index>#x1601</Index>
          <Name>2nd RxPdo mapping</Name>
          <Exclude>#x1600</Exclude>
          <Exclude>#x1602</Exclude>
          <Exclude>#x1603</Exclude>
          <Exclude>#x1604</Exclude>
          <Entry>
            <Index>#x6040</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Control Word</Name>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6060</Index>
            <SubIndex>0</SubIndex>
            <BitLen>8</BitLen>
            <Name>Modes of operation </Name>
            <DataType>SINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6071</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Target torque </Name>
            <DataType>INT</DataType>
          </Entry>
          <Entry>
            <Index>#x607A</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Target position</Name>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6080</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Max motor speed</Name>
            <DataType>UDINT</DataType>
          </Entry>
          <Entry>
            <Index>#x60B8</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Touch Probe Function</Name>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x60FF</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Target velocity</Name>
            <DataType>DINT</DataType>
          </Entry>
        </RxPdo>
        <RxPdo Fixed="true">
          <Index>#x1602</Index>
          <Name>3rd RxPdo mapping</Name>
          <Exclude>#x1600</Exclude>
          <Exclude>#x1601</Exclude>
          <Exclude>#x1603</Exclude>
          <Exclude>#x1604</Exclude>
          <Entry>
            <Index>#x6040</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Control Word</Name>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6060</Index>
            <SubIndex>0</SubIndex>
            <BitLen>8</BitLen>
            <Name>Modes of operation </Name>
            <DataType>SINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6072</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Max Torque</Name>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x607A</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Target position</Name>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x60B8</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Touch Probe Function</Name>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x60FF</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Target velocity</Name>
            <DataType>DINT</DataType>
          </Entry>
        </RxPdo>
        <RxPdo Fixed="true">
          <Index>#x1603</Index>
          <Name>4th RxPdo mapping</Name>
          <Exclude>#x1600</Exclude>
          <Exclude>#x1601</Exclude>
          <Exclude>#x1602</Exclude>
          <Exclude>#x1604</Exclude>
          <Entry>
            <Index>#x6040</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Control Word</Name>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6060</Index>
            <SubIndex>0</SubIndex>
            <BitLen>8</BitLen>
            <Name>Modes of operation </Name>
            <DataType>SINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6071</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Target torque</Name>
            <DataType>INT</DataType>
          </Entry>
          <Entry>
            <Index>#x6072</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Max torque</Name>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x607A</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Target position</Name>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6080</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Max motor speed</Name>
            <DataType>UDINT</DataType>
          </Entry>
          <Entry>
            <Index>#x60B8</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Touch Probe Function</Name>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x60FF</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Target velocity</Name>
            <DataType>DINT</DataType>
          </Entry>
        </RxPdo>
        <RxPdo Fixed="true">
          <Index>#x1604</Index>
          <Name>5th RxPdo mapping</Name>
          <Exclude>#x1600</Exclude>
          <Exclude>#x1601</Exclude>
          <Exclude>#x1602</Exclude>
          <Exclude>#x1603</Exclude>
          <Entry>
            <Index>#x6040</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Control Word</Name>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x607A</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Target position</Name>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x60FF</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Target velocity</Name>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6071</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Target torque</Name>
            <DataType>INT</DataType>
          </Entry>
          <Entry>
            <Index>#x6060</Index>
            <SubIndex>0</SubIndex>
            <BitLen>8</BitLen>
            <Name>Modes of operation</Name>
            <DataType>SINT</DataType>
          </Entry>
          <Entry>
            <Index>#x60B8</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Touch Probe Function</Name>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x60E0</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Positive torque limit</Name>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x60E1</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Negative torque limit</Name>
            <DataType>UINT</DataType>
          </Entry>
        </RxPdo>
        <TxPdo Fixed="false" Sm="3">
          <Index>#x1a00</Index>
          <Name>1st TxPdo mapping</Name>
          <Exclude>#x1A01</Exclude>
          <Exclude>#x1A02</Exclude>
          <Exclude>#x1A03</Exclude>
          <Exclude>#x1A04</Exclude>
          <Entry>
            <Index>#x603F</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Error Code</Name>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6041</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Status Word</Name>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6064</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Position actual value</Name>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6061</Index>
            <SubIndex>0</SubIndex>
            <BitLen>8</BitLen>
            <Name>Modes of operation display</Name>
            <DataType>SINT</DataType>
          </Entry>
          <Entry>
            <Index>#x60B9</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Touch probe status</Name>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x60BA</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Touch probe pos1 pos value</Name>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x60F4</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Following error actual value</Name>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x60FD</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Digital inputs</Name>
            <DataType>UDINT</DataType>
          </Entry>
        </TxPdo>
        <TxPdo Fixed="true">
          <Index>#x1a01</Index>
          <Name>2nd TxPdo mapping</Name>
          <Exclude>#x1A00</Exclude>
          <Exclude>#x1A02</Exclude>
          <Exclude>#x1A03</Exclude>
          <Exclude>#x1A04</Exclude>
          <Entry>
            <Index>#x603F</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Error code</Name>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6041</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Statusword</Name>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6061</Index>
            <SubIndex>0</SubIndex>
            <BitLen>8</BitLen>
            <Name>Modes of operation display</Name>
            <DataType>SINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6064</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Position actual value</Name>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x606C</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Velocity actual value</Name>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6077</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Torque actual value</Name>
            <DataType>INT</DataType>
          </Entry>
          <Entry>
            <Index>#x60B9</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Touch probe status</Name>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x60BA</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Touch probe pos1 pos value</Name>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x60BB</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Touch probe pos1 neg value</Name>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x60FD</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Digital inputs</Name>
            <DataType>UDINT</DataType>
          </Entry>
        </TxPdo>
        <TxPdo Fixed="true">
          <Index>#x1a02</Index>
          <Name>3rd TxPdo mapping</Name>
          <Exclude>#x1A00</Exclude>
          <Exclude>#x1A01</Exclude>
          <Exclude>#x1A03</Exclude>
          <Exclude>#x1A04</Exclude>
          <Entry>
            <Index>#x603F</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Error code</Name>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6041</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Statusword</Name>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6061</Index>
            <SubIndex>0</SubIndex>
            <BitLen>8</BitLen>
            <Name>Modes of operation display</Name>
            <DataType>SINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6064</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Position actual value</Name>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x606C</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Velocity actual value</Name>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6077</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Torque actual value</Name>
            <DataType>INT</DataType>
          </Entry>
          <Entry>
            <Index>#x60B9</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Touch probe status</Name>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x60BA</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Touch probe pos1 pos value</Name>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x60FD</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Digital inputs</Name>
            <DataType>UDINT</DataType>
          </Entry>
        </TxPdo>
        <TxPdo Fixed="true">
          <Index>#x1a03</Index>
          <Name>4th TxPdo mapping</Name>
          <Exclude>#x1A00</Exclude>
          <Exclude>#x1A01</Exclude>
          <Exclude>#x1A02</Exclude>
          <Exclude>#x1A03</Exclude>
          <Entry>
            <Index>#x603F</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Error code</Name>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6041</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Statusword</Name>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6061</Index>
            <SubIndex>0</SubIndex>
            <BitLen>8</BitLen>
            <Name>Modes of operation display</Name>
            <DataType>SINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6064</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Position actual value</Name>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x606C</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Velocity actual value</Name>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6077</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Torque actual value</Name>
            <DataType>INT</DataType>
          </Entry>
          <Entry>
            <Index>#x60B9</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Touch probe status</Name>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x60BA</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Touch probe pos1 pos value</Name>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x60FD</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Digital inputs</Name>
            <DataType>UDINT</DataType>
          </Entry>
        </TxPdo>
        <TxPdo Fixed="true">
          <Index>#x1a04</Index>
          <Name>5th TxPdo mapping</Name>
          <Exclude>#x1A00</Exclude>
          <Exclude>#x1A01</Exclude>
          <Exclude>#x1A02</Exclude>
          <Exclude>#x1A03</Exclude>
          <Entry>
            <Index>#x603F</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Error Code</Name>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6041</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Status Word</Name>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6061</Index>
            <SubIndex>0</SubIndex>
            <BitLen>8</BitLen>
            <Name>Modes of operation display</Name>
            <DataType>SINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6064</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Position actual value</Name>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x606C</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Velocity actual value</Name>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6077</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Torque actual value</Name>
            <DataType>INT</DataType>
          </Entry>
          <Entry>
            <Index>#x60F4</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Following error actual value</Name>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x60B9</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Touch probe status</Name>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x60BA</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Touch probe pos1 pos value</Name>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x60BC</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Touch Probe2 Pos1 Pos Value</Name>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x60FD</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Digital inputs</Name>
            <DataType>UDINT</DataType>
          </Entry>
        </TxPdo>
        <Mailbox DataLinkLayer="true">
          <CoE SdoInfo="true" PdoAssign="true" CompleteAccess="false" PdoConfig="true">
            <InitCmd>
              <Transition>PS</Transition>
              <Index>#x6060</Index>
              <SubIndex>0</SubIndex>
              <Data>08</Data>
            </InitCmd>
          </CoE>
          <FoE></FoE>
        </Mailbox>
        <Dc>
          <OpMode>
            <Name>DC</Name>
            <Desc>DC-Synchron</Desc>
            <AssignActivate>#x300</AssignActivate>
            <CycleTimeSync0 Factor="1">0</CycleTimeSync0>
            <ShiftTimeSync0>0</ShiftTimeSync0>
            <CycleTimeSync1 Factor="1">0</CycleTimeSync1>
          </OpMode>
          <OpMode>
            <Name>FreeRun</Name>
            <Desc>Free-Run</Desc>
            <AssignActivate>#x0</AssignActivate>
            <CycleTimeSync0 Factor="1">0</CycleTimeSync0>
            <ShiftTimeSync0>0</ShiftTimeSync0>
            <CycleTimeSync1 Factor="1">0</CycleTimeSync1>
          </OpMode>
        </Dc>
        <Eeprom>
          <ByteSize>8192</ByteSize>
          <ConfigData>080E00CC88130000000000800000</ConfigData>
        </Eeprom>
        <ImageData16x14>424dd8020000000000003600000028000000100000000e00000001001800000000000000000000000000000000000000000000000000fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffefeffe1efdef2f7f0f2f8f1eaf3e9fafcf9cee5c9d6e8d2d8ead5f9fbf8fffffffbfcfbdaebd5c3ddbcecf6ecfffffffffeff8fc2878fc388b8dab07bbe7275b86790c386b5d7ac56a549b7d9b2eff6ed9bc99772b46761aa4e90c485ffffffffffffa5cf9e3b9a2b6daa5886a76b359622dff0ddffffff87be7c449c32aad1a1bddcba9fcd9893c58658a646f7faf6ffffffd6e8d05cac4dbbd6b5c3918c81a86685c37e89c17db8d9b14ba0387dba6fb6d8ada6d09d77b769489d35f3f9f3fffffffbfdfbd3e7ceeff9f0f0e5e3fff7fbc2e0bd9ac890dfeedbd2e7cea4ce9bc3dfbcbcdbb6aad2a1cce4c9ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
				</ImageData16x14>
      </Device>
    </Devices>
  </Descriptions>
</EtherCATInfo>