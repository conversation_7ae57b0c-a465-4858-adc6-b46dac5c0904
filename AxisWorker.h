#ifndef AXISWORKER_H
#define AXISWORKER_H

#include <QObject>
#include <QString>
#include <QVector>
#include <QMap>

// 定義PVT數據緩存結構
struct PVTDataCache {
    QVector<double> times;
    QVector<double> positions;
    QVector<double> velocities;
    double startVelocity = 0.0;
    double endVelocity = 0.0;
    bool isDirty = true;
    bool isUploaded = false;
};

class AxisWorker : public QObject {
    Q_OBJECT
public:
    explicit AxisWorker(short axis, short cardCore, QObject *parent = nullptr);
    ~AxisWorker();

    // 將函數移到這裡（public部分）
    void setOriginalCommand(const QString& cmd) {
        originalCommand = cmd;
        // 直接呼叫信號
        emit calculationDone(m_axis);  // 注意：使用m_axis而不是axis
    }

public slots:
    void processCommand(const QString &command); // 處理單軸命令
    void uploadData(short tableId);              // 上傳數據到控制卡

signals:
    void calculationDone(short axis);           // 計算完成信號
    void uploadDone(short axis);                // 上傳完成信號
    void error(short axis, const QString &msg); // 錯誤信號

private:
    short m_axis;                              // 軸號
    short m_cardCore;                          // 卡核心編號
    PVTDataCache m_cache;                      // 軸緩存數據
    QString originalCommand;                   // 存儲原始命令
    void calculatePVTData(const QString &cmd); // 計算PVT數據
};

#endif // AXISWORKER_H 