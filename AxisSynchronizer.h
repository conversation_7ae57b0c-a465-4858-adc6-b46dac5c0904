#ifndef AXISSYNCHRONIZER_H
#define AXISSYNCHRONIZER_H

#include <QObject>
#include <QSet>
#include <QMutex>
#include <QDebug>

class AxisSynchronizer : public QObject {
    Q_OBJECT
public:
    explicit AxisSynchronizer(QObject *parent = nullptr);
    ~AxisSynchronizer();
    
    void addAxis(short axis);                 // 添加需要同步的軸
    void resetBarrier();                      // 重置同步障壁
    int waitingCount() const;                 // 剩餘等待軸數量
    QSet<short> pendingAxes() const;          // 獲取等待中的軸集合

public slots:
    void axisReady(short axis);               // 軸就緒通知

signals:
    void allAxesReady();                      // 所有軸就緒信號
    void axisReadyChanged(short axis, bool isReady); // 軸狀態改變信號

private:
    QSet<short> m_pendingAxes;                // 等待中的軸
    mutable QMutex m_mutex;                   // 互斥鎖
};

#endif // AXISSYNCHRONIZER_H 