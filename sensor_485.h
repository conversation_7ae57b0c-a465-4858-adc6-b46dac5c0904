#ifndef SENSOR_485_H
#define SENSOR_485_H

#include <QDialog>
#include <QSerialPort>
#include <QSerialPortInfo>
#include <QTextEdit>

namespace Ui {
class Sensor_485;
}

class Sensor_485 : public QDialog
{
    Q_OBJECT

public:
    explicit Sensor_485(QWidget *parent = nullptr);
    ~Sensor_485();

    QString getName() const;
    void setName(const QString& name);
    bool isConnected() const;
    bool sendCommand(const QString& command);
    bool sendHexCommand(const QByteArray& hexData);  // 添加这行

signals:
    void nameChanged(const QString& newName);


private slots:
    void on_pushButton_485_Connect_clicked();
    void on_pushButton_485sendtest_clicked();
    void readData();
    void on_pushButton_RefreshPorts_clicked();

private:
    Ui::Sensor_485 *ui;
    QSerialPort *serialPort;
    QTextEdit *lineEdit_485BackMassae;
    void initializeSerialPort();
    void updateComPorts();
};

#endif // SENSOR_485_H
