<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ethercat</class>
 <widget class="QWidget" name="ethercat">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1276</width>
    <height>800</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>EtherCAT Connection</string>
  </property>
  <property name="styleSheet">
   <string notr="true">
    QDialog {
      background-color: #f5f5f5;
    }
    QLabel {
      font-size: 12px;
      background: transparent;
    }
    QLineEdit, QTextEdit {
      background-color: white;
      border: 1px solid #cccccc;
      border-radius: 4px;
      padding: 4px;
    }
    QPushButton {
      background-color: #e0e0e0;
      color: #333333;
      border: 1px solid #cccccc;
      border-radius: 4px;
      padding: 6px 12px;
      min-width: 80px;
    }
    QPushButton:hover {
      background-color: #d0d0d0;
    }
    QGroupBox {
      background: transparent;
      border: 1px solid #cccccc;
      border-radius: 4px;
      margin-top: 1ex;
      padding: 10px;
    }
    QGroupBox::title {
      subcontrol-origin: margin;
      subcontrol-position: top left;
      padding: 0 3px;
      background-color: #f5f5f5;
    }
    QComboBox {
      background-color: white;
      border: 1px solid #cccccc;
      border-radius: 4px;
      padding: 4px;
    }
    QComboBox:hover {
      border-color: #aaaaaa;
    }
    QComboBox::drop-down {
      border: none;
      width: 20px;
    }
    QComboBox::down-arrow {
      image: url(:/images/down-arrow.png);
      width: 12px;
      height: 12px;
    }
   </string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>20</number>
   </property>
   <property name="sizeConstraint">
    <enum>QLayout::SetDefaultConstraint</enum>
   </property>
   <property name="leftMargin">
    <number>30</number>
   </property>
   <property name="topMargin">
    <number>20</number>
   </property>
   <property name="rightMargin">
    <number>30</number>
   </property>
   <property name="bottomMargin">
    <number>20</number>
   </property>
   <item>
    <layout class="QVBoxLayout" name="topLayout">
     <property name="sizeConstraint">
      <enum>QLayout::SetMaximumSize</enum>
     </property>
     <item>
      <widget class="QLabel" name="label_title">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="font">
        <font>
         <pointsize>-1</pointsize>
         <bold>true</bold>
        </font>
       </property>
       <property name="text">
        <string>EtherCAT Connect</string>
       </property>
      </widget>
     </item>
     <item>
      <layout class="QHBoxLayout" name="nameLayout">
       <property name="spacing">
        <number>10</number>
       </property>
       <item>
        <widget class="QLabel" name="label_name">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>50</width>
           <height>0</height>
          </size>
         </property>
         <property name="text">
          <string>NAME:</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLineEdit" name="lineEdit_ethercat_name">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
           <horstretch>1</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="cardLayout">
       <item>
        <widget class="QLabel" name="label_card">
         <property name="text">
          <string>运动控制卡:</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QComboBox" name="comboBox_movepic"/>
       </item>
       <item>
        <widget class="QLabel" name="label">
         <property name="text">
          <string>驱动器:</string>
         </property>
        </widget>
       </item>
       <item>
        <layout class="QHBoxLayout" name="driveLayout">
         <item>
          <widget class="QComboBox" name="comboBox_move_drive">
           <property name="placeholderText">
            <string>请选择驱动器</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButton_refresh_drives">
           <property name="text">
            <string>刷新</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QLabel" name="label_2">
         <property name="text">
          <string>驱动器状态：</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLineEdit" name="lineEdit_drive_sis">
         <property name="readOnly">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_motion_control">
         <item>
          <widget class="QLabel" name="label_position">
           <property name="text">
            <string>目標位置(pulse):</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QSpinBox" name="spinBox_position">
           <property name="minimum">
            <number>-1000000</number>
           </property>
           <property name="maximum">
            <number>1000000</number>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_velocity">
           <property name="text">
            <string>速度(rpm):</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QSpinBox" name="spinBox_velocity">
           <property name="minimum">
            <number>0</number>
           </property>
           <property name="maximum">
            <number>3000</number>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButton_move">
           <property name="text">
            <string>移動</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="connectionLayout">
       <item>
        <widget class="QLabel" name="label_ip">
         <property name="text">
          <string>IP:</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLineEdit" name="lineEdit_ethercat_ip"/>
       </item>
       <item>
        <widget class="QLabel" name="label_port">
         <property name="text">
          <string>Port:</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLineEdit" name="lineEdit__ethercat_port"/>
       </item>
       <item>
        <widget class="QPushButton" name="pushButton_ethercat_connect">
         <property name="text">
          <string>Connect</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <widget class="QGroupBox" name="groupBox_send">
       <property name="maximumSize">
        <size>
         <width>0</width>
         <height>0</height>
        </size>
       </property>
       <property name="title">
        <string>Send Message</string>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout_2">
        <item>
         <widget class="QTextEdit" name="textEdit_ethercat_send_massage"/>
        </item>
        <item alignment="Qt::AlignRight">
         <widget class="QPushButton" name="pushButton_ethercat_send_m">
          <property name="text">
           <string>Send</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QGroupBox" name="groupBox_receive">
       <property name="maximumSize">
        <size>
         <width>0</width>
         <height>0</height>
        </size>
       </property>
       <property name="title">
        <string>Received Message</string>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout_3">
        <item>
         <widget class="QTextEdit" name="textEdit_ethercat_back_m">
          <property name="readOnly">
           <bool>true</bool>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QWidget" name="bottomWidget" native="true">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>200</height>
      </size>
     </property>
     <widget class="QLabel" name="label_3">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>20</y>
        <width>141</width>
        <height>19</height>
       </rect>
      </property>
      <property name="text">
       <string>定义绝对位置(脉冲位置):</string>
      </property>
     </widget>
     <widget class="QLineEdit" name="lineEdit_PP_Position">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>50</y>
        <width>371</width>
        <height>25</height>
       </rect>
      </property>
     </widget>
     <widget class="QPushButton" name="pushButton_pp_Position">
      <property name="geometry">
       <rect>
        <x>410</x>
        <y>50</y>
        <width>106</width>
        <height>28</height>
       </rect>
      </property>
      <property name="text">
       <string>SEND</string>
      </property>
     </widget>
     <widget class="QLabel" name="label_4">
      <property name="geometry">
       <rect>
        <x>590</x>
        <y>20</y>
        <width>171</width>
        <height>19</height>
       </rect>
      </property>
      <property name="text">
       <string>定义回归绝对位置(脉冲位置):</string>
      </property>
     </widget>
     <widget class="QPushButton" name="pushButton_backhome">
      <property name="geometry">
       <rect>
        <x>750</x>
        <y>50</y>
        <width>106</width>
        <height>28</height>
       </rect>
      </property>
      <property name="text">
       <string>HOME</string>
      </property>
     </widget>
    </widget>
   </item>
   <item>
    <widget class="Line" name="line">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QVBoxLayout" name="bottomLayout"/>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
