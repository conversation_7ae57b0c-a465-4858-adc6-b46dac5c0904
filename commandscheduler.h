///////////commandscheduler.h


#ifndef COMMANDSCHEDULER_H
#define COMMANDSCHEDULER_H

#include <QDialog>
#include "ui_commandscheduler.h"  // 包含自動生成的 UI 標頭檔

namespace Ui {
class CommandScheduler;
}

class CommandScheduler : public QDialog
{
    Q_OBJECT

public:
    explicit CommandScheduler(QWidget *parent = nullptr);
    ~CommandScheduler();

    void setTotalTimes(const QString &time);

private:
    Ui::CommandScheduler *ui;
};

#endif // COMMANDSCHEDULER_H
