<?xml version="1.0" encoding="UTF-8"?>
<EtherCATConfig>
	<Config>
		<Master AdapterIndex="0" IoUpdateFreq="4" StackDebugLevel="0">
			<Info>
				<Name>GOOGOL EtherCAT Master</Name>
				<Destination>FFFFFFFFFFFF</Destination>
				<Source>000000000000</Source>
				<EtherType>A488</EtherType>
			</Info>
		</Master>


		<Slave>
			<Info>
				<Name>HCFA Y7 Servo Driver</Name>
				<VendorId>71367</VendorId>
				<ProductCode>8258562</ProductCode>
				<RevisionNo>1</RevisionNo>
				<SerialNo>-1</SerialNo>
				<DevType>1</DevType>
				<NChannel>1</NChannel>
				<UseLrdLwr>0</UseLrdLwr>
				<SlaveId>1001</SlaveId>
			</Info>
			<ProcessData PdoAssign="true" PdoConfig="true">
				<Sm2>
					<Type>Outputs</Type>
					<MinSize>0</MinSize>
					<MaxSize>128</MaxSize>
					<StartAddress>6144</StartAddress>
					<ControlByte>100</ControlByte>
					<Enable>true</Enable>
					<Pdo>5632</Pdo>
				</Sm2>
				<Sm3>
					<Type>Inputs</Type>
					<MinSize>0</MinSize>
					<MaxSize>128</MaxSize>
					<StartAddress>8192</StartAddress>
					<ControlByte>32</ControlByte>
					<Enable>true</Enable>
					<Pdo>6656</Pdo>
				</Sm3>
				<RxPdo Fixed="false" Mandatory="false" Sm="2" Virtual="false">
					<Index>#x1600</Index>
					<Name LcId="1033">1st RxPdo mapping</Name>
					<Exclude>#x1601</Exclude>
					<Exclude>#x1602</Exclude>
					<Exclude>#x1603</Exclude>
					<Exclude>#x1604</Exclude>
					<Entry Fixed="false">
						<Index>#x6040</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Control Word</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6060</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>8</BitLen>
						<Name LcId="1033">Modes of operation </Name>
						<Comment />
						<DataType>SINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x607a</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Target position</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60b8</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Touch Probe Function</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
				</RxPdo>
				<TxPdo Fixed="false" Mandatory="false" Sm="3" Virtual="false">
					<Index>#x1a00</Index>
					<Name LcId="1033">1st TxPdo mapping</Name>
					<Exclude>#x1a01</Exclude>
					<Exclude>#x1a02</Exclude>
					<Exclude>#x1a03</Exclude>
					<Exclude>#x1a04</Exclude>
					<Entry Fixed="false">
						<Index>#x603f</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Error Code</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6041</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Status Word</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6064</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Position actual value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6061</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>8</BitLen>
						<Name LcId="1033">Modes of operation display</Name>
						<Comment />
						<DataType>SINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60b9</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Touch probe status</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60ba</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Touch probe pos1 pos value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60f4</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Following error actual value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60fd</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Digital inputs</Name>
						<Comment />
						<DataType>UDINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
				</TxPdo>
			</ProcessData>
			<Mailbox>
				<Send>
					<Start>4096</Start>
					<Length>128</Length>
				</Send>
				<Recv>
					<Start>5120</Start>
					<Length>128</Length>
				</Recv>
				<Protocol>CoE</Protocol>
				<CoE PdoCheck="0">


				<Slave>
			<Info>
				<Name>HCFA Y7 Servo Driver</Name>
				<VendorId>71367</VendorId>
				<ProductCode>8258562</ProductCode>
				<RevisionNo>1</RevisionNo>
				<SerialNo>-1</SerialNo>
				<DevType>1</DevType>
				<NChannel>1</NChannel>
				<UseLrdLwr>0</UseLrdLwr>
				<SlaveId>1002</SlaveId>
			</Info>
			<ProcessData PdoAssign="true" PdoConfig="true">
				<Sm2>
					<Type>Outputs</Type>
					<MinSize>0</MinSize>
					<MaxSize>128</MaxSize>
					<StartAddress>6144</StartAddress>
					<ControlByte>100</ControlByte>
					<Enable>true</Enable>
					<Pdo>5632</Pdo>
				</Sm2>
				<Sm3>
					<Type>Inputs</Type>
					<MinSize>0</MinSize>
					<MaxSize>128</MaxSize>
					<StartAddress>8192</StartAddress>
					<ControlByte>32</ControlByte>
					<Enable>true</Enable>
					<Pdo>6656</Pdo>
				</Sm3>
				<RxPdo Fixed="false" Mandatory="false" Sm="2" Virtual="false">
					<Index>#x1600</Index>
					<Name LcId="1033">1st RxPdo mapping</Name>
					<Exclude>#x1601</Exclude>
					<Exclude>#x1602</Exclude>
					<Exclude>#x1603</Exclude>
					<Exclude>#x1604</Exclude>
					<Entry Fixed="false">
						<Index>#x6040</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Control Word</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6060</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>8</BitLen>
						<Name LcId="1033">Modes of operation </Name>
						<Comment />
						<DataType>SINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x607a</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Target position</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60b8</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Touch Probe Function</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
				</RxPdo>
				<TxPdo Fixed="false" Mandatory="false" Sm="3" Virtual="false">
					<Index>#x1a00</Index>
					<Name LcId="1033">1st TxPdo mapping</Name>
					<Exclude>#x1a01</Exclude>
					<Exclude>#x1a02</Exclude>
					<Exclude>#x1a03</Exclude>
					<Exclude>#x1a04</Exclude>
					<Entry Fixed="false">
						<Index>#x603f</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Error Code</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6041</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Status Word</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6064</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Position actual value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6061</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>8</BitLen>
						<Name LcId="1033">Modes of operation display</Name>
						<Comment />
						<DataType>SINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60b9</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Touch probe status</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60ba</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Touch probe pos1 pos value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60f4</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Following error actual value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60fd</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Digital inputs</Name>
						<Comment />
						<DataType>UDINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
				</TxPdo>
			</ProcessData>
			<Mailbox>
				<Send>
					<Start>4096</Start>
					<Length>128</Length>
				</Send>
				<Recv>
					<Start>5120</Start>
					<Length>128</Length>
				</Recv>
				<Protocol>CoE</Protocol>
				<CoE PdoCheck="0">
<Slave>
			<Info>
				<Name>HCFA Y7 Servo Driver</Name>
				<VendorId>71367</VendorId>
				<ProductCode>8258562</ProductCode>
				<RevisionNo>1</RevisionNo>
				<SerialNo>-1</SerialNo>
				<DevType>1</DevType>
				<NChannel>1</NChannel>
				<UseLrdLwr>0</UseLrdLwr>
				<SlaveId>1003</SlaveId>
			</Info>
			<ProcessData PdoAssign="true" PdoConfig="true">
				<Sm2>
					<Type>Outputs</Type>
					<MinSize>0</MinSize>
					<MaxSize>128</MaxSize>
					<StartAddress>6144</StartAddress>
					<ControlByte>100</ControlByte>
					<Enable>true</Enable>
					<Pdo>5632</Pdo>
				</Sm2>
				<Sm3>
					<Type>Inputs</Type>
					<MinSize>0</MinSize>
					<MaxSize>128</MaxSize>
					<StartAddress>8192</StartAddress>
					<ControlByte>32</ControlByte>
					<Enable>true</Enable>
					<Pdo>6656</Pdo>
				</Sm3>
				<RxPdo Fixed="false" Mandatory="false" Sm="2" Virtual="false">
					<Index>#x1600</Index>
					<Name LcId="1033">1st RxPdo mapping</Name>
					<Exclude>#x1601</Exclude>
					<Exclude>#x1602</Exclude>
					<Exclude>#x1603</Exclude>
					<Exclude>#x1604</Exclude>
					<Entry Fixed="false">
						<Index>#x6040</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Control Word</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6060</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>8</BitLen>
						<Name LcId="1033">Modes of operation </Name>
						<Comment />
						<DataType>SINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x607a</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Target position</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60b8</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Touch Probe Function</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
				</RxPdo>
				<TxPdo Fixed="false" Mandatory="false" Sm="3" Virtual="false">
					<Index>#x1a00</Index>
					<Name LcId="1033">1st TxPdo mapping</Name>
					<Exclude>#x1a01</Exclude>
					<Exclude>#x1a02</Exclude>
					<Exclude>#x1a03</Exclude>
					<Exclude>#x1a04</Exclude>
					<Entry Fixed="false">
						<Index>#x603f</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Error Code</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6041</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Status Word</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6064</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Position actual value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6061</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>8</BitLen>
						<Name LcId="1033">Modes of operation display</Name>
						<Comment />
						<DataType>SINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60b9</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Touch probe status</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60ba</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Touch probe pos1 pos value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60f4</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Following error actual value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60fd</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Digital inputs</Name>
						<Comment />
						<DataType>UDINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
				</TxPdo>
			</ProcessData>
			<Mailbox>
				<Send>
					<Start>4096</Start>
					<Length>128</Length>
				</Send>
				<Recv>
					<Start>5120</Start>
					<Length>128</Length>
				</Recv>
				<Protocol>CoE</Protocol>
				<CoE PdoCheck="0">

				<Slave>
			<Info>
				<Name>HCFA Y7 Servo Driver</Name>
				<VendorId>71367</VendorId>
				<ProductCode>8258562</ProductCode>
				<RevisionNo>1</RevisionNo>
				<SerialNo>-1</SerialNo>
				<DevType>1</DevType>
				<NChannel>1</NChannel>
				<UseLrdLwr>0</UseLrdLwr>
				<SlaveId>1004</SlaveId>
			</Info>
			<ProcessData PdoAssign="true" PdoConfig="true">
				<Sm2>
					<Type>Outputs</Type>
					<MinSize>0</MinSize>
					<MaxSize>128</MaxSize>
					<StartAddress>6144</StartAddress>
					<ControlByte>100</ControlByte>
					<Enable>true</Enable>
					<Pdo>5632</Pdo>
				</Sm2>
				<Sm3>
					<Type>Inputs</Type>
					<MinSize>0</MinSize>
					<MaxSize>128</MaxSize>
					<StartAddress>8192</StartAddress>
					<ControlByte>32</ControlByte>
					<Enable>true</Enable>
					<Pdo>6656</Pdo>
				</Sm3>
				<RxPdo Fixed="false" Mandatory="false" Sm="2" Virtual="false">
					<Index>#x1600</Index>
					<Name LcId="1033">1st RxPdo mapping</Name>
					<Exclude>#x1601</Exclude>
					<Exclude>#x1602</Exclude>
					<Exclude>#x1603</Exclude>
					<Exclude>#x1604</Exclude>
					<Entry Fixed="false">
						<Index>#x6040</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Control Word</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6060</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>8</BitLen>
						<Name LcId="1033">Modes of operation </Name>
						<Comment />
						<DataType>SINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x607a</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Target position</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60b8</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Touch Probe Function</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
				</RxPdo>
				<TxPdo Fixed="false" Mandatory="false" Sm="3" Virtual="false">
					<Index>#x1a00</Index>
					<Name LcId="1033">1st TxPdo mapping</Name>
					<Exclude>#x1a01</Exclude>
					<Exclude>#x1a02</Exclude>
					<Exclude>#x1a03</Exclude>
					<Exclude>#x1a04</Exclude>
					<Entry Fixed="false">
						<Index>#x603f</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Error Code</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6041</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Status Word</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6064</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Position actual value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6061</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>8</BitLen>
						<Name LcId="1033">Modes of operation display</Name>
						<Comment />
						<DataType>SINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60b9</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Touch probe status</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60ba</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Touch probe pos1 pos value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60f4</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Following error actual value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60fd</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Digital inputs</Name>
						<Comment />
						<DataType>UDINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
				</TxPdo>
			</ProcessData>
			<Mailbox>
				<Send>
					<Start>4096</Start>
					<Length>128</Length>
				</Send>
				<Recv>
					<Start>5120</Start>
					<Length>128</Length>
				</Recv>
				<Protocol>CoE</Protocol>
				<CoE PdoCheck="0">


<Slave>
			<Info>
				<Name>HCFA Y7 Servo Driver</Name>
				<VendorId>71367</VendorId>
				<ProductCode>8258562</ProductCode>
				<RevisionNo>1</RevisionNo>
				<SerialNo>-1</SerialNo>
				<DevType>1</DevType>
				<NChannel>1</NChannel>
				<UseLrdLwr>0</UseLrdLwr>
				<SlaveId>1005</SlaveId>
			</Info>
			<ProcessData PdoAssign="true" PdoConfig="true">
				<Sm2>
					<Type>Outputs</Type>
					<MinSize>0</MinSize>
					<MaxSize>128</MaxSize>
					<StartAddress>6144</StartAddress>
					<ControlByte>100</ControlByte>
					<Enable>true</Enable>
					<Pdo>5632</Pdo>
				</Sm2>
				<Sm3>
					<Type>Inputs</Type>
					<MinSize>0</MinSize>
					<MaxSize>128</MaxSize>
					<StartAddress>8192</StartAddress>
					<ControlByte>32</ControlByte>
					<Enable>true</Enable>
					<Pdo>6656</Pdo>
				</Sm3>
				<RxPdo Fixed="false" Mandatory="false" Sm="2" Virtual="false">
					<Index>#x1600</Index>
					<Name LcId="1033">1st RxPdo mapping</Name>
					<Exclude>#x1601</Exclude>
					<Exclude>#x1602</Exclude>
					<Exclude>#x1603</Exclude>
					<Exclude>#x1604</Exclude>
					<Entry Fixed="false">
						<Index>#x6040</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Control Word</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6060</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>8</BitLen>
						<Name LcId="1033">Modes of operation </Name>
						<Comment />
						<DataType>SINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x607a</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Target position</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60b8</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Touch Probe Function</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
				</RxPdo>
				<TxPdo Fixed="false" Mandatory="false" Sm="3" Virtual="false">
					<Index>#x1a00</Index>
					<Name LcId="1033">1st TxPdo mapping</Name>
					<Exclude>#x1a01</Exclude>
					<Exclude>#x1a02</Exclude>
					<Exclude>#x1a03</Exclude>
					<Exclude>#x1a04</Exclude>
					<Entry Fixed="false">
						<Index>#x603f</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Error Code</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6041</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Status Word</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6064</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Position actual value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6061</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>8</BitLen>
						<Name LcId="1033">Modes of operation display</Name>
						<Comment />
						<DataType>SINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60b9</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Touch probe status</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60ba</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Touch probe pos1 pos value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60f4</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Following error actual value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60fd</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Digital inputs</Name>
						<Comment />
						<DataType>UDINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
				</TxPdo>
			</ProcessData>
			<Mailbox>
				<Send>
					<Start>4096</Start>
					<Length>128</Length>
				</Send>
				<Recv>
					<Start>5120</Start>
					<Length>128</Length>
				</Recv>
				<Protocol>CoE</Protocol>
				<CoE PdoCheck="0">


<Slave>
			<Info>
				<Name>HCFA Y7 Servo Driver</Name>
				<VendorId>71367</VendorId>
				<ProductCode>8258562</ProductCode>
				<RevisionNo>1</RevisionNo>
				<SerialNo>-1</SerialNo>
				<DevType>1</DevType>
				<NChannel>1</NChannel>
				<UseLrdLwr>0</UseLrdLwr>
				<SlaveId>1006</SlaveId>
			</Info>
			<ProcessData PdoAssign="true" PdoConfig="true">
				<Sm2>
					<Type>Outputs</Type>
					<MinSize>0</MinSize>
					<MaxSize>128</MaxSize>
					<StartAddress>6144</StartAddress>
					<ControlByte>100</ControlByte>
					<Enable>true</Enable>
					<Pdo>5632</Pdo>
				</Sm2>
				<Sm3>
					<Type>Inputs</Type>
					<MinSize>0</MinSize>
					<MaxSize>128</MaxSize>
					<StartAddress>8192</StartAddress>
					<ControlByte>32</ControlByte>
					<Enable>true</Enable>
					<Pdo>6656</Pdo>
				</Sm3>
				<RxPdo Fixed="false" Mandatory="false" Sm="2" Virtual="false">
					<Index>#x1600</Index>
					<Name LcId="1033">1st RxPdo mapping</Name>
					<Exclude>#x1601</Exclude>
					<Exclude>#x1602</Exclude>
					<Exclude>#x1603</Exclude>
					<Exclude>#x1604</Exclude>
					<Entry Fixed="false">
						<Index>#x6040</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Control Word</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6060</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>8</BitLen>
						<Name LcId="1033">Modes of operation </Name>
						<Comment />
						<DataType>SINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x607a</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Target position</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60b8</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Touch Probe Function</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
				</RxPdo>
				<TxPdo Fixed="false" Mandatory="false" Sm="3" Virtual="false">
					<Index>#x1a00</Index>
					<Name LcId="1033">1st TxPdo mapping</Name>
					<Exclude>#x1a01</Exclude>
					<Exclude>#x1a02</Exclude>
					<Exclude>#x1a03</Exclude>
					<Exclude>#x1a04</Exclude>
					<Entry Fixed="false">
						<Index>#x603f</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Error Code</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6041</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Status Word</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6064</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Position actual value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6061</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>8</BitLen>
						<Name LcId="1033">Modes of operation display</Name>
						<Comment />
						<DataType>SINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60b9</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Touch probe status</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60ba</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Touch probe pos1 pos value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60f4</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Following error actual value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60fd</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Digital inputs</Name>
						<Comment />
						<DataType>UDINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
				</TxPdo>
			</ProcessData>
			<Mailbox>
				<Send>
					<Start>4096</Start>
					<Length>128</Length>
				</Send>
				<Recv>
					<Start>5120</Start>
					<Length>128</Length>
				</Recv>
				<Protocol>CoE</Protocol>
				<CoE PdoCheck="0">


<Slave>
			<Info>
				<Name>HCFA Y7 Servo Driver</Name>
				<VendorId>71367</VendorId>
				<ProductCode>8258562</ProductCode>
				<RevisionNo>1</RevisionNo>
				<SerialNo>-1</SerialNo>
				<DevType>1</DevType>
				<NChannel>1</NChannel>
				<UseLrdLwr>0</UseLrdLwr>
				<SlaveId>1007</SlaveId>
			</Info>
			<ProcessData PdoAssign="true" PdoConfig="true">
				<Sm2>
					<Type>Outputs</Type>
					<MinSize>0</MinSize>
					<MaxSize>128</MaxSize>
					<StartAddress>6144</StartAddress>
					<ControlByte>100</ControlByte>
					<Enable>true</Enable>
					<Pdo>5632</Pdo>
				</Sm2>
				<Sm3>
					<Type>Inputs</Type>
					<MinSize>0</MinSize>
					<MaxSize>128</MaxSize>
					<StartAddress>8192</StartAddress>
					<ControlByte>32</ControlByte>
					<Enable>true</Enable>
					<Pdo>6656</Pdo>
				</Sm3>
				<RxPdo Fixed="false" Mandatory="false" Sm="2" Virtual="false">
					<Index>#x1600</Index>
					<Name LcId="1033">1st RxPdo mapping</Name>
					<Exclude>#x1601</Exclude>
					<Exclude>#x1602</Exclude>
					<Exclude>#x1603</Exclude>
					<Exclude>#x1604</Exclude>
					<Entry Fixed="false">
						<Index>#x6040</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Control Word</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6060</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>8</BitLen>
						<Name LcId="1033">Modes of operation </Name>
						<Comment />
						<DataType>SINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x607a</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Target position</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60b8</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Touch Probe Function</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
				</RxPdo>
				<TxPdo Fixed="false" Mandatory="false" Sm="3" Virtual="false">
					<Index>#x1a00</Index>
					<Name LcId="1033">1st TxPdo mapping</Name>
					<Exclude>#x1a01</Exclude>
					<Exclude>#x1a02</Exclude>
					<Exclude>#x1a03</Exclude>
					<Exclude>#x1a04</Exclude>
					<Entry Fixed="false">
						<Index>#x603f</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Error Code</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6041</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Status Word</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6064</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Position actual value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6061</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>8</BitLen>
						<Name LcId="1033">Modes of operation display</Name>
						<Comment />
						<DataType>SINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60b9</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Touch probe status</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60ba</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Touch probe pos1 pos value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60f4</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Following error actual value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60fd</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Digital inputs</Name>
						<Comment />
						<DataType>UDINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
				</TxPdo>
			</ProcessData>
			<Mailbox>
				<Send>
					<Start>4096</Start>
					<Length>128</Length>
				</Send>
				<Recv>
					<Start>5120</Start>
					<Length>128</Length>
				</Recv>
				<Protocol>CoE</Protocol>
				<CoE PdoCheck="0">

<Slave>
			<Info>
				<Name>HCFA Y7 Servo Driver</Name>
				<VendorId>71367</VendorId>
				<ProductCode>8258562</ProductCode>
				<RevisionNo>1</RevisionNo>
				<SerialNo>-1</SerialNo>
				<DevType>1</DevType>
				<NChannel>1</NChannel>
				<UseLrdLwr>0</UseLrdLwr>
				<SlaveId>1008</SlaveId>
			</Info>
			<ProcessData PdoAssign="true" PdoConfig="true">
				<Sm2>
					<Type>Outputs</Type>
					<MinSize>0</MinSize>
					<MaxSize>128</MaxSize>
					<StartAddress>6144</StartAddress>
					<ControlByte>100</ControlByte>
					<Enable>true</Enable>
					<Pdo>5632</Pdo>
				</Sm2>
				<Sm3>
					<Type>Inputs</Type>
					<MinSize>0</MinSize>
					<MaxSize>128</MaxSize>
					<StartAddress>8192</StartAddress>
					<ControlByte>32</ControlByte>
					<Enable>true</Enable>
					<Pdo>6656</Pdo>
				</Sm3>
				<RxPdo Fixed="false" Mandatory="false" Sm="2" Virtual="false">
					<Index>#x1600</Index>
					<Name LcId="1033">1st RxPdo mapping</Name>
					<Exclude>#x1601</Exclude>
					<Exclude>#x1602</Exclude>
					<Exclude>#x1603</Exclude>
					<Exclude>#x1604</Exclude>
					<Entry Fixed="false">
						<Index>#x6040</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Control Word</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6060</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>8</BitLen>
						<Name LcId="1033">Modes of operation </Name>
						<Comment />
						<DataType>SINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x607a</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Target position</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60b8</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Touch Probe Function</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
				</RxPdo>
				<TxPdo Fixed="false" Mandatory="false" Sm="3" Virtual="false">
					<Index>#x1a00</Index>
					<Name LcId="1033">1st TxPdo mapping</Name>
					<Exclude>#x1a01</Exclude>
					<Exclude>#x1a02</Exclude>
					<Exclude>#x1a03</Exclude>
					<Exclude>#x1a04</Exclude>
					<Entry Fixed="false">
						<Index>#x603f</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Error Code</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6041</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Status Word</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6064</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Position actual value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6061</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>8</BitLen>
						<Name LcId="1033">Modes of operation display</Name>
						<Comment />
						<DataType>SINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60b9</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Touch probe status</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60ba</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Touch probe pos1 pos value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60f4</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Following error actual value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60fd</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Digital inputs</Name>
						<Comment />
						<DataType>UDINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
				</TxPdo>
			</ProcessData>
			<Mailbox>
				<Send>
					<Start>4096</Start>
					<Length>128</Length>
				</Send>
				<Recv>
					<Start>5120</Start>
					<Length>128</Length>
				</Recv>
				<Protocol>CoE</Protocol>
				<CoE PdoCheck="0">

				
					<InitCmds>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear sm pdos 0x1c12</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7186</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear sm pdos 0x1c13</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7187</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear pdo 0x1a00 entriesX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>1</SubIndex>
							<Data>10003F60</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>2</SubIndex>
							<Data>10004160</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>3</SubIndex>
							<Data>20006460</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>4</SubIndex>
							<Data>08006160</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>5</SubIndex>
							<Data>1000B960</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>6</SubIndex>
							<Data>2000BA60</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>7</SubIndex>
							<Data>2000F460</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>8</SubIndex>
							<Data>2000FD60</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entry countX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>0</SubIndex>
							<Data>08</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear pdo 0x1600 entriesX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>1</SubIndex>
							<Data>10004060</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>2</SubIndex>
							<Data>08006060</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>3</SubIndex>
							<Data>20007A60</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>4</SubIndex>
							<Data>1000B860</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entry countX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>0</SubIndex>
							<Data>04</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdos 0x1C12:01 index</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7186</Index>
							<SubIndex>1</SubIndex>
							<Data>0016</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1C12 count</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7186</Index>
							<SubIndex>0</SubIndex>
							<Data>01</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdos 0x1C13:01 index</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7187</Index>
							<SubIndex>1</SubIndex>
							<Data>001A</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1C13 count</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7187</Index>
							<SubIndex>0</SubIndex>
							<Data>01</Data>
						</InitCmd>
						<InitCmd Fixed="false">
							<Transition>PS</Transition>
							<Comment />
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>24672</Index>
							<SubIndex>0</SubIndex>
							<Data>08</Data>
						</InitCmd>
					</InitCmds>
				</CoE>
			</Mailbox>
			<DC>
				<AssignActivate>768</AssignActivate>
				<CycleTime0>1000000</CycleTime0>
				<CycleTime1>0</CycleTime1>
				<ShiftTime0>0</ShiftTime0>
				<ShiftTime1>0</ShiftTime1>
			</DC>
			<IOKeep>
				<Enable>0</Enable>
				<Type>sdo</Type>
			</IOKeep>
		</Slave>
		<Slave>
			<Info>
				<Name>HCFA Y7 Servo Driver</Name>
				<VendorId>71367</VendorId>
				<ProductCode>8258562</ProductCode>
				<RevisionNo>1</RevisionNo>
				<SerialNo>-1</SerialNo>
				<DevType>1</DevType>
				<NChannel>1</NChannel>
				<UseLrdLwr>0</UseLrdLwr>
				<SlaveId>1002</SlaveId>
			</Info>
			<ProcessData PdoAssign="true" PdoConfig="true">
				<Sm2>
					<Type>Outputs</Type>
					<MinSize>0</MinSize>
					<MaxSize>128</MaxSize>
					<StartAddress>6144</StartAddress>
					<ControlByte>100</ControlByte>
					<Enable>true</Enable>
					<Pdo>5632</Pdo>
				</Sm2>
				<Sm3>
					<Type>Inputs</Type>
					<MinSize>0</MinSize>
					<MaxSize>128</MaxSize>
					<StartAddress>8192</StartAddress>
					<ControlByte>32</ControlByte>
					<Enable>true</Enable>
					<Pdo>6656</Pdo>
				</Sm3>
				<RxPdo Fixed="false" Mandatory="false" Sm="2" Virtual="false">
					<Index>#x1600</Index>
					<Name LcId="1033">1st RxPdo mapping</Name>
					<Exclude>#x1601</Exclude>
					<Exclude>#x1602</Exclude>
					<Exclude>#x1603</Exclude>
					<Exclude>#x1604</Exclude>
					<Entry Fixed="false">
						<Index>#x6040</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Control Word</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6060</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>8</BitLen>
						<Name LcId="1033">Modes of operation </Name>
						<Comment />
						<DataType>SINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x607a</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Target position</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60b8</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Touch Probe Function</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
				</RxPdo>
				<TxPdo Fixed="false" Mandatory="false" Sm="3" Virtual="false">
					<Index>#x1a00</Index>
					<Name LcId="1033">1st TxPdo mapping</Name>
					<Exclude>#x1a01</Exclude>
					<Exclude>#x1a02</Exclude>
					<Exclude>#x1a03</Exclude>
					<Exclude>#x1a04</Exclude>
					<Entry Fixed="false">
						<Index>#x603f</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Error Code</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6041</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Status Word</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6064</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Position actual value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6061</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>8</BitLen>
						<Name LcId="1033">Modes of operation display</Name>
						<Comment />
						<DataType>SINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60b9</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Touch probe status</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60ba</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Touch probe pos1 pos value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60f4</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Following error actual value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60fd</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Digital inputs</Name>
						<Comment />
						<DataType>UDINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
				</TxPdo>
			</ProcessData>
			<Mailbox>
				<Send>
					<Start>4096</Start>
					<Length>128</Length>
				</Send>
				<Recv>
					<Start>5120</Start>
					<Length>128</Length>
				</Recv>
				<Protocol>CoE</Protocol>
				<CoE PdoCheck="0">
					<InitCmds>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear sm pdos 0x1c12</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7186</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear sm pdos 0x1c13</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7187</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear pdo 0x1a00 entriesX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>1</SubIndex>
							<Data>10003F60</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>2</SubIndex>
							<Data>10004160</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>3</SubIndex>
							<Data>20006460</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>4</SubIndex>
							<Data>08006160</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>5</SubIndex>
							<Data>1000B960</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>6</SubIndex>
							<Data>2000BA60</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>7</SubIndex>
							<Data>2000F460</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>8</SubIndex>
							<Data>2000FD60</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entry countX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>0</SubIndex>
							<Data>08</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear pdo 0x1600 entriesX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>1</SubIndex>
							<Data>10004060</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>2</SubIndex>
							<Data>08006060</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>3</SubIndex>
							<Data>20007A60</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>4</SubIndex>
							<Data>1000B860</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entry countX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>0</SubIndex>
							<Data>04</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdos 0x1C12:01 index</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7186</Index>
							<SubIndex>1</SubIndex>
							<Data>0016</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1C12 count</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7186</Index>
							<SubIndex>0</SubIndex>
							<Data>01</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdos 0x1C13:01 index</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7187</Index>
							<SubIndex>1</SubIndex>
							<Data>001A</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1C13 count</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7187</Index>
							<SubIndex>0</SubIndex>
							<Data>01</Data>
						</InitCmd>
						<InitCmd Fixed="false">
							<Transition>PS</Transition>
							<Comment />
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>24672</Index>
							<SubIndex>0</SubIndex>
							<Data>08</Data>
						</InitCmd>
					</InitCmds>
				</CoE>
			</Mailbox>
			<DC>
				<AssignActivate>768</AssignActivate>
				<CycleTime0>1000000</CycleTime0>
				<CycleTime1>0</CycleTime1>
				<ShiftTime0>0</ShiftTime0>
				<ShiftTime1>0</ShiftTime1>
			</DC>
			<IOKeep>
				<Enable>0</Enable>
				<Type>sdo</Type>
			</IOKeep>
		</Slave>
		<Slave>
			<Info>
				<Name>HCFA Y7 Servo Driver</Name>
				<VendorId>71367</VendorId>
				<ProductCode>8258562</ProductCode>
				<RevisionNo>1</RevisionNo>
				<SerialNo>-1</SerialNo>
				<DevType>1</DevType>
				<NChannel>1</NChannel>
				<UseLrdLwr>0</UseLrdLwr>
				<SlaveId>1003</SlaveId>
			</Info>
			<ProcessData PdoAssign="true" PdoConfig="true">
				<Sm2>
					<Type>Outputs</Type>
					<MinSize>0</MinSize>
					<MaxSize>128</MaxSize>
					<StartAddress>6144</StartAddress>
					<ControlByte>100</ControlByte>
					<Enable>true</Enable>
					<Pdo>5632</Pdo>
				</Sm2>
				<Sm3>
					<Type>Inputs</Type>
					<MinSize>0</MinSize>
					<MaxSize>128</MaxSize>
					<StartAddress>8192</StartAddress>
					<ControlByte>32</ControlByte>
					<Enable>true</Enable>
					<Pdo>6656</Pdo>
				</Sm3>
				<RxPdo Fixed="false" Mandatory="false" Sm="2" Virtual="false">
					<Index>#x1600</Index>
					<Name LcId="1033">1st RxPdo mapping</Name>
					<Exclude>#x1601</Exclude>
					<Exclude>#x1602</Exclude>
					<Exclude>#x1603</Exclude>
					<Exclude>#x1604</Exclude>
					<Entry Fixed="false">
						<Index>#x6040</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Control Word</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6060</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>8</BitLen>
						<Name LcId="1033">Modes of operation </Name>
						<Comment />
						<DataType>SINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x607a</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Target position</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60b8</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Touch Probe Function</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
				</RxPdo>
				<TxPdo Fixed="false" Mandatory="false" Sm="3" Virtual="false">
					<Index>#x1a00</Index>
					<Name LcId="1033">1st TxPdo mapping</Name>
					<Exclude>#x1a01</Exclude>
					<Exclude>#x1a02</Exclude>
					<Exclude>#x1a03</Exclude>
					<Exclude>#x1a04</Exclude>
					<Entry Fixed="false">
						<Index>#x603f</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Error Code</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6041</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Status Word</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6064</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Position actual value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6061</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>8</BitLen>
						<Name LcId="1033">Modes of operation display</Name>
						<Comment />
						<DataType>SINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60b9</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Touch probe status</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60ba</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Touch probe pos1 pos value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60f4</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Following error actual value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60fd</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Digital inputs</Name>
						<Comment />
						<DataType>UDINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
				</TxPdo>
			</ProcessData>
			<Mailbox>
				<Send>
					<Start>4096</Start>
					<Length>128</Length>
				</Send>
				<Recv>
					<Start>5120</Start>
					<Length>128</Length>
				</Recv>
				<Protocol>CoE</Protocol>
				<CoE PdoCheck="0">
					<InitCmds>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear sm pdos 0x1c12</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7186</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear sm pdos 0x1c13</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7187</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear pdo 0x1a00 entriesX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>1</SubIndex>
							<Data>10003F60</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>2</SubIndex>
							<Data>10004160</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>3</SubIndex>
							<Data>20006460</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>4</SubIndex>
							<Data>08006160</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>5</SubIndex>
							<Data>1000B960</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>6</SubIndex>
							<Data>2000BA60</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>7</SubIndex>
							<Data>2000F460</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>8</SubIndex>
							<Data>2000FD60</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entry countX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>0</SubIndex>
							<Data>08</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear pdo 0x1600 entriesX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>1</SubIndex>
							<Data>10004060</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>2</SubIndex>
							<Data>08006060</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>3</SubIndex>
							<Data>20007A60</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>4</SubIndex>
							<Data>1000B860</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entry countX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>0</SubIndex>
							<Data>04</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdos 0x1C12:01 index</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7186</Index>
							<SubIndex>1</SubIndex>
							<Data>0016</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1C12 count</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7186</Index>
							<SubIndex>0</SubIndex>
							<Data>01</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdos 0x1C13:01 index</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7187</Index>
							<SubIndex>1</SubIndex>
							<Data>001A</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1C13 count</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7187</Index>
							<SubIndex>0</SubIndex>
							<Data>01</Data>
						</InitCmd>
						<InitCmd Fixed="false">
							<Transition>PS</Transition>
							<Comment />
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>24672</Index>
							<SubIndex>0</SubIndex>
							<Data>08</Data>
						</InitCmd>
					</InitCmds>
				</CoE>
			</Mailbox>
			<DC>
				<AssignActivate>768</AssignActivate>
				<CycleTime0>1000000</CycleTime0>
				<CycleTime1>0</CycleTime1>
				<ShiftTime0>0</ShiftTime0>
				<ShiftTime1>0</ShiftTime1>
			</DC>
			<IOKeep>
				<Enable>0</Enable>
				<Type>sdo</Type>
			</IOKeep>
		</Slave>
		<Slave>
			<Info>
				<Name>HCFA Y7 Servo Driver</Name>
				<VendorId>71367</VendorId>
				<ProductCode>8258562</ProductCode>
				<RevisionNo>1</RevisionNo>
				<SerialNo>-1</SerialNo>
				<DevType>1</DevType>
				<NChannel>1</NChannel>
				<UseLrdLwr>0</UseLrdLwr>
				<SlaveId>1004</SlaveId>
			</Info>
			<ProcessData PdoAssign="true" PdoConfig="true">
				<Sm2>
					<Type>Outputs</Type>
					<MinSize>0</MinSize>
					<MaxSize>128</MaxSize>
					<StartAddress>6144</StartAddress>
					<ControlByte>100</ControlByte>
					<Enable>true</Enable>
					<Pdo>5632</Pdo>
				</Sm2>
				<Sm3>
					<Type>Inputs</Type>
					<MinSize>0</MinSize>
					<MaxSize>128</MaxSize>
					<StartAddress>8192</StartAddress>
					<ControlByte>32</ControlByte>
					<Enable>true</Enable>
					<Pdo>6656</Pdo>
				</Sm3>
				<RxPdo Fixed="false" Mandatory="false" Sm="2" Virtual="false">
					<Index>#x1600</Index>
					<Name LcId="1033">1st RxPdo mapping</Name>
					<Exclude>#x1601</Exclude>
					<Exclude>#x1602</Exclude>
					<Exclude>#x1603</Exclude>
					<Exclude>#x1604</Exclude>
					<Entry Fixed="false">
						<Index>#x6040</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Control Word</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6060</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>8</BitLen>
						<Name LcId="1033">Modes of operation </Name>
						<Comment />
						<DataType>SINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x607a</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Target position</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60b8</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Touch Probe Function</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
				</RxPdo>
				<TxPdo Fixed="false" Mandatory="false" Sm="3" Virtual="false">
					<Index>#x1a00</Index>
					<Name LcId="1033">1st TxPdo mapping</Name>
					<Exclude>#x1a01</Exclude>
					<Exclude>#x1a02</Exclude>
					<Exclude>#x1a03</Exclude>
					<Exclude>#x1a04</Exclude>
					<Entry Fixed="false">
						<Index>#x603f</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Error Code</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6041</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Status Word</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6064</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Position actual value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6061</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>8</BitLen>
						<Name LcId="1033">Modes of operation display</Name>
						<Comment />
						<DataType>SINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60b9</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Touch probe status</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60ba</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Touch probe pos1 pos value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60f4</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Following error actual value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60fd</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Digital inputs</Name>
						<Comment />
						<DataType>UDINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
				</TxPdo>
			</ProcessData>
			<Mailbox>
				<Send>
					<Start>4096</Start>
					<Length>128</Length>
				</Send>
				<Recv>
					<Start>5120</Start>
					<Length>128</Length>
				</Recv>
				<Protocol>CoE</Protocol>
				<CoE PdoCheck="0">
					<InitCmds>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear sm pdos 0x1c12</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7186</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear sm pdos 0x1c13</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7187</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear pdo 0x1a00 entriesX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>1</SubIndex>
							<Data>10003F60</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>2</SubIndex>
							<Data>10004160</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>3</SubIndex>
							<Data>20006460</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>4</SubIndex>
							<Data>08006160</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>5</SubIndex>
							<Data>1000B960</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>6</SubIndex>
							<Data>2000BA60</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>7</SubIndex>
							<Data>2000F460</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>8</SubIndex>
							<Data>2000FD60</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entry countX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>0</SubIndex>
							<Data>08</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear pdo 0x1600 entriesX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>1</SubIndex>
							<Data>10004060</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>2</SubIndex>
							<Data>08006060</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>3</SubIndex>
							<Data>20007A60</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>4</SubIndex>
							<Data>1000B860</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entry countX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>0</SubIndex>
							<Data>04</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdos 0x1C12:01 index</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7186</Index>
							<SubIndex>1</SubIndex>
							<Data>0016</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1C12 count</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7186</Index>
							<SubIndex>0</SubIndex>
							<Data>01</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdos 0x1C13:01 index</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7187</Index>
							<SubIndex>1</SubIndex>
							<Data>001A</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1C13 count</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7187</Index>
							<SubIndex>0</SubIndex>
							<Data>01</Data>
						</InitCmd>
						<InitCmd Fixed="false">
							<Transition>PS</Transition>
							<Comment />
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>24672</Index>
							<SubIndex>0</SubIndex>
							<Data>08</Data>
						</InitCmd>
					</InitCmds>
				</CoE>
			</Mailbox>
			<DC>
				<AssignActivate>768</AssignActivate>
				<CycleTime0>1000000</CycleTime0>
				<CycleTime1>0</CycleTime1>
				<ShiftTime0>0</ShiftTime0>
				<ShiftTime1>0</ShiftTime1>
			</DC>
			<IOKeep>
				<Enable>0</Enable>
				<Type>sdo</Type>
			</IOKeep>
		</Slave>
		<Slave>
			<Info>
				<Name>HCFA Y7 Servo Driver</Name>
				<VendorId>71367</VendorId>
				<ProductCode>8258562</ProductCode>
				<RevisionNo>1</RevisionNo>
				<SerialNo>-1</SerialNo>
				<DevType>1</DevType>
				<NChannel>1</NChannel>
				<UseLrdLwr>0</UseLrdLwr>
				<SlaveId>1005</SlaveId>
			</Info>
			<ProcessData PdoAssign="true" PdoConfig="true">
				<Sm2>
					<Type>Outputs</Type>
					<MinSize>0</MinSize>
					<MaxSize>128</MaxSize>
					<StartAddress>6144</StartAddress>
					<ControlByte>100</ControlByte>
					<Enable>true</Enable>
					<Pdo>5632</Pdo>
				</Sm2>
				<Sm3>
					<Type>Inputs</Type>
					<MinSize>0</MinSize>
					<MaxSize>128</MaxSize>
					<StartAddress>8192</StartAddress>
					<ControlByte>32</ControlByte>
					<Enable>true</Enable>
					<Pdo>6656</Pdo>
				</Sm3>
				<RxPdo Fixed="false" Mandatory="false" Sm="2" Virtual="false">
					<Index>#x1600</Index>
					<Name LcId="1033">1st RxPdo mapping</Name>
					<Exclude>#x1601</Exclude>
					<Exclude>#x1602</Exclude>
					<Exclude>#x1603</Exclude>
					<Exclude>#x1604</Exclude>
					<Entry Fixed="false">
						<Index>#x6040</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Control Word</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6060</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>8</BitLen>
						<Name LcId="1033">Modes of operation </Name>
						<Comment />
						<DataType>SINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x607a</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Target position</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60b8</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Touch Probe Function</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
				</RxPdo>
				<TxPdo Fixed="false" Mandatory="false" Sm="3" Virtual="false">
					<Index>#x1a00</Index>
					<Name LcId="1033">1st TxPdo mapping</Name>
					<Exclude>#x1a01</Exclude>
					<Exclude>#x1a02</Exclude>
					<Exclude>#x1a03</Exclude>
					<Exclude>#x1a04</Exclude>
					<Entry Fixed="false">
						<Index>#x603f</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Error Code</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6041</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Status Word</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6064</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Position actual value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6061</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>8</BitLen>
						<Name LcId="1033">Modes of operation display</Name>
						<Comment />
						<DataType>SINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60b9</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Touch probe status</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60ba</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Touch probe pos1 pos value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60f4</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Following error actual value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60fd</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Digital inputs</Name>
						<Comment />
						<DataType>UDINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
				</TxPdo>
			</ProcessData>
			<Mailbox>
				<Send>
					<Start>4096</Start>
					<Length>128</Length>
				</Send>
				<Recv>
					<Start>5120</Start>
					<Length>128</Length>
				</Recv>
				<Protocol>CoE</Protocol>
				<CoE PdoCheck="0">
					<InitCmds>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear sm pdos 0x1c12</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7186</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear sm pdos 0x1c13</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7187</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear pdo 0x1a00 entriesX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>1</SubIndex>
							<Data>10003F60</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>2</SubIndex>
							<Data>10004160</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>3</SubIndex>
							<Data>20006460</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>4</SubIndex>
							<Data>08006160</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>5</SubIndex>
							<Data>1000B960</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>6</SubIndex>
							<Data>2000BA60</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>7</SubIndex>
							<Data>2000F460</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>8</SubIndex>
							<Data>2000FD60</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entry countX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>0</SubIndex>
							<Data>08</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear pdo 0x1600 entriesX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>1</SubIndex>
							<Data>10004060</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>2</SubIndex>
							<Data>08006060</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>3</SubIndex>
							<Data>20007A60</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>4</SubIndex>
							<Data>1000B860</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entry countX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>0</SubIndex>
							<Data>04</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdos 0x1C12:01 index</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7186</Index>
							<SubIndex>1</SubIndex>
							<Data>0016</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1C12 count</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7186</Index>
							<SubIndex>0</SubIndex>
							<Data>01</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdos 0x1C13:01 index</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7187</Index>
							<SubIndex>1</SubIndex>
							<Data>001A</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1C13 count</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7187</Index>
							<SubIndex>0</SubIndex>
							<Data>01</Data>
						</InitCmd>
						<InitCmd Fixed="false">
							<Transition>PS</Transition>
							<Comment />
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>24672</Index>
							<SubIndex>0</SubIndex>
							<Data>08</Data>
						</InitCmd>
					</InitCmds>
				</CoE>
			</Mailbox>
			<DC>
				<AssignActivate>768</AssignActivate>
				<CycleTime0>1000000</CycleTime0>
				<CycleTime1>0</CycleTime1>
				<ShiftTime0>0</ShiftTime0>
				<ShiftTime1>0</ShiftTime1>
			</DC>
			<IOKeep>
				<Enable>0</Enable>
				<Type>sdo</Type>
			</IOKeep>
		</Slave>
		<Slave>
			<Info>
				<Name>HCFA Y7 Servo Driver</Name>
				<VendorId>71367</VendorId>
				<ProductCode>8258562</ProductCode>
				<RevisionNo>1</RevisionNo>
				<SerialNo>-1</SerialNo>
				<DevType>1</DevType>
				<NChannel>1</NChannel>
				<UseLrdLwr>0</UseLrdLwr>
				<SlaveId>1006</SlaveId>
			</Info>
			<ProcessData PdoAssign="true" PdoConfig="true">
				<Sm2>
					<Type>Outputs</Type>
					<MinSize>0</MinSize>
					<MaxSize>128</MaxSize>
					<StartAddress>6144</StartAddress>
					<ControlByte>100</ControlByte>
					<Enable>true</Enable>
					<Pdo>5632</Pdo>
				</Sm2>
				<Sm3>
					<Type>Inputs</Type>
					<MinSize>0</MinSize>
					<MaxSize>128</MaxSize>
					<StartAddress>8192</StartAddress>
					<ControlByte>32</ControlByte>
					<Enable>true</Enable>
					<Pdo>6656</Pdo>
				</Sm3>
				<RxPdo Fixed="false" Mandatory="false" Sm="2" Virtual="false">
					<Index>#x1600</Index>
					<Name LcId="1033">1st RxPdo mapping</Name>
					<Exclude>#x1601</Exclude>
					<Exclude>#x1602</Exclude>
					<Exclude>#x1603</Exclude>
					<Exclude>#x1604</Exclude>
					<Entry Fixed="false">
						<Index>#x6040</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Control Word</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6060</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>8</BitLen>
						<Name LcId="1033">Modes of operation </Name>
						<Comment />
						<DataType>SINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x607a</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Target position</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60b8</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Touch Probe Function</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
				</RxPdo>
				<TxPdo Fixed="false" Mandatory="false" Sm="3" Virtual="false">
					<Index>#x1a00</Index>
					<Name LcId="1033">1st TxPdo mapping</Name>
					<Exclude>#x1a01</Exclude>
					<Exclude>#x1a02</Exclude>
					<Exclude>#x1a03</Exclude>
					<Exclude>#x1a04</Exclude>
					<Entry Fixed="false">
						<Index>#x603f</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Error Code</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6041</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Status Word</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6064</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Position actual value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6061</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>8</BitLen>
						<Name LcId="1033">Modes of operation display</Name>
						<Comment />
						<DataType>SINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60b9</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Touch probe status</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60ba</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Touch probe pos1 pos value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60f4</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Following error actual value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60fd</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Digital inputs</Name>
						<Comment />
						<DataType>UDINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
				</TxPdo>
			</ProcessData>
			<Mailbox>
				<Send>
					<Start>4096</Start>
					<Length>128</Length>
				</Send>
				<Recv>
					<Start>5120</Start>
					<Length>128</Length>
				</Recv>
				<Protocol>CoE</Protocol>
				<CoE PdoCheck="0">
					<InitCmds>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear sm pdos 0x1c12</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7186</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear sm pdos 0x1c13</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7187</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear pdo 0x1a00 entriesX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>1</SubIndex>
							<Data>10003F60</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>2</SubIndex>
							<Data>10004160</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>3</SubIndex>
							<Data>20006460</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>4</SubIndex>
							<Data>08006160</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>5</SubIndex>
							<Data>1000B960</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>6</SubIndex>
							<Data>2000BA60</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>7</SubIndex>
							<Data>2000F460</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>8</SubIndex>
							<Data>2000FD60</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entry countX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>0</SubIndex>
							<Data>08</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear pdo 0x1600 entriesX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>1</SubIndex>
							<Data>10004060</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>2</SubIndex>
							<Data>08006060</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>3</SubIndex>
							<Data>20007A60</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>4</SubIndex>
							<Data>1000B860</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entry countX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>0</SubIndex>
							<Data>04</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdos 0x1C12:01 index</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7186</Index>
							<SubIndex>1</SubIndex>
							<Data>0016</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1C12 count</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7186</Index>
							<SubIndex>0</SubIndex>
							<Data>01</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdos 0x1C13:01 index</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7187</Index>
							<SubIndex>1</SubIndex>
							<Data>001A</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1C13 count</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7187</Index>
							<SubIndex>0</SubIndex>
							<Data>01</Data>
						</InitCmd>
						<InitCmd Fixed="false">
							<Transition>PS</Transition>
							<Comment />
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>24672</Index>
							<SubIndex>0</SubIndex>
							<Data>08</Data>
						</InitCmd>
					</InitCmds>
				</CoE>
			</Mailbox>
			<DC>
				<AssignActivate>768</AssignActivate>
				<CycleTime0>1000000</CycleTime0>
				<CycleTime1>0</CycleTime1>
				<ShiftTime0>0</ShiftTime0>
				<ShiftTime1>0</ShiftTime1>
			</DC>
			<IOKeep>
				<Enable>0</Enable>
				<Type>sdo</Type>
			</IOKeep>
		</Slave>
		<Slave>
			<Info>
				<Name>HCFA Y7 Servo Driver</Name>
				<VendorId>71367</VendorId>
				<ProductCode>8258562</ProductCode>
				<RevisionNo>1</RevisionNo>
				<SerialNo>-1</SerialNo>
				<DevType>1</DevType>
				<NChannel>1</NChannel>
				<UseLrdLwr>0</UseLrdLwr>
				<SlaveId>1007</SlaveId>
			</Info>
			<ProcessData PdoAssign="true" PdoConfig="true">
				<Sm2>
					<Type>Outputs</Type>
					<MinSize>0</MinSize>
					<MaxSize>128</MaxSize>
					<StartAddress>6144</StartAddress>
					<ControlByte>100</ControlByte>
					<Enable>true</Enable>
					<Pdo>5632</Pdo>
				</Sm2>
				<Sm3>
					<Type>Inputs</Type>
					<MinSize>0</MinSize>
					<MaxSize>128</MaxSize>
					<StartAddress>8192</StartAddress>
					<ControlByte>32</ControlByte>
					<Enable>true</Enable>
					<Pdo>6656</Pdo>
				</Sm3>
				<RxPdo Fixed="false" Mandatory="false" Sm="2" Virtual="false">
					<Index>#x1600</Index>
					<Name LcId="1033">1st RxPdo mapping</Name>
					<Exclude>#x1601</Exclude>
					<Exclude>#x1602</Exclude>
					<Exclude>#x1603</Exclude>
					<Exclude>#x1604</Exclude>
					<Entry Fixed="false">
						<Index>#x6040</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Control Word</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6060</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>8</BitLen>
						<Name LcId="1033">Modes of operation </Name>
						<Comment />
						<DataType>SINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x607a</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Target position</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60b8</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Touch Probe Function</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
				</RxPdo>
				<TxPdo Fixed="false" Mandatory="false" Sm="3" Virtual="false">
					<Index>#x1a00</Index>
					<Name LcId="1033">1st TxPdo mapping</Name>
					<Exclude>#x1a01</Exclude>
					<Exclude>#x1a02</Exclude>
					<Exclude>#x1a03</Exclude>
					<Exclude>#x1a04</Exclude>
					<Entry Fixed="false">
						<Index>#x603f</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Error Code</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6041</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Status Word</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6064</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Position actual value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x6061</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>8</BitLen>
						<Name LcId="1033">Modes of operation display</Name>
						<Comment />
						<DataType>SINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60b9</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name LcId="1033">Touch probe status</Name>
						<Comment />
						<DataType>UINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60ba</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Touch probe pos1 pos value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60f4</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Following error actual value</Name>
						<Comment />
						<DataType>DINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
					<Entry Fixed="false">
						<Index>#x60fd</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name LcId="1033">Digital inputs</Name>
						<Comment />
						<DataType>UDINT</DataType>
						<DefaultValue>0</DefaultValue>
					</Entry>
				</TxPdo>
			</ProcessData>
			<Mailbox>
				<Send>
					<Start>4096</Start>
					<Length>128</Length>
				</Send>
				<Recv>
					<Start>5120</Start>
					<Length>128</Length>
				</Recv>
				<Protocol>CoE</Protocol>
				<CoE PdoCheck="0">
					<InitCmds>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear sm pdos 0x1c12</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7186</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear sm pdos 0x1c13</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7187</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear pdo 0x1a00 entriesX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>1</SubIndex>
							<Data>10003F60</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>2</SubIndex>
							<Data>10004160</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>3</SubIndex>
							<Data>20006460</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>4</SubIndex>
							<Data>08006160</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>5</SubIndex>
							<Data>1000B960</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>6</SubIndex>
							<Data>2000BA60</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>7</SubIndex>
							<Data>2000F460</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>8</SubIndex>
							<Data>2000FD60</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1a00 entry countX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>6656</Index>
							<SubIndex>0</SubIndex>
							<Data>08</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>clear pdo 0x1600 entriesX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>0</SubIndex>
							<Data>00</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>1</SubIndex>
							<Data>10004060</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>2</SubIndex>
							<Data>08006060</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>3</SubIndex>
							<Data>20007A60</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entryX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>4</SubIndex>
							<Data>1000B860</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1600 entry countX</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>5632</Index>
							<SubIndex>0</SubIndex>
							<Data>04</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdos 0x1C12:01 index</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7186</Index>
							<SubIndex>1</SubIndex>
							<Data>0016</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1C12 count</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7186</Index>
							<SubIndex>0</SubIndex>
							<Data>01</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdos 0x1C13:01 index</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7187</Index>
							<SubIndex>1</SubIndex>
							<Data>001A</Data>
						</InitCmd>
						<InitCmd Fixed="true">
							<Transition>PS</Transition>
							<Comment>download pdo 0x1C13 count</Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>7187</Index>
							<SubIndex>0</SubIndex>
							<Data>01</Data>
						</InitCmd>
						<InitCmd Fixed="false">
							<Transition>PS</Transition>
							<Comment />
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>24672</Index>
							<SubIndex>0</SubIndex>
							<Data>08</Data>
						</InitCmd>
					</InitCmds>
				</CoE>
			</Mailbox>
			<DC>
				<AssignActivate>768</AssignActivate>
				<CycleTime0>1000000</CycleTime0>
				<CycleTime1>0</CycleTime1>
				<ShiftTime0>0</ShiftTime0>
				<ShiftTime1>0</ShiftTime1>
			</DC>
			<IOKeep>
				<Enable>0</Enable>
				<Type>sdo</Type>
			</IOKeep>
		</Slave>
	</Config>
</EtherCATConfig>
