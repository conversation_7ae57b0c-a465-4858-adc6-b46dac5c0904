sendPVTCommands 函數 (參數: const QStringList& commands)
│
├── 功能概述
│   ├── 收集和批處理多個電機的PVT命令
│   ├── 等待所有命令就緒或超時後統一執行
│   └── 按順序執行命令序列
│
├── 關鍵靜態變量
│   ├── static QMap<QString, QStringList> batchCommands ──── 批次ID → 命令列表
│   ├── static QMap<QString, QSet<QString>> batchMotors ─── 批次ID → 電機集合
│   ├── static QMap<QString, QDateTime> batchTimestamps ─── 批次ID → 時間戳
│   └── static QString currentBatchId ───────────────────── 當前批次ID (默認為空)
│
├── 執行流程
│   │
│   ├── 【批次初始化】
│   │   ├── 檢查 currentBatchId.isEmpty()
│   │   │   ├── 如果為空 → currentBatchId = QDateTime::currentDateTime().toString("yyyyMMddhhmmsszzz")
│   │   │   └── batchTimestamps[currentBatchId] = QDateTime::currentDateTime()
│   │   └── 檢查 !batchCommands.contains(currentBatchId)
│   │       ├── batchCommands[currentBatchId] = QStringList()
│   │       └── batchMotors[currentBatchId] = QSet<QString>()
│   │
│   ├── 【命令解析與收集】
│   │   ├── batchCommands[currentBatchId].append(commands) ── 添加命令到當前批次
│   │   └── 遍歷 commands 中的每個 cmd
│   │       ├── QString cmdMotorName = cmd.left(cmd.indexOf(":")).trimmed()
│   │       └── 如果 !cmdMotorName.isEmpty() → batchMotors[currentBatchId].insert(cmdMotorName)
│   │
│   ├── 【批次完成條件檢查】
│   │   ├── QSet<QString> expectedMotors = batchMotors[currentBatchId] ── 獲取預期電機集合
│   │   ├── bool allMotorsReceived = true ───────────────────────────── 預設所有電機都已接收
│   │   ├── 遍歷 expectedMotors 檢查是否都在 batchMotors[currentBatchId] 中
│   │   │   └── 如有缺失 → allMotorsReceived = false
│   │   └── bool isTimeout = batchTimestamps[currentBatchId].msecsTo(QDateTime::currentDateTime()) > 10000
│   │
│   └── 【批次處理分支】
│       │
│       ├── 如果 (allMotorsReceived || isTimeout) ── 條件滿足
│       │   │
│       │   ├── 【創建命令序列】
│       │   │   ├── CommandSequence sequence ────────────── 新建序列對象
│       │   │   ├── sequence.rawCommands = batchCommands[currentBatchId]
│       │   │   ├── sequence.currentIndex = 0
│       │   │   └── pendingSequences.append(sequence) ───── 添加到待處理隊列
│       │   │
│       │   ├── 【清理批次資源】
│       │   │   ├── batchCommands.remove(currentBatchId)
│       │   │   ├── batchMotors.remove(currentBatchId)
│       │   │   ├── batchTimestamps.remove(currentBatchId)
│       │   │   └── currentBatchId = "" ──────────────── 重置當前批次ID
│       │   │
│       │   └── 【序列執行控制】
│       │       ├── 檢查 !isExecutingCommand ────────── 是否有命令正在執行
│       │       │   ├── 如果為true → executeNextSequence() ── 立即執行
│       │       │   └── 如果為false → 註冊信號連接 ────────── 等待完成
│       │       └── connect(this, &ethercat::commandSequenceCompleted, this, [this]() {
│       │           executeNextSequence();
│       │       }, Qt::SingleShotConnection)
│       │
│       └── 否則 ── 條件不滿足，繼續等待
│           ├── QStringList collectedMotors ──────── 創建已收集電機列表
│           ├── 遍歷 batchMotors[currentBatchId] 填充 collectedMotors
│           ├── QStringList missingMotors ──────────── 創建缺失電機列表
│           └── 遍歷 expectedMotors 檢查缺失電機並添加到 missingMotors
│
├── 外部依賴
│   ├── executeNextSequence() ──── 執行待處理隊列中的下一個序列
│   ├── commandSequenceCompleted ─ 信號：當命令序列完成時發出
│   └── isExecutingCommand ─────── 成員變量：標記是否有命令正在執行
│
└── 相關數據結構
    ├── CommandSequence ─── 命令序列結構
    │   ├── QList<QString> rawCommands ── 原始命令字符串列表
    │   └── int currentIndex ──────────── 當前執行到的命令索引
    │
    └── QQueue<CommandSequence> pendingSequences ── 待處理的命令序列隊列





    executeCommandSequence 函數 (參數: const QStringList& commands)
│
├── 功能概述
│   ├── 解析並執行多軸PVT命令
│   ├── 為所有啟用的軸準備和開始PVT運動
│   └── 監控運動進度和完成狀態
│
├── 關鍵變量
│   ├── axisPVTCache ────────────────── 軸PVT數據緩存
│   ├── axisCommandQueues ───────────── 每個軸的命令佇列映射(軸號 → 命令佇列)
│   ├── activeAxes (QSet<short>) ────── 有命令需要執行的活動軸集合
│   ├── mask (short) ──────────────────── 用於批量啟動的軸掩碼
│   ├── currentTableId (short) ─────────── 當前軸使用的表單ID
│   └── result (int) ───────────────────── GTN_PvtStart的返回結果
│
├── 執行流程
│   │
│   ├── 【初始化】
│   │   └── axisPVTCache.clear() ─────────── 清除軸PVT緩存
│   │
│   ├── 【命令解析】
│   │   ├── parseCommandsToQueues(commands) ─── 解析命令到各軸佇列
│   │   └── 打印佇列信息（共有多少軸的命令）
│   │
│   ├── 【處理首個命令】
│   │   ├── QSet<short> activeAxes ─────────── 創建活動軸集合
│   │   └── 遍歷 axisCommandQueues 每個軸
│   │       ├── 獲取軸號 axis = it.key()
│   │       ├── 檢查佇列非空 !it.value().isEmpty()
│   │       │   ├── 獲取首個命令 command = it.value().dequeue()
│   │       │   ├── parseCommandToCache(command) ─── 解析命令到緩存
│   │       │   └── activeAxes.insert(axis) ───────── 添加軸到活動集合
│   │       └── 打印活動軸數量
│   │
│   ├── 【準備PVT運動】
│   │   ├── short mask = 0 ─────────────────────── 初始化掩碼
│   │   └── 遍歷每個活動軸 (for (short axis : activeAxes))
│   │       │
│   │       ├── 【清除軸錯誤】
│   │       │   ├── GTN_ClrSts(cardCore, axis) ──── 清除狀態
│   │       │   └── QThread::msleep(50) ─────────── 等待50毫秒
│   │       │
│   │       ├── 【檢查軸使能】
│   │       │   ├── long status ───────────────────── 狀態變量
│   │       │   ├── GTN_GetSts(cardCore, axis, &status) ─── 獲取狀態
│   │       │   └── 如果未使能(!(status & 0x200))
│   │       │       ├── GTN_AxisOn(cardCore, axis) ──── 使能軸
│   │       │       └── Sleep(1000) ─────────────────── 等待使能完成
│   │       │
│   │       ├── 【設置PVT模式】
│   │       │   ├── GTN_PrfPvt(cardCore, axis) ───── 設置為PVT模式
│   │       │   └── 檢查返回值，失敗則跳過該軸
│   │       │
│   │       ├── 【獲取並上傳PVT數據】
│   │       │   ├── short currentTableId = getCurrentTableId(axis) ─── 獲取表單ID
│   │       │   ├── 打印表單ID信息
│   │       │   └── uploadPVTData(axis, currentTableId) ─── 上傳PVT數據到表單
│   │       │
│   │       ├── 【選擇PVT表單】
│   │       │   ├── GTN_PvtTableSelect(cardCore, axis, currentTableId) ─── 選擇表單
│   │       │   └── 檢查返回值，失敗則跳過該軸
│   │       │
│   │       └── 【添加到啟動掩碼】
│   │           └── mask |= (1 << (axis - 1)) ─── 設置對應軸的位
│   │
│   ├── 【檢查可用軸】
│   │   └── 如果 mask == 0
│   │       └── 退出函數，報錯"沒有可用的軸可以啟動"
│   │
│   └── 【啟動與監控】
│       ├── 【啟動PVT運動】
│       │   ├── int result = GTN_PvtStart(cardCore, mask) ─── 啟動所有活動軸
│       │   └── 打印啟動結果
│       │
│       └── 【監控運動】
│           ├── 如果 result == 0（啟動成功）
│           │   └── monitorAllPVTMotions() ─── 監控所有PVT運動
│           └── 否則
│               └── 打印錯誤信息
│
├── 外部依賴
│   ├── parseCommandsToQueues() ────── 解析命令到各軸佇列
│   ├── parseCommandToCache() ─────── 解析命令到PVT緩存
│   ├── getCurrentTableId() ──────── 獲取當前表單ID
│   ├── uploadPVTData() ────────────── 上傳PVT數據
│   ├── monitorAllPVTMotions() ───── 監控所有PVT運動
│   └── 多個GTN_系列函數 ────────── 運動控制卡API
│
└── 實現要點
    ├── 處理多軸並行運動
    ├── 遵循固定流程（清錯誤 → 使能 → 設模式 → 上傳數據 → 選表單 → 啟動)
    ├── 使用表單ID機制管理PVT數據
    └── 通過位掩碼(mask)實現批量軸控制