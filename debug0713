[2025-07-14 09:37:48.575] Debug: 特殊單軸 PT 命令添加: "700W:Point0-200000；Time0-10"
[2025-07-14 09:37:48.575] Debug: [PQ_D0] processSensorCommandQueue_Entry. Queue size: 1
[2025-07-14 09:37:48.575] Debug: [PQ_D0_Detail] First command: "700W:Point0-200000；Time0-10" Type: -3
[2025-07-14 09:37:48.575] Debug: [PQ_D2] Before static isProcessing. Queue size: 1
[2025-07-14 09:37:48.575] Debug: [PQ_D3] After static isProcessing. isProcessing_Value_Before_Check: false
[2025-07-14 09:37:48.575] Debug: [PQ_D5] Set isProcessing to true.
[2025-07-14 09:37:48.575] Debug: [PQ_D6] Entering try block.
[2025-07-14 09:37:48.575] Debug: [PQ_D7] After checking for SpecialPTCommands. hasSpecialPTCommands: true Queue size: 1
[2025-07-14 09:37:48.575] Debug: [PQ_D8] Entered hasSpecialPTCommands block.
[2025-07-14 09:37:48.575] Debug: [PQ_D8a] ptCommandsToProcess count: 1
[2025-07-14 09:37:48.575] Debug: [PQ_D9] Entered !ptCommandsToProcess.isEmpty() block for batch PT.
[2025-07-14 09:37:48.575] Debug: 批量處理 1 條特殊PT命令
[2025-07-14 09:37:48.575] Debug: 
===== [SUPERPT_1] 開始處理特殊PT命令 =====
[2025-07-14 09:37:48.575] Debug: - 收到的命令： "700W:Point0-200000；Time0-10"
[2025-07-14 09:37:48.575] Debug: - 命令行數： 1
[2025-07-14 09:37:48.575] Debug: - 命令軸數： 1 ，包含的軸： QList("700W")
[2025-07-14 09:37:48.575] Debug: - 各軸命令數量：
[2025-07-14 09:37:48.575] Debug:    "700W" 軸： 1 條命令
[2025-07-14 09:37:48.575] Debug: - 目前各軸累積命令數量：
[2025-07-14 09:37:48.575] Debug:    "700W" 軸累計： 1 條命令
[2025-07-14 09:37:48.575] Debug: - 設置2秒後自動調用 SUPERPT_2 進行最終分析
[2025-07-14 09:37:48.575] Debug: 已將批量特殊PT命令發送到 ethercat 窗口，調用 SUPERPT_1 函數
[2025-07-14 09:37:48.575] Debug: 批量處理了 1 條特殊PT命令，從隊列中移除
[2025-07-14 09:37:48.575] Debug: [PQ_D10] Before isProcessing = false in PT batch. Current isProcessing: true
[2025-07-14 09:37:48.575] Debug: [PQ_D12] Returning from PT batch processing.
[2025-07-14 09:37:53.564] Debug: 
===== [SUPERPT_2] 2秒累積後的最終命令分析 =====
[2025-07-14 09:37:53.564] Debug: - 各軸累積命令總數：
[2025-07-14 09:37:53.564] Debug:    "700W" 軸累計： 1 條命令
[2025-07-14 09:37:53.564] Debug: 
- 各軸累積命令詳情：
[2025-07-14 09:37:53.564] Debug:    "700W" 軸的命令：
[2025-07-14 09:37:53.564] Debug:     命令  1 :  "Point0-200000；Time0-10"
[2025-07-14 09:37:53.564] Debug: - 分析完成，即將調用 SUPERPT_3 函數創建線程...
[2025-07-14 09:37:53.564] Debug: 
===== [SUPERPT_3] 開始為每個軸創建線程和命令隊列 =====
[2025-07-14 09:37:53.564] Debug: - 建立軸映射關係：
[2025-07-14 09:37:53.564] Debug:    "100W"  對應  1 軸
[2025-07-14 09:37:53.564] Debug:    "200W"  對應  2 軸
[2025-07-14 09:37:53.564] Debug:    "300W"  對應  3 軸
[2025-07-14 09:37:53.564] Debug:    "400W"  對應  4 軸
[2025-07-14 09:37:53.564] Debug:    "500W"  對應  5 軸
[2025-07-14 09:37:53.564] Debug:    "600W"  對應  6 軸
[2025-07-14 09:37:53.564] Debug:    "700W"  對應  7 軸
[2025-07-14 09:37:53.565] Debug: - 檢測到需要創建 1 個線程，對應 1 個軸
[2025-07-14 09:37:53.565] Debug: - 為 "700W" ( 7 軸) 創建線程，共 1 條命令
[2025-07-14 09:37:53.565] Debug: -  "700W" ( 7 軸) 線程已創建並啟動
[2025-07-14 09:37:53.565] Debug: - 所有線程已創建，開始處理每個軸的命令
[2025-07-14 09:37:53.565] Debug: 
- 線程和命令隊列統計：
[2025-07-14 09:37:53.565] Debug:    "700W" ( 7 軸)：
[2025-07-14 09:37:53.565] Debug:     - 線程ID： 0x4e38
[2025-07-14 09:37:53.565] Debug:     - 命令數量： 1 條
[2025-07-14 09:37:53.566] Debug:     - 第一條命令： "Point0-200000；Time0-10"
[2025-07-14 09:37:53.566] Debug: 
===== [SUPERPT_4] 開始處理每個軸的命令 =====
[2025-07-14 09:37:53.566] Debug: "從檔案讀取軸 1 歸零位置: 38278"
[2025-07-14 09:37:53.566] Debug: "從檔案讀取軸 2 歸零位置: 161088"
[2025-07-14 09:37:53.566] Debug: "從檔案讀取軸 3 歸零位置: 224203"
[2025-07-14 09:37:53.566] Debug: "從檔案讀取軸 4 歸零位置: -511632"
[2025-07-14 09:37:53.566] Debug: "從檔案讀取軸 5 歸零位置: 107669"
[2025-07-14 09:37:53.566] Debug: "從檔案讀取軸 6 歸零位置: 58098"
[2025-07-14 09:37:53.566] Debug: "從檔案讀取軸 7 歸零位置: -50000"
[2025-07-14 09:37:53.566] Debug: 成功從 0_pose.txt 讀取 7 個軸的歸零位置
[2025-07-14 09:37:53.566] Debug: "軸 7 【優先使用】檔案中的絕對位置: -50000"
[2025-07-14 09:37:53.566] Debug: "===== 軸 7 運動前狀態 ====="
[2025-07-14 09:37:53.566] Debug: "  歸零位置（檔案）: -50000"
[2025-07-14 09:37:53.566] Debug: "  目標位置: 150000"
[2025-07-14 09:37:53.566] Debug: "  運動距離: 200000"
[2025-07-14 09:37:53.566] Debug: ================================
[2025-07-14 09:37:53.566] Debug: "🎯 軸 7 運動計劃：從位置 -50000 運動到位置 150000"
[2025-07-14 09:37:53.566] Debug: "📏 軸 7 預計運動距離：200000 個脈衝"
[2025-07-14 09:37:53.567] Debug:   [SYNC] 已將軸 7  的 PrfPos 對齊到0_pose.txt中的歸零位置:  -50000
[2025-07-14 09:37:53.567] Debug: "  [STATUS CHECK] EncPos=-50000  PrfPos=-50000  檔案歸零位置=-50000  Status=0x200"
[2025-07-14 09:37:53.568] Debug:   [Ruckig] 軸 7  段  1  計算失敗，跳過此段
[2025-07-14 09:37:53.568] Debug: 生成了 0 個PT點
[2025-07-14 09:37:53.568] Debug: 沒有可用的PT點，退出處理