[2025-07-14 15:00:48.954] Debug: Starting setWidgetEnabled for widget: "centralwidget" with enabled: true
[2025-07-14 15:00:48.954] Debug: Processing child widget: "horizontalLayoutWidget" with enabled: true
[2025-07-14 15:00:48.954] Debug: Starting setWidgetEnabled for widget: "horizontalLayoutWidget" with enabled: true
[2025-07-14 15:00:48.954] Debug: Processing child widget: "pushButton_5" with enabled: true
[2025-07-14 15:00:48.954] Debug: Starting setWidgetEnabled for widget: "pushButton_5" with enabled: true
[2025-07-14 15:00:48.954] Debug: Finished setWidgetEnabled for widget: "pushButton_5"
[2025-07-14 15:00:48.954] Debug: Processing child widget: "pushButton_4" with enabled: true
[2025-07-14 15:00:48.954] Debug: Starting setWidgetEnabled for widget: "pushButton_4" with enabled: true
[2025-07-14 15:00:48.954] Debug: Finished setWidgetEnabled for widget: "pushButton_4"
[2025-07-14 15:00:48.954] Debug: Processing child widget: "pushButton_Open_Sensor_2" with enabled: true
[2025-07-14 15:00:48.954] Debug: Starting setWidgetEnabled for widget: "pushButton_Open_Sensor_2" with enabled: true
[2025-07-14 15:00:48.954] Debug: Finished setWidgetEnabled for widget: "pushButton_Open_Sensor_2"
[2025-07-14 15:00:48.954] Debug: Processing child widget: "pushButton_Open_Sensor_485" with enabled: true
[2025-07-14 15:00:48.954] Debug: Starting setWidgetEnabled for widget: "pushButton_Open_Sensor_485" with enabled: true
[2025-07-14 15:00:48.954] Debug: Finished setWidgetEnabled for widget: "pushButton_Open_Sensor_485"
[2025-07-14 15:00:48.954] Debug: Processing child widget: "pushButton_2Logic" with enabled: true
[2025-07-14 15:00:48.954] Debug: Starting setWidgetEnabled for widget: "pushButton_2Logic" with enabled: true
[2025-07-14 15:00:48.954] Debug: Finished setWidgetEnabled for widget: "pushButton_2Logic"
[2025-07-14 15:00:48.954] Debug: Processing child widget: "pushButton_Opendsize" with enabled: true
[2025-07-14 15:00:48.954] Debug: Starting setWidgetEnabled for widget: "pushButton_Opendsize" with enabled: true
[2025-07-14 15:00:48.954] Debug: Finished setWidgetEnabled for widget: "pushButton_Opendsize"
[2025-07-14 15:00:48.954] Debug: Processing child widget: "pushButton_EtherCAT" with enabled: true
[2025-07-14 15:00:48.954] Debug: Starting setWidgetEnabled for widget: "pushButton_EtherCAT" with enabled: true
[2025-07-14 15:00:48.954] Debug: Finished setWidgetEnabled for widget: "pushButton_EtherCAT"
[2025-07-14 15:00:48.954] Debug: Processing child widget: "openCommandSchedulerButton" with enabled: true
[2025-07-14 15:00:48.954] Debug: Starting setWidgetEnabled for widget: "openCommandSchedulerButton" with enabled: true
[2025-07-14 15:00:48.954] Debug: Finished setWidgetEnabled for widget: "openCommandSchedulerButton"
[2025-07-14 15:00:48.954] Debug: Finished setWidgetEnabled for widget: "horizontalLayoutWidget"
[2025-07-14 15:00:48.954] Debug: Processing child widget: "horizontalLayoutWidget_2" with enabled: true
[2025-07-14 15:00:48.954] Debug: Starting setWidgetEnabled for widget: "horizontalLayoutWidget_2" with enabled: true
[2025-07-14 15:00:48.954] Debug: Processing child widget: "label" with enabled: true
[2025-07-14 15:00:48.954] Debug: Starting setWidgetEnabled for widget: "label" with enabled: true
[2025-07-14 15:00:48.954] Debug: Finished setWidgetEnabled for widget: "label"
[2025-07-14 15:00:48.954] Debug: Finished setWidgetEnabled for widget: "horizontalLayoutWidget_2"
[2025-07-14 15:00:48.954] Debug: Processing child widget: "horizontalLayoutWidget_3" with enabled: true
[2025-07-14 15:00:48.954] Debug: Starting setWidgetEnabled for widget: "horizontalLayoutWidget_3" with enabled: true
[2025-07-14 15:00:48.954] Debug: Processing child widget: "label_4" with enabled: true
[2025-07-14 15:00:48.954] Debug: Starting setWidgetEnabled for widget: "label_4" with enabled: true
[2025-07-14 15:00:48.954] Debug: Finished setWidgetEnabled for widget: "label_4"
[2025-07-14 15:00:48.954] Debug: Finished setWidgetEnabled for widget: "horizontalLayoutWidget_3"
[2025-07-14 15:00:48.954] Debug: Processing child widget: "horizontalLayoutWidget_5" with enabled: true
[2025-07-14 15:00:48.954] Debug: Starting setWidgetEnabled for widget: "horizontalLayoutWidget_5" with enabled: true
[2025-07-14 15:00:48.954] Debug: Processing child widget: "label_5" with enabled: true
[2025-07-14 15:00:48.954] Debug: Starting setWidgetEnabled for widget: "label_5" with enabled: true
[2025-07-14 15:00:48.954] Debug: Finished setWidgetEnabled for widget: "label_5"
[2025-07-14 15:00:48.954] Debug: Finished setWidgetEnabled for widget: "horizontalLayoutWidget_5"
[2025-07-14 15:00:48.954] Debug: Processing child widget: "horizontalLayoutWidget_7" with enabled: true
[2025-07-14 15:00:48.954] Debug: Starting setWidgetEnabled for widget: "horizontalLayoutWidget_7" with enabled: true
[2025-07-14 15:00:48.954] Debug: Finished setWidgetEnabled for widget: "horizontalLayoutWidget_7"
[2025-07-14 15:00:48.954] Debug: Processing child widget: "scrollArea" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "scrollArea" with enabled: true
[2025-07-14 15:00:48.955] Debug: Processing child widget: "qt_scrollarea_viewport" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "qt_scrollarea_viewport" with enabled: true
[2025-07-14 15:00:48.955] Debug: Processing child widget: "scrollAreaWidgetContents" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "scrollAreaWidgetContents" with enabled: true
[2025-07-14 15:00:48.955] Debug: Processing child widget: "widget" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "widget" with enabled: true
[2025-07-14 15:00:48.955] Debug: Processing child widget: "textEdit_total_action" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "textEdit_total_action" with enabled: true
[2025-07-14 15:00:48.955] Debug: Processing child widget: "qt_scrollarea_viewport" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "qt_scrollarea_viewport" with enabled: true
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: "qt_scrollarea_viewport"
[2025-07-14 15:00:48.955] Debug: Processing child widget: "qt_scrollarea_hcontainer" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "qt_scrollarea_hcontainer" with enabled: true
[2025-07-14 15:00:48.955] Debug: Processing child widget: "" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "" with enabled: true
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: ""
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: "qt_scrollarea_hcontainer"
[2025-07-14 15:00:48.955] Debug: Processing child widget: "qt_scrollarea_vcontainer" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "qt_scrollarea_vcontainer" with enabled: true
[2025-07-14 15:00:48.955] Debug: Processing child widget: "" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "" with enabled: true
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: ""
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: "qt_scrollarea_vcontainer"
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: "textEdit_total_action"
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: "widget"
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: "scrollAreaWidgetContents"
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: "qt_scrollarea_viewport"
[2025-07-14 15:00:48.955] Debug: Processing child widget: "qt_scrollarea_hcontainer" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "qt_scrollarea_hcontainer" with enabled: true
[2025-07-14 15:00:48.955] Debug: Processing child widget: "" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "" with enabled: true
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: ""
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: "qt_scrollarea_hcontainer"
[2025-07-14 15:00:48.955] Debug: Processing child widget: "qt_scrollarea_vcontainer" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "qt_scrollarea_vcontainer" with enabled: true
[2025-07-14 15:00:48.955] Debug: Processing child widget: "" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "" with enabled: true
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: ""
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: "qt_scrollarea_vcontainer"
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: "scrollArea"
[2025-07-14 15:00:48.955] Debug: Processing child widget: "pushButton_sendorder" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "pushButton_sendorder" with enabled: true
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: "pushButton_sendorder"
[2025-07-14 15:00:48.955] Debug: Processing child widget: "scrollArea_2" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "scrollArea_2" with enabled: true
[2025-07-14 15:00:48.955] Debug: Processing child widget: "qt_scrollarea_viewport" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "qt_scrollarea_viewport" with enabled: true
[2025-07-14 15:00:48.955] Debug: Processing child widget: "scrollAreaWidgetContents_2" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "scrollAreaWidgetContents_2" with enabled: true
[2025-07-14 15:00:48.955] Debug: Processing child widget: "widget_2" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "widget_2" with enabled: true
[2025-07-14 15:00:48.955] Debug: Processing child widget: "textEdit_Hex" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "textEdit_Hex" with enabled: true
[2025-07-14 15:00:48.955] Debug: Processing child widget: "qt_scrollarea_viewport" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "qt_scrollarea_viewport" with enabled: true
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: "qt_scrollarea_viewport"
[2025-07-14 15:00:48.955] Debug: Processing child widget: "qt_scrollarea_hcontainer" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "qt_scrollarea_hcontainer" with enabled: true
[2025-07-14 15:00:48.955] Debug: Processing child widget: "" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "" with enabled: true
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: ""
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: "qt_scrollarea_hcontainer"
[2025-07-14 15:00:48.955] Debug: Processing child widget: "qt_scrollarea_vcontainer" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "qt_scrollarea_vcontainer" with enabled: true
[2025-07-14 15:00:48.955] Debug: Processing child widget: "" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "" with enabled: true
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: ""
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: "qt_scrollarea_vcontainer"
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: "textEdit_Hex"
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: "widget_2"
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: "scrollAreaWidgetContents_2"
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: "qt_scrollarea_viewport"
[2025-07-14 15:00:48.955] Debug: Processing child widget: "qt_scrollarea_hcontainer" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "qt_scrollarea_hcontainer" with enabled: true
[2025-07-14 15:00:48.955] Debug: Processing child widget: "" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "" with enabled: true
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: ""
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: "qt_scrollarea_hcontainer"
[2025-07-14 15:00:48.955] Debug: Processing child widget: "qt_scrollarea_vcontainer" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "qt_scrollarea_vcontainer" with enabled: true
[2025-07-14 15:00:48.955] Debug: Processing child widget: "" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "" with enabled: true
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: ""
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: "qt_scrollarea_vcontainer"
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: "scrollArea_2"
[2025-07-14 15:00:48.955] Debug: Processing child widget: "pushButton_8" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "pushButton_8" with enabled: true
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: "pushButton_8"
[2025-07-14 15:00:48.955] Debug: Processing child widget: "pushButton_sendMain_2" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "pushButton_sendMain_2" with enabled: true
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: "pushButton_sendMain_2"
[2025-07-14 15:00:48.955] Debug: Processing child widget: "label_6" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "label_6" with enabled: true
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: "label_6"
[2025-07-14 15:00:48.955] Debug: Processing child widget: "scrollArea_3" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "scrollArea_3" with enabled: true
[2025-07-14 15:00:48.955] Debug: Processing child widget: "qt_scrollarea_viewport" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "qt_scrollarea_viewport" with enabled: true
[2025-07-14 15:00:48.955] Debug: Processing child widget: "scrollAreaWidgetContents_3" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "scrollAreaWidgetContents_3" with enabled: true
[2025-07-14 15:00:48.955] Debug: Processing child widget: "textEdit_Muti" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "textEdit_Muti" with enabled: true
[2025-07-14 15:00:48.955] Debug: Processing child widget: "qt_scrollarea_viewport" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "qt_scrollarea_viewport" with enabled: true
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: "qt_scrollarea_viewport"
[2025-07-14 15:00:48.955] Debug: Processing child widget: "qt_scrollarea_hcontainer" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "qt_scrollarea_hcontainer" with enabled: true
[2025-07-14 15:00:48.955] Debug: Processing child widget: "" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "" with enabled: true
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: ""
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: "qt_scrollarea_hcontainer"
[2025-07-14 15:00:48.955] Debug: Processing child widget: "qt_scrollarea_vcontainer" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "qt_scrollarea_vcontainer" with enabled: true
[2025-07-14 15:00:48.955] Debug: Processing child widget: "" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "" with enabled: true
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: ""
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: "qt_scrollarea_vcontainer"
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: "textEdit_Muti"
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: "scrollAreaWidgetContents_3"
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: "qt_scrollarea_viewport"
[2025-07-14 15:00:48.955] Debug: Processing child widget: "qt_scrollarea_hcontainer" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "qt_scrollarea_hcontainer" with enabled: true
[2025-07-14 15:00:48.955] Debug: Processing child widget: "" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "" with enabled: true
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: ""
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: "qt_scrollarea_hcontainer"
[2025-07-14 15:00:48.955] Debug: Processing child widget: "qt_scrollarea_vcontainer" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "qt_scrollarea_vcontainer" with enabled: true
[2025-07-14 15:00:48.955] Debug: Processing child widget: "" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "" with enabled: true
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: ""
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: "qt_scrollarea_vcontainer"
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: "scrollArea_3"
[2025-07-14 15:00:48.955] Debug: Processing child widget: "pushButton_sendMain_3" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "pushButton_sendMain_3" with enabled: true
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: "pushButton_sendMain_3"
[2025-07-14 15:00:48.955] Debug: Processing child widget: "label_7" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "label_7" with enabled: true
[2025-07-14 15:00:48.955] Debug: Finished setWidgetEnabled for widget: "label_7"
[2025-07-14 15:00:48.955] Debug: Processing child widget: "textEdit_presend1" with enabled: true
[2025-07-14 15:00:48.955] Debug: Starting setWidgetEnabled for widget: "textEdit_presend1" with enabled: true
[2025-07-14 15:00:48.956] Debug: Processing child widget: "qt_scrollarea_viewport" with enabled: true
[2025-07-14 15:00:48.956] Debug: Starting setWidgetEnabled for widget: "qt_scrollarea_viewport" with enabled: true
[2025-07-14 15:00:48.956] Debug: Finished setWidgetEnabled for widget: "qt_scrollarea_viewport"
[2025-07-14 15:00:48.956] Debug: Processing child widget: "qt_scrollarea_hcontainer" with enabled: true
[2025-07-14 15:00:48.956] Debug: Starting setWidgetEnabled for widget: "qt_scrollarea_hcontainer" with enabled: true
[2025-07-14 15:00:48.956] Debug: Processing child widget: "" with enabled: true
[2025-07-14 15:00:48.956] Debug: Starting setWidgetEnabled for widget: "" with enabled: true
[2025-07-14 15:00:48.956] Debug: Finished setWidgetEnabled for widget: ""
[2025-07-14 15:00:48.956] Debug: Finished setWidgetEnabled for widget: "qt_scrollarea_hcontainer"
[2025-07-14 15:00:48.956] Debug: Processing child widget: "qt_scrollarea_vcontainer" with enabled: true
[2025-07-14 15:00:48.956] Debug: Starting setWidgetEnabled for widget: "qt_scrollarea_vcontainer" with enabled: true
[2025-07-14 15:00:48.956] Debug: Processing child widget: "" with enabled: true
[2025-07-14 15:00:48.956] Debug: Starting setWidgetEnabled for widget: "" with enabled: true
[2025-07-14 15:00:48.956] Debug: Finished setWidgetEnabled for widget: ""
[2025-07-14 15:00:48.956] Debug: Finished setWidgetEnabled for widget: "qt_scrollarea_vcontainer"
[2025-07-14 15:00:48.956] Debug: Finished setWidgetEnabled for widget: "textEdit_presend1"
[2025-07-14 15:00:48.956] Debug: Processing child widget: "label_8" with enabled: true
[2025-07-14 15:00:48.956] Debug: Starting setWidgetEnabled for widget: "label_8" with enabled: true
[2025-07-14 15:00:48.956] Debug: Finished setWidgetEnabled for widget: "label_8"
[2025-07-14 15:00:48.956] Debug: Processing child widget: "textEdit_presend2" with enabled: true
[2025-07-14 15:00:48.956] Debug: Starting setWidgetEnabled for widget: "textEdit_presend2" with enabled: true
[2025-07-14 15:00:48.956] Debug: Processing child widget: "qt_scrollarea_viewport" with enabled: true
[2025-07-14 15:00:48.956] Debug: Starting setWidgetEnabled for widget: "qt_scrollarea_viewport" with enabled: true
[2025-07-14 15:00:48.956] Debug: Finished setWidgetEnabled for widget: "qt_scrollarea_viewport"
[2025-07-14 15:00:48.956] Debug: Processing child widget: "qt_scrollarea_hcontainer" with enabled: true
[2025-07-14 15:00:48.956] Debug: Starting setWidgetEnabled for widget: "qt_scrollarea_hcontainer" with enabled: true
[2025-07-14 15:00:48.956] Debug: Processing child widget: "" with enabled: true
[2025-07-14 15:00:48.956] Debug: Starting setWidgetEnabled for widget: "" with enabled: true
[2025-07-14 15:00:48.956] Debug: Finished setWidgetEnabled for widget: ""
[2025-07-14 15:00:48.956] Debug: Finished setWidgetEnabled for widget: "qt_scrollarea_hcontainer"
[2025-07-14 15:00:48.956] Debug: Processing child widget: "qt_scrollarea_vcontainer" with enabled: true
[2025-07-14 15:00:48.956] Debug: Starting setWidgetEnabled for widget: "qt_scrollarea_vcontainer" with enabled: true
[2025-07-14 15:00:48.956] Debug: Processing child widget: "" with enabled: true
[2025-07-14 15:00:48.956] Debug: Starting setWidgetEnabled for widget: "" with enabled: true
[2025-07-14 15:00:48.956] Debug: Finished setWidgetEnabled for widget: ""
[2025-07-14 15:00:48.956] Debug: Finished setWidgetEnabled for widget: "qt_scrollarea_vcontainer"
[2025-07-14 15:00:48.956] Debug: Finished setWidgetEnabled for widget: "textEdit_presend2"
[2025-07-14 15:00:48.956] Debug: Processing child widget: "label_9" with enabled: true
[2025-07-14 15:00:48.956] Debug: Starting setWidgetEnabled for widget: "label_9" with enabled: true
[2025-07-14 15:00:48.956] Debug: Finished setWidgetEnabled for widget: "label_9"
[2025-07-14 15:00:48.956] Debug: Processing child widget: "lineEdit_mainwindow_totaltimes" with enabled: true
[2025-07-14 15:00:48.956] Debug: Starting setWidgetEnabled for widget: "lineEdit_mainwindow_totaltimes" with enabled: true
[2025-07-14 15:00:48.956] Debug: Finished setWidgetEnabled for widget: "lineEdit_mainwindow_totaltimes"
[2025-07-14 15:00:48.956] Debug: Processing child widget: "textEdit_Debug" with enabled: true
[2025-07-14 15:00:48.956] Debug: Starting setWidgetEnabled for widget: "textEdit_Debug" with enabled: true
[2025-07-14 15:00:48.956] Debug: Processing child widget: "qt_scrollarea_viewport" with enabled: true
[2025-07-14 15:00:48.956] Debug: Starting setWidgetEnabled for widget: "qt_scrollarea_viewport" with enabled: true
[2025-07-14 15:00:48.956] Debug: Finished setWidgetEnabled for widget: "qt_scrollarea_viewport"
[2025-07-14 15:00:48.956] Debug: Processing child widget: "qt_scrollarea_hcontainer" with enabled: true
[2025-07-14 15:00:48.956] Debug: Starting setWidgetEnabled for widget: "qt_scrollarea_hcontainer" with enabled: true
[2025-07-14 15:00:48.956] Debug: Processing child widget: "" with enabled: true
[2025-07-14 15:00:48.956] Debug: Starting setWidgetEnabled for widget: "" with enabled: true
[2025-07-14 15:00:48.956] Debug: Finished setWidgetEnabled for widget: ""
[2025-07-14 15:00:48.956] Debug: Finished setWidgetEnabled for widget: "qt_scrollarea_hcontainer"
[2025-07-14 15:00:48.956] Debug: Processing child widget: "qt_scrollarea_vcontainer" with enabled: true
[2025-07-14 15:00:48.956] Debug: Starting setWidgetEnabled for widget: "qt_scrollarea_vcontainer" with enabled: true
[2025-07-14 15:00:48.956] Debug: Processing child widget: "" with enabled: true
[2025-07-14 15:00:48.956] Debug: Starting setWidgetEnabled for widget: "" with enabled: true
[2025-07-14 15:00:48.956] Debug: Finished setWidgetEnabled for widget: ""
[2025-07-14 15:00:48.956] Debug: Finished setWidgetEnabled for widget: "qt_scrollarea_vcontainer"
[2025-07-14 15:00:48.956] Debug: Finished setWidgetEnabled for widget: "textEdit_Debug"
[2025-07-14 15:00:48.956] Debug: Processing child widget: "pushButton_ClearDebug" with enabled: true
[2025-07-14 15:00:48.956] Debug: Starting setWidgetEnabled for widget: "pushButton_ClearDebug" with enabled: true
[2025-07-14 15:00:48.956] Debug: Finished setWidgetEnabled for widget: "pushButton_ClearDebug"
[2025-07-14 15:00:48.956] Debug: Processing child widget: "pushButton_go_0" with enabled: true
[2025-07-14 15:00:48.956] Debug: Starting setWidgetEnabled for widget: "pushButton_go_0" with enabled: true
[2025-07-14 15:00:48.956] Debug: Finished setWidgetEnabled for widget: "pushButton_go_0"
[2025-07-14 15:00:48.956] Debug: Finished setWidgetEnabled for widget: "centralwidget"
[2025-07-14 15:00:49.752] Warning: QMetaObject::connectSlotsByName: No matching signal for on_pushButton_go_0_clicked()
[2025-07-14 15:00:49.752] Debug: 
===== [初始化表單ID] =====
[2025-07-14 15:00:49.752] Debug: - 當前表單映射大小： 0
[2025-07-14 15:00:49.752] Debug: 為軸 1 設置表單ID對: 主= 1 , 次= 11
[2025-07-14 15:00:49.752] Debug: 為軸 2 設置表單ID對: 主= 2 , 次= 12
[2025-07-14 15:00:49.752] Debug: 為軸 3 設置表單ID對: 主= 3 , 次= 13
[2025-07-14 15:00:49.752] Debug: 為軸 4 設置表單ID對: 主= 4 , 次= 14
[2025-07-14 15:00:49.752] Debug: 為軸 5 設置表單ID對: 主= 5 , 次= 15
[2025-07-14 15:00:49.752] Debug: 為軸 6 設置表單ID對: 主= 6 , 次= 16
[2025-07-14 15:00:49.752] Debug: 為軸 7 設置表單ID對: 主= 7 , 次= 17
[2025-07-14 15:00:49.752] Debug: 為軸 8 設置表單ID對: 主= 8 , 次= 18
[2025-07-14 15:00:49.752] Debug: - 初始化後表單映射大小： 8
[2025-07-14 15:00:49.753] Debug: 日誌系統初始化成功，路徑： "D:/测试保存/0714/0714_3/build-sewing2025-Desktop_Qt_6_6_1_MinGW_64_bit-Release/logs"
[2025-07-14 15:00:55.001] Debug: 開始初始化補償 - 軸號: 1
[2025-07-14 15:00:55.002] Debug: 補償初始化成功 - 軸號: 1
[2025-07-14 15:00:55.004] Debug: 開始初始化補償 - 軸號: 2
[2025-07-14 15:00:55.006] Debug: 補償初始化成功 - 軸號: 2
[2025-07-14 15:00:55.006] Debug: 開始初始化補償 - 軸號: 3
[2025-07-14 15:00:55.008] Debug: 補償初始化成功 - 軸號: 3
[2025-07-14 15:00:55.008] Debug: 開始初始化補償 - 軸號: 4
[2025-07-14 15:00:55.010] Debug: 補償初始化成功 - 軸號: 4
[2025-07-14 15:00:55.010] Debug: 開始初始化補償 - 軸號: 5
[2025-07-14 15:00:55.012] Debug: 補償初始化成功 - 軸號: 5
[2025-07-14 15:00:55.012] Debug: 開始初始化補償 - 軸號: 6
[2025-07-14 15:00:55.014] Debug: 補償初始化成功 - 軸號: 6
[2025-07-14 15:00:55.014] Debug: 開始初始化補償 - 軸號: 7
[2025-07-14 15:00:55.016] Debug: 補償初始化成功 - 軸號: 7
[2025-07-14 15:00:55.016] Debug: 開始初始化補償 - 軸號: 8
[2025-07-14 15:00:55.017] Debug: 補償初始化成功 - 軸號: 8
[2025-07-14 15:00:55.018] Debug: "軸 1 參考位置初始化:"
[2025-07-14 15:00:55.018] Debug: "  當前位置: 0"
[2025-07-14 15:00:55.018] Debug: "  原點位置: 0"
[2025-07-14 15:00:55.018] Debug: "軸 2 參考位置初始化:"
[2025-07-14 15:00:55.018] Debug: "  當前位置: 0"
[2025-07-14 15:00:55.018] Debug: "  原點位置: 0"
[2025-07-14 15:00:55.018] Debug: "軸 3 參考位置初始化:"
[2025-07-14 15:00:55.018] Debug: "  當前位置: 0"
[2025-07-14 15:00:55.018] Debug: "  原點位置: 0"
[2025-07-14 15:00:55.018] Debug: "軸 4 參考位置初始化:"
[2025-07-14 15:00:55.018] Debug: "  當前位置: 0"
[2025-07-14 15:00:55.018] Debug: "  原點位置: 0"
[2025-07-14 15:00:55.019] Debug: "軸 5 參考位置初始化:"
[2025-07-14 15:00:55.019] Debug: "  當前位置: 0"
[2025-07-14 15:00:55.019] Debug: "  原點位置: 0"
[2025-07-14 15:00:55.019] Debug: "軸 6 參考位置初始化:"
[2025-07-14 15:00:55.019] Debug: "  當前位置: 0"
[2025-07-14 15:00:55.019] Debug: "  原點位置: 0"
[2025-07-14 15:00:55.019] Debug: "軸 7 參考位置初始化:"
[2025-07-14 15:00:55.019] Debug: "  當前位置: 0"
[2025-07-14 15:00:55.019] Debug: "  原點位置: 0"
[2025-07-14 15:00:55.019] Debug: "軸 8 參考位置初始化:"
[2025-07-14 15:00:55.019] Debug: "  當前位置: 0"
[2025-07-14 15:00:55.019] Debug: "  原點位置: 0"
[2025-07-14 15:00:56.070] Debug: 配置文件中的最大軸數: 7
[2025-07-14 15:00:56.621] Debug: ===== 位置監控 =====
[2025-07-14 15:00:56.621] Debug: 規劃位置: 0
[2025-07-14 15:00:56.621] Debug: 編碼器位置: 0
[2025-07-14 15:00:56.621] Debug: EtherCAT編碼器絕對位置: 38296
[2025-07-14 15:00:56.621] Debug: 補償量: 0
[2025-07-14 15:00:56.622] Debug: 補償模式: 禁用
[2025-07-14 15:00:56.622] Debug: 補償循環模式: 0
[2025-07-14 15:00:56.622] Debug: "軸 1 PDO 數據:"
[2025-07-14 15:00:56.622] Debug: "  Byte 0: 0x57"
[2025-07-14 15:00:56.622] Debug: "  Byte 1: 0x75"
[2025-07-14 15:00:56.622] Debug: "  Byte 2: 0x02"
[2025-07-14 15:00:56.622] Debug: "  Byte 3: 0x00"
[2025-07-14 15:00:56.622] Debug: "軸 1 診斷信息:"
[2025-07-14 15:00:56.622] Debug: "- EtherCAT 編碼器位置: 38296"
[2025-07-14 15:00:56.622] Debug: "軸 1 自動使能成功"
[2025-07-14 15:00:56.622] Debug: "軸 1 狀態: 0x200"
[2025-07-14 15:00:57.174] Debug: ===== 位置監控 =====
[2025-07-14 15:00:57.174] Debug: 規劃位置: 0
[2025-07-14 15:00:57.174] Debug: 編碼器位置: 0
[2025-07-14 15:00:57.174] Debug: EtherCAT編碼器絕對位置: 161111
[2025-07-14 15:00:57.174] Debug: 補償量: 0
[2025-07-14 15:00:57.174] Debug: 補償模式: 禁用
[2025-07-14 15:00:57.175] Debug: 補償循環模式: 0
[2025-07-14 15:00:57.175] Debug: "軸 2 PDO 數據:"
[2025-07-14 15:00:57.175] Debug: "  Byte 0: 0xd4"
[2025-07-14 15:00:57.175] Debug: "  Byte 1: 0x6b"
[2025-07-14 15:00:57.175] Debug: "  Byte 2: 0x03"
[2025-07-14 15:00:57.175] Debug: "  Byte 3: 0x00"
[2025-07-14 15:00:57.175] Debug: "軸 2 診斷信息:"
[2025-07-14 15:00:57.175] Debug: "- EtherCAT 編碼器位置: 161111"
[2025-07-14 15:00:57.175] Debug: "軸 2 自動使能成功"
[2025-07-14 15:00:57.175] Debug: "軸 2 狀態: 0x200"
[2025-07-14 15:00:57.726] Debug: ===== 位置監控 =====
[2025-07-14 15:00:57.726] Debug: 規劃位置: 0
[2025-07-14 15:00:57.726] Debug: 編碼器位置: 0
[2025-07-14 15:00:57.726] Debug: EtherCAT編碼器絕對位置: 224212
[2025-07-14 15:00:57.726] Debug: 補償量: 0
[2025-07-14 15:00:57.727] Debug: 補償模式: 禁用
[2025-07-14 15:00:57.727] Debug: 補償循環模式: 0
[2025-07-14 15:00:57.727] Debug: "軸 3 PDO 數據:"
[2025-07-14 15:00:57.727] Debug: "  Byte 0: 0x7a"
[2025-07-14 15:00:57.727] Debug: "  Byte 1: 0x31"
[2025-07-14 15:00:57.727] Debug: "  Byte 2: 0xf8"
[2025-07-14 15:00:57.727] Debug: "  Byte 3: 0xff"
[2025-07-14 15:00:57.727] Debug: "軸 3 診斷信息:"
[2025-07-14 15:00:57.727] Debug: "- EtherCAT 編碼器位置: 224212"
[2025-07-14 15:00:57.727] Debug: "軸 3 自動使能成功"
[2025-07-14 15:00:57.727] Debug: "軸 3 狀態: 0x200"
[2025-07-14 15:00:58.279] Debug: ===== 位置監控 =====
[2025-07-14 15:00:58.279] Debug: 規劃位置: 0
[2025-07-14 15:00:58.279] Debug: 編碼器位置: 0
[2025-07-14 15:00:58.279] Debug: EtherCAT編碼器絕對位置: -511622
[2025-07-14 15:00:58.279] Debug: 補償量: 0
[2025-07-14 15:00:58.280] Debug: 補償模式: 禁用
[2025-07-14 15:00:58.280] Debug: 補償循環模式: 0
[2025-07-14 15:00:58.280] Debug: "軸 4 PDO 數據:"
[2025-07-14 15:00:58.280] Debug: "  Byte 0: 0x9c"
[2025-07-14 15:00:58.280] Debug: "  Byte 1: 0xa4"
[2025-07-14 15:00:58.280] Debug: "  Byte 2: 0x01"
[2025-07-14 15:00:58.280] Debug: "  Byte 3: 0x00"
[2025-07-14 15:00:58.280] Debug: "軸 4 診斷信息:"
[2025-07-14 15:00:58.280] Debug: "- EtherCAT 編碼器位置: -511622"
[2025-07-14 15:00:58.280] Debug: "軸 4 自動使能成功"
[2025-07-14 15:00:58.280] Debug: "軸 4 狀態: 0x200"
[2025-07-14 15:00:58.831] Debug: ===== 位置監控 =====
[2025-07-14 15:00:58.831] Debug: 規劃位置: 0
[2025-07-14 15:00:58.831] Debug: 編碼器位置: 0
[2025-07-14 15:00:58.831] Debug: EtherCAT編碼器絕對位置: 107676
[2025-07-14 15:00:58.831] Debug: 補償量: 0
[2025-07-14 15:00:58.832] Debug: 補償模式: 禁用
[2025-07-14 15:00:58.832] Debug: 補償循環模式: 0
[2025-07-14 15:00:58.832] Debug: "軸 5 PDO 數據:"
[2025-07-14 15:00:58.832] Debug: "  Byte 0: 0x94"
[2025-07-14 15:00:58.832] Debug: "  Byte 1: 0x4c"
[2025-07-14 15:00:58.832] Debug: "  Byte 2: 0xff"
[2025-07-14 15:00:58.832] Debug: "  Byte 3: 0xff"
[2025-07-14 15:00:58.832] Debug: "軸 5 診斷信息:"
[2025-07-14 15:00:58.832] Debug: "- EtherCAT 編碼器位置: 107676"
[2025-07-14 15:00:58.832] Debug: "軸 5 自動使能成功"
[2025-07-14 15:00:58.832] Debug: "軸 5 狀態: 0x200"
[2025-07-14 15:00:59.433] Debug: ===== 位置監控 =====
[2025-07-14 15:00:59.433] Debug: 規劃位置: 0
[2025-07-14 15:00:59.433] Debug: 編碼器位置: 0
[2025-07-14 15:00:59.433] Debug: EtherCAT編碼器絕對位置: -45932
[2025-07-14 15:00:59.433] Debug: 補償量: 0
[2025-07-14 15:00:59.433] Debug: 補償模式: 禁用
[2025-07-14 15:00:59.433] Debug: 補償循環模式: 0
[2025-07-14 15:00:59.434] Debug: "軸 6 PDO 數據:"
[2025-07-14 15:00:59.434] Debug: "  Byte 0: 0x56"
[2025-07-14 15:00:59.434] Debug: "  Byte 1: 0x3c"
[2025-07-14 15:00:59.434] Debug: "  Byte 2: 0xff"
[2025-07-14 15:00:59.434] Debug: "  Byte 3: 0xff"
[2025-07-14 15:00:59.434] Debug: "軸 6 診斷信息:"
[2025-07-14 15:00:59.434] Debug: "- EtherCAT 編碼器位置: -45932"
[2025-07-14 15:00:59.434] Debug: "軸 6 自動使能成功"
[2025-07-14 15:00:59.434] Debug: "軸 6 狀態: 0x220"
[2025-07-14 15:00:59.985] Debug: ===== 位置監控 =====
[2025-07-14 15:00:59.985] Debug: 規劃位置: 0
[2025-07-14 15:00:59.985] Debug: 編碼器位置: 1
[2025-07-14 15:00:59.985] Debug: EtherCAT編碼器絕對位置: -50089
[2025-07-14 15:00:59.985] Debug: 補償量: 1
[2025-07-14 15:00:59.985] Debug: 補償模式: 禁用
[2025-07-14 15:00:59.985] Debug: 補償循環模式: 0
[2025-07-14 15:00:59.985] Debug: "軸 7 診斷信息:"
[2025-07-14 15:00:59.985] Debug: "- EtherCAT 編碼器位置: -50089"
[2025-07-14 15:00:59.985] Debug: "軸 7 自動使能成功"
[2025-07-14 15:00:59.985] Debug: "軸 7 狀態: 0x200"
[2025-07-14 15:01:00.987] Debug: ===== [對準步驟] 檢查所有軸的絕對位置並緩慢歸零 =====
[2025-07-14 15:01:00.987] Debug: "軸 1 狀態檢查: 0x200"
[2025-07-14 15:01:00.987] Debug: "  軸 1 當前 EtherCAT 編碼器絕對位置: 38296"
[2025-07-14 15:01:00.987] Debug: "  軸 1 絕對位置差異: 38296 (已存儲)"
[2025-07-14 15:01:00.987] Debug: "軸 2 狀態檢查: 0x200"
[2025-07-14 15:01:00.987] Debug: "  軸 2 當前 EtherCAT 編碼器絕對位置: 161111"
[2025-07-14 15:01:00.987] Debug: "  軸 2 絕對位置差異: 161111 (已存儲)"
[2025-07-14 15:01:00.988] Debug: "軸 3 狀態檢查: 0x200"
[2025-07-14 15:01:00.988] Debug: "  軸 3 當前 EtherCAT 編碼器絕對位置: 224212"
[2025-07-14 15:01:00.988] Debug: "  軸 3 絕對位置差異: 224212 (已存儲)"
[2025-07-14 15:01:00.988] Debug: "軸 4 狀態檢查: 0x200"
[2025-07-14 15:01:00.988] Debug: "  軸 4 當前 EtherCAT 編碼器絕對位置: -511622"
[2025-07-14 15:01:00.988] Debug: "  軸 4 絕對位置差異: -511622 (已存儲)"
[2025-07-14 15:01:00.988] Debug: "軸 5 狀態檢查: 0x200"
[2025-07-14 15:01:00.988] Debug: "  軸 5 當前 EtherCAT 編碼器絕對位置: 107676"
[2025-07-14 15:01:00.988] Debug: "  軸 5 絕對位置差異: 107676 (已存儲)"
[2025-07-14 15:01:00.989] Debug: "軸 6 狀態檢查: 0x220"
[2025-07-14 15:01:00.989] Debug: "  軸 6 當前 EtherCAT 編碼器絕對位置: -45932"
[2025-07-14 15:01:00.989] Debug: "  軸 6 絕對位置差異: -45932 (已存儲)"
[2025-07-14 15:01:00.989] Debug: "軸 7 狀態檢查: 0x200"
[2025-07-14 15:01:00.989] Debug: "  軸 7 當前 EtherCAT 編碼器絕對位置: -50089"
[2025-07-14 15:01:00.989] Debug: "  軸 7 絕對位置差異: -50089 (已存儲)"
[2025-07-14 15:01:00.989] Debug: "  軸 8 未使能或不存在，跳過位置檢查"
[2025-07-14 15:01:00.989] Debug: ===== 軸絕對位置差異總結 =====
[2025-07-14 15:01:00.989] Debug: "軸 1: 38296"
[2025-07-14 15:01:00.989] Debug: "軸 2: 161111"
[2025-07-14 15:01:00.989] Debug: "軸 3: 224212"
[2025-07-14 15:01:00.989] Debug: "軸 4: -511622"
[2025-07-14 15:01:00.989] Debug: "軸 5: 107676"
[2025-07-14 15:01:00.989] Debug: "軸 6: -45932"
[2025-07-14 15:01:00.989] Debug: "軸 7: -50089"
[2025-07-14 15:01:00.990] Debug: 
===== 設置Watch監測 =====
[2025-07-14 15:01:01.002] Debug: Watch配置文件加載成功
[2025-07-14 15:01:01.066] Debug: EtherCAT窗口已創建並保存到m_ethercat實例
[2025-07-14 15:01:03.309] Debug: MainWindow: pushButton_go_0 按鈕被點擊
[2025-07-14 15:01:03.309] Debug: ===== [軸運動] 開始執行所有軸運動到目標位置 =====
[2025-07-14 15:01:03.309] Debug: "設置軸 1 目標位置為: 38278"
[2025-07-14 15:01:03.309] Debug: "設置軸 2 目標位置為: 161088"
[2025-07-14 15:01:03.309] Debug: "設置軸 3 目標位置為: 224203"
[2025-07-14 15:01:03.309] Debug: "設置軸 4 目標位置為: -511632"
[2025-07-14 15:01:03.309] Debug: "設置軸 5 目標位置為: 107669"
[2025-07-14 15:01:03.309] Debug: "設置軸 6 目標位置為: 58098"
[2025-07-14 15:01:03.309] Debug: "設置軸 7 目標位置為: -50000"
[2025-07-14 15:01:03.309] Debug: ===== 軸目標位置初始化完成 =====
[2025-07-14 15:01:03.310] Debug: "寫入軸 1 當前絕對位置: 38296"
[2025-07-14 15:01:03.310] Debug: "寫入軸 2 當前絕對位置: 161111"
[2025-07-14 15:01:03.310] Debug: "寫入軸 3 當前絕對位置: 224212"
[2025-07-14 15:01:03.310] Debug: "寫入軸 4 當前絕對位置: -511622"
[2025-07-14 15:01:03.310] Debug: "寫入軸 5 當前絕對位置: 107676"
[2025-07-14 15:01:03.310] Debug: "寫入軸 6 當前絕對位置: -45932"
[2025-07-14 15:01:03.310] Debug: "寫入軸 7 當前絕對位置: -50089"
[2025-07-14 15:01:03.310] Debug: "警告: 無法讀取軸 8 的絕對位置"
[2025-07-14 15:01:03.310] Debug: 成功生成 0_pose.txt 檔案： "D:/测试保存/0714/0714_3/build-sewing2025-Desktop_Qt_6_6_1_MinGW_64_bit-Release/0_pose.txt"
[2025-07-14 15:01:03.310] Debug: "開始軸 1 歸零運動，當前絕對位置: 38296"
[2025-07-14 15:01:03.311] Debug: "軸 1 狀態檢查: 不適合歸零 (狀態碼: 0x200)"
[2025-07-14 15:01:03.311] Debug: "  問題: 伺服未準備"
[2025-07-14 15:01:03.311] Debug: "  狀態詳情: 使能"
[2025-07-14 15:01:03.311] Debug: "警告: 軸 1 狀態不適合歸零，跳過"
[2025-07-14 15:01:03.311] Debug: "開始軸 2 歸零運動，當前絕對位置: 161111"
[2025-07-14 15:01:03.311] Debug: "軸 2 狀態檢查: 不適合歸零 (狀態碼: 0x200)"
[2025-07-14 15:01:03.311] Debug: "  問題: 伺服未準備"
[2025-07-14 15:01:03.311] Debug: "  狀態詳情: 使能"
[2025-07-14 15:01:03.311] Debug: "警告: 軸 2 狀態不適合歸零，跳過"
[2025-07-14 15:01:03.311] Debug: "開始軸 3 歸零運動，當前絕對位置: 224212"
[2025-07-14 15:01:03.311] Debug: "軸 3 狀態檢查: 不適合歸零 (狀態碼: 0x200)"
[2025-07-14 15:01:03.311] Debug: "  問題: 伺服未準備"
[2025-07-14 15:01:03.311] Debug: "  狀態詳情: 使能"
[2025-07-14 15:01:03.311] Debug: "警告: 軸 3 狀態不適合歸零，跳過"
[2025-07-14 15:01:03.311] Debug: "開始軸 4 歸零運動，當前絕對位置: -511622"
[2025-07-14 15:01:03.311] Debug: "軸 4 狀態檢查: 不適合歸零 (狀態碼: 0x200)"
[2025-07-14 15:01:03.311] Debug: "  問題: 伺服未準備"
[2025-07-14 15:01:03.311] Debug: "  狀態詳情: 使能"
[2025-07-14 15:01:03.311] Debug: "警告: 軸 4 狀態不適合歸零，跳過"
[2025-07-14 15:01:03.311] Debug: "開始軸 5 歸零運動，當前絕對位置: 107676"
[2025-07-14 15:01:03.311] Debug: "軸 5 狀態檢查: 不適合歸零 (狀態碼: 0x200)"
[2025-07-14 15:01:03.311] Debug: "  問題: 伺服未準備"
[2025-07-14 15:01:03.311] Debug: "  狀態詳情: 使能"
[2025-07-14 15:01:03.311] Debug: "警告: 軸 5 狀態不適合歸零，跳過"
[2025-07-14 15:01:03.311] Debug: "開始軸 6 歸零運動，當前絕對位置: -45932"
[2025-07-14 15:01:03.311] Debug: "軸 6 狀態檢查: 不適合歸零 (狀態碼: 0x220)"
[2025-07-14 15:01:03.311] Debug: "  問題: 伺服未準備, 觸發正限位"
[2025-07-14 15:01:03.311] Debug: "  狀態詳情: 正限位 | 使能"
[2025-07-14 15:01:03.311] Debug: "警告: 軸 6 狀態不適合歸零，跳過"
[2025-07-14 15:01:03.311] Debug: "開始軸 7 歸零運動，當前絕對位置: -50089"
[2025-07-14 15:01:03.311] Debug: "軸 7 狀態檢查: 不適合歸零 (狀態碼: 0x200)"
[2025-07-14 15:01:03.311] Debug: "  問題: 伺服未準備"
[2025-07-14 15:01:03.311] Debug: "  狀態詳情: 使能"
[2025-07-14 15:01:03.311] Debug: "警告: 軸 7 狀態不適合歸零，跳過"
[2025-07-14 15:01:03.311] Debug: ===== [軸運動] 所有軸運動指令已發送，開始監控歸位進度 =====
[2025-07-14 15:01:03.311] Debug: "[歸位完成] 軸 1 已停止，歸位結束"
[2025-07-14 15:01:03.312] Debug: "[歸位完成] 軸 2 已停止，歸位結束"
[2025-07-14 15:01:03.312] Debug: "[歸位完成] 軸 3 已停止，歸位結束"
[2025-07-14 15:01:03.312] Debug: "[歸位完成] 軸 4 已停止，歸位結束"
[2025-07-14 15:01:03.312] Debug: "[歸位完成] 軸 5 已停止，歸位結束"
[2025-07-14 15:01:03.312] Debug: "[歸位完成] 軸 6 已停止，歸位結束"
[2025-07-14 15:01:03.312] Debug: "[歸位完成] 軸 7 已停止，歸位結束"
[2025-07-14 15:01:03.312] Debug: ===== [軸運動] 所有軸歸位完成，開始位置同步驗證 =====
[2025-07-14 15:01:05.313] Debug: "  [CHECK] 檢查軸 1 運動完成後的真實位置狀態，目標位置: 38278"
[2025-07-14 15:01:05.314] Debug: "  [REAL-STATUS] 軸 1 真實位置狀態:"
[2025-07-14 15:01:05.314] Debug: "    規劃位置: 38296 (誤差: 18)"
[2025-07-14 15:01:05.314] Debug: "    編碼器位置: 0 (誤差: -38278)"
[2025-07-14 15:01:05.314] Debug: "    絕對位置: 38296 (誤差: 18)"
[2025-07-14 15:01:05.314] Debug: "  [POSITION-OK] 軸 1 定位精度合格，絕對誤差: 18 (≤50)"
[2025-07-14 15:01:05.314] Debug: "  [CHECK] 檢查軸 2 運動完成後的真實位置狀態，目標位置: 161088"
[2025-07-14 15:01:05.315] Debug: "  [REAL-STATUS] 軸 2 真實位置狀態:"
[2025-07-14 15:01:05.315] Debug: "    規劃位置: 161111 (誤差: 23)"
[2025-07-14 15:01:05.315] Debug: "    編碼器位置: 0 (誤差: -161088)"
[2025-07-14 15:01:05.315] Debug: "    絕對位置: 161111 (誤差: 23)"
[2025-07-14 15:01:05.315] Debug: "  [POSITION-OK] 軸 2 定位精度合格，絕對誤差: 23 (≤50)"
[2025-07-14 15:01:05.315] Debug: "  [CHECK] 檢查軸 3 運動完成後的真實位置狀態，目標位置: 224203"
[2025-07-14 15:01:05.316] Debug: "  [REAL-STATUS] 軸 3 真實位置狀態:"
[2025-07-14 15:01:05.316] Debug: "    規劃位置: 224212 (誤差: 9)"
[2025-07-14 15:01:05.316] Debug: "    編碼器位置: 0 (誤差: -224203)"
[2025-07-14 15:01:05.316] Debug: "    絕對位置: 224212 (誤差: 9)"
[2025-07-14 15:01:05.316] Debug: "  [POSITION-OK] 軸 3 定位精度合格，絕對誤差: 9 (≤50)"
[2025-07-14 15:01:05.316] Debug: "  [CHECK] 檢查軸 4 運動完成後的真實位置狀態，目標位置: -511632"
[2025-07-14 15:01:05.317] Debug: "  [REAL-STATUS] 軸 4 真實位置狀態:"
[2025-07-14 15:01:05.317] Debug: "    規劃位置: -511622 (誤差: 10)"
[2025-07-14 15:01:05.317] Debug: "    編碼器位置: 0 (誤差: 511632)"
[2025-07-14 15:01:05.317] Debug: "    絕對位置: -511622 (誤差: 10)"
[2025-07-14 15:01:05.317] Debug: "  [POSITION-OK] 軸 4 定位精度合格，絕對誤差: 10 (≤50)"
[2025-07-14 15:01:05.317] Debug: "  [CHECK] 檢查軸 5 運動完成後的真實位置狀態，目標位置: 107669"
[2025-07-14 15:01:05.318] Debug: "  [REAL-STATUS] 軸 5 真實位置狀態:"
[2025-07-14 15:01:05.318] Debug: "    規劃位置: 107676 (誤差: 7)"
[2025-07-14 15:01:05.318] Debug: "    編碼器位置: 0 (誤差: -107669)"
[2025-07-14 15:01:05.318] Debug: "    絕對位置: 107676 (誤差: 7)"
[2025-07-14 15:01:05.318] Debug: "  [POSITION-OK] 軸 5 定位精度合格，絕對誤差: 7 (≤50)"
[2025-07-14 15:01:05.318] Debug: "  [CHECK] 檢查軸 6 運動完成後的真實位置狀態，目標位置: 58098"
[2025-07-14 15:01:05.319] Debug: "  [REAL-STATUS] 軸 6 真實位置狀態:"
[2025-07-14 15:01:05.319] Debug: "    規劃位置: -45932 (誤差: -104030)"
[2025-07-14 15:01:05.319] Debug: "    編碼器位置: 0 (誤差: -58098)"
[2025-07-14 15:01:05.319] Debug: "    絕對位置: -45932 (誤差: -104030)"
[2025-07-14 15:01:05.319] Debug: "  [POSITION-FAIL] 軸 6 定位精度不足，絕對誤差: -104030 (>50)"
[2025-07-14 15:01:05.319] Debug: "  [CHECK] 檢查軸 7 運動完成後的真實位置狀態，目標位置: -50000"
[2025-07-14 15:01:05.320] Debug: "  [REAL-STATUS] 軸 7 真實位置狀態:"
[2025-07-14 15:01:05.320] Debug: "    規劃位置: -50089 (誤差: -89)"
[2025-07-14 15:01:05.320] Debug: "    編碼器位置: 1 (誤差: 50001)"
[2025-07-14 15:01:05.320] Debug: "    絕對位置: -50089 (誤差: -89)"
[2025-07-14 15:01:05.320] Debug: "  [POSITION-FAIL] 軸 7 定位精度不足，絕對誤差: -89 (>50)"
[2025-07-14 15:01:05.320] Debug: ===== [軸運動] 歸位完成但部分軸定位精度不足，請檢查軸狀態 =====