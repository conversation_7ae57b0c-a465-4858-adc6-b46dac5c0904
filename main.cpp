#include <QApplication>
#include <QMessageBox>
#include <QDebug>
#include <QDir>
#include "mainwindow.h"

int main(int argc, char *argv[])
{
    try {
        // 设置应用程序工作目录
        QDir::setCurrent(QCoreApplication::applicationDirPath());
        
        qDebug() << "Starting application from:" << QCoreApplication::applicationDirPath();
        
        QApplication a(argc, argv);
        qDebug() << "QApplication initialized";
        
        MainWindow w;
        qDebug() << "MainWindow created";
        
        w.show();
        qDebug() << "MainWindow shown";
        
        qDebug() << "Entering event loop...";
        return a.exec();
        
    } catch (const std::exception& e) {
        QMessageBox::critical(nullptr, "Error", 
            QString("Application error: %1").arg(e.what()));
        return 1;
    } catch (...) {
        QMessageBox::critical(nullptr, "Error", 
            "Unknown error occurred");
        return 1;
    }
}
