// 2-2.cpp : 演示控制台應用程序的入口點
//

#include "stdafx.h"
#include "windows.h"
#include "conio.h"
#include "math.h"
#include "gts.h"
	
#define CORE		1				// 控制卡核心編號
#define AXIS		1				// 控制軸號

#define A			50				// 幅值
#define T			1				// 週期
#define DELTA		0.016			// 時間間隔
#define LOOP		2				// 循環次數

#define PI			3.1415926

// 該函數用於處理GTN指令的執行結果，command為指令名稱，error為指令執行返回值
void commandhandler(char *command, short error)
{
	// 如果指令執行返回值為非0，說明指令執行錯誤，在屏幕上顯示
	if (error)
	{
		printf("%s = %d\n", command, error);
	}
}

int _tmain(int argc, _TCHAR* argv[])
{
	short sRtn, space;
	short sEcatSts;
	// 控制卡核心編號默認為1
	double pos, vel, velPre, time;
	long lAxisSts;
	double prfPos, prfVel;
	short start, loop;

	// 打開運動控制卡
	sRtn = GTN_Open();
	// 指令返回值
	commandhandler("GTN_Open", sRtn);
	if(sRtn)
	{
		printf("Failure to access cord!\n");
		return -1;
	}
	// 初始化EtherCAT通訊環境
	sRtn = GTN_InitEcatComm(CORE);
	commandhandler("GTN_InitEcatComm", sRtn);
	if(sRtn)
	{
		printf("EtherCAT communication error!\n");
		return -1;
	}
	do {//等待直到EtherCAT通訊正常
		sRtn = GTN_IsEcatReady(CORE, &sEcatSts);
	} while (sEcatSts != 1 || sRtn != 0);
	// 開始EtherCAT通訊
	sRtn = GTN_StartEcatComm(CORE);
	commandhandler("GTN_StartEcatComm", sRtn);
	// 複位控制器
	sRtn = GTN_Reset(CORE);
	commandhandler("GTN_Reset", sRtn);
	// 載入運動控制參數
	// 注意：配置文件取決於實際的設備配置
	sRtn = GTN_LoadConfig(CORE, "GTS800.cfg");
	commandhandler("GTN_LoadConfig", sRtn);
	// 清除所有軸的報警和位置
	sRtn = GTN_ClrSts(CORE, 1, 8);
	commandhandler("GTN_ClrSts", sRtn);
	// 驅動器使能
	sRtn = GTN_AxisOn(CORE, AXIS);
	commandhandler("GTN_AxisOn", sRtn);
	// 位置清零
	sRtn = GTN_ZeroPos(CORE, AXIS);
	commandhandler("GTN_ZeroPos", sRtn);
	// 將AXIS設置為PT模式
	sRtn = GTN_PrfPt(CORE, AXIS, PT_MODE_DYNAMIC);
	commandhandler("GTN_PrfPt", sRtn);
	// 清空AXIS的PT FIFO
	sRtn = GTN_PtClear(CORE, AXIS);
	commandhandler("GTN_PtClear", sRtn); 
	pos = 0;
	vel = velPre = 0;
	time = 0;
	start = 0;
	loop = 1;
	
	while (1)
	{
		// 查詢PT模式FIFO的剩餘空間
		sRtn = GTN_PtSpace(CORE, AXIS, &space);
		commandhandler("GTN_PtSpace", sRtn);
		if (space > 0)
		{
			time += DELTA;
			// 計算末速度
			vel = A*sin((2 * PI) / T*time);
			// 計算當前位置
			pos += 1000 * (vel + velPre)*DELTA / 2;
			velPre = vel;
			if (time < loop*T)
			{
				// 添加數據點
				sRtn = GTN_PtData(CORE, AXIS, pos, (long)(time * 1000));
				commandhandler("GTN_PtData", sRtn);
				if (0 != sRtn)
				{
					printf("\nGTN_PtData()=%d\n", sRtn);
					_getch();
					return 1;
				}
			}
			else
			{
				// 添加終點數據
				sRtn = GTN_PtData(CORE, AXIS, 0, loop*T * 1000, PT_SEGMENT_STOP);
				commandhandler("GTN_PtData", sRtn);
				if (0 != sRtn)
				{
					printf("\nGTN_PtData()=%d\n", sRtn);
					_getch();
					return 1;
				}
				pos = 0;
				time = loop*T;
				velPre = 0;
				++loop;
				if (loop > LOOP)
				{
					break;
				}
			}
		}
		else if (0 == start)
		{
			// 啟動PT運動
			sRtn = GTN_PtStart(CORE, 1 << (AXIS - 1));
			commandhandler("GTN_PtStart", sRtn);
			start = 1;
		}
		// 讀取AXIS的狀態
		sRtn = GTN_GetSts(CORE, AXIS, &lAxisSts);
		commandhandler("GTN_GetSts", sRtn);
		// 讀取AXIS的規劃位置
		sRtn = GTN_GetPrfPos(CORE, AXIS, &prfPos);
		commandhandler("GTN_GetPrfPos", sRtn);
		// 讀取AXIS的規劃速度
		sRtn = GTN_GetPrfVel(CORE, AXIS, &prfVel);
		commandhandler("GTN_GetPrfVel", sRtn);
		printf("lAxisSts=0x%-10lxprfVel=%-10.2lfprfPos=%-10.1lf\r", lAxisSts, prfVel, prfPos);
	}
	
 	while (1)
	{
		// 讀取AXIS的狀態
		sRtn = GTN_GetSts(CORE, AXIS, &lAxisSts);
		commandhandler("GTN_GetSts", sRtn);
		// 讀取AXIS的規劃位置
		sRtn = GTN_GetPrfPos(CORE, AXIS, &prfPos);
		commandhandler("GTN_GetPrfPos", sRtn);
		// 讀取AXIS的規劃速度
		sRtn = GTN_GetPrfVel(CORE, AXIS, &prfVel);
		commandhandler("GTN_GetPrfVel", sRtn);
		printf("lAxisSts=0x%-10lxprfVel=%-10.2lfprfPos=%-10.1lf\r", lAxisSts, prfVel, prfPos);
		if(~lAxisSts & 0x400)
		{
			printf("\nPlase press anykey to continue!\n");
			_getch();
			break;
		}
	}
	// 驅動器關閉
	sRtn = GTN_AxisOff(CORE, AXIS);
	commandhandler("GTN_AxisOff", sRtn);
	sRtn = GTN_TerminateEcatComm(CORE);
	commandhandler("GTN_TerminateEcatComm", sRtn);
	// 關閉運動控制卡
	sRtn = GTN_Close();
	commandhandler("GTN_Close", sRtn);
	return 0;
}

