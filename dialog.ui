<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Dialog</class>
 <widget class="QDialog" name="Dialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>878</width>
    <height>588</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>硬件设置</string>
  </property>
  <property name="styleSheet">
   <string notr="true">background-color: rgb(239, 239, 239);</string>
  </property>
  <widget class="QLabel" name="label">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>20</y>
     <width>101</width>
     <height>19</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="text">
    <string>填加硬件选择</string>
   </property>
  </widget>
  <widget class="QComboBox" name="comboBox">
   <property name="geometry">
    <rect>
     <x>140</x>
     <y>20</y>
     <width>211</width>
     <height>25</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(255, 255, 255);</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pushButton">
   <property name="geometry">
    <rect>
     <x>650</x>
     <y>550</y>
     <width>93</width>
     <height>28</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(170, 170, 255);</string>
   </property>
   <property name="text">
    <string>填加</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pushButton_2">
   <property name="geometry">
    <rect>
     <x>760</x>
     <y>550</y>
     <width>93</width>
     <height>28</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(170, 170, 255);</string>
   </property>
   <property name="text">
    <string>取消</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_2">
   <property name="geometry">
    <rect>
     <x>40</x>
     <y>110</y>
     <width>69</width>
     <height>19</height>
    </rect>
   </property>
   <property name="text">
    <string>通讯连结</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_3">
   <property name="geometry">
    <rect>
     <x>40</x>
     <y>190</y>
     <width>101</width>
     <height>19</height>
    </rect>
   </property>
   <property name="text">
    <string>Baud Rate</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEditBaudRate">
   <property name="geometry">
    <rect>
     <x>140</x>
     <y>190</y>
     <width>141</width>
     <height>25</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(255, 255, 255);</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_4">
   <property name="geometry">
    <rect>
     <x>40</x>
     <y>230</y>
     <width>101</width>
     <height>19</height>
    </rect>
   </property>
   <property name="text">
    <string>Data Bits</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_5">
   <property name="geometry">
    <rect>
     <x>40</x>
     <y>270</y>
     <width>101</width>
     <height>19</height>
    </rect>
   </property>
   <property name="text">
    <string>Stop Bits</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEditDataBit">
   <property name="geometry">
    <rect>
     <x>140</x>
     <y>230</y>
     <width>141</width>
     <height>25</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(255, 255, 255);</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEditStopBit">
   <property name="geometry">
    <rect>
     <x>140</x>
     <y>270</y>
     <width>141</width>
     <height>25</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(255, 255, 255);</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_6">
   <property name="geometry">
    <rect>
     <x>40</x>
     <y>310</y>
     <width>101</width>
     <height>19</height>
    </rect>
   </property>
   <property name="text">
    <string>Parity</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEditParity">
   <property name="geometry">
    <rect>
     <x>140</x>
     <y>310</y>
     <width>141</width>
     <height>25</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(255, 255, 255);</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_7">
   <property name="geometry">
    <rect>
     <x>410</x>
     <y>110</y>
     <width>131</width>
     <height>19</height>
    </rect>
   </property>
   <property name="text">
    <string>Address Setting</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEdit_5">
   <property name="geometry">
    <rect>
     <x>540</x>
     <y>110</y>
     <width>231</width>
     <height>25</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(255, 255, 255);</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_8">
   <property name="geometry">
    <rect>
     <x>410</x>
     <y>150</y>
     <width>191</width>
     <height>19</height>
    </rect>
   </property>
   <property name="text">
    <string>Communication Protocol</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pushButton_3">
   <property name="geometry">
    <rect>
     <x>40</x>
     <y>520</y>
     <width>93</width>
     <height>28</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(170, 170, 255);</string>
   </property>
   <property name="text">
    <string>TEST</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_9">
   <property name="geometry">
    <rect>
     <x>40</x>
     <y>70</y>
     <width>131</width>
     <height>19</height>
    </rect>
   </property>
   <property name="text">
    <string>Name</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEdit_Name">
   <property name="geometry">
    <rect>
     <x>140</x>
     <y>70</y>
     <width>141</width>
     <height>25</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(255, 255, 255);</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_10">
   <property name="geometry">
    <rect>
     <x>40</x>
     <y>400</y>
     <width>161</width>
     <height>19</height>
    </rect>
   </property>
   <property name="text">
    <string>通讯模块整体命令输出</string>
   </property>
  </widget>
  <widget class="QTextEdit" name="textEdit">
   <property name="geometry">
    <rect>
     <x>410</x>
     <y>270</y>
     <width>381</width>
     <height>87</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(255, 255, 255);</string>
   </property>
   <property name="readOnly">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QPushButton" name="pushButton_action">
   <property name="geometry">
    <rect>
     <x>250</x>
     <y>520</y>
     <width>161</width>
     <height>28</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(85, 170, 255);</string>
   </property>
   <property name="text">
    <string>动作设定</string>
   </property>
  </widget>
  <widget class="QComboBox" name="comboBox_2">
   <property name="geometry">
    <rect>
     <x>410</x>
     <y>190</y>
     <width>211</width>
     <height>25</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(255, 255, 255);</string>
   </property>
  </widget>
  <widget class="QTextEdit" name="textEdit_2">
   <property name="geometry">
    <rect>
     <x>43</x>
     <y>430</y>
     <width>751</width>
     <height>71</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(255, 255, 255);</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_11">
   <property name="geometry">
    <rect>
     <x>410</x>
     <y>230</y>
     <width>111</width>
     <height>19</height>
    </rect>
   </property>
   <property name="text">
    <string>back massage</string>
   </property>
  </widget>
  <widget class="QPushButton" name="handleSetBaudRateButton">
   <property name="geometry">
    <rect>
     <x>300</x>
     <y>390</y>
     <width>51</width>
     <height>28</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(255, 170, 127);</string>
   </property>
   <property name="text">
    <string>set</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_12">
   <property name="geometry">
    <rect>
     <x>40</x>
     <y>150</y>
     <width>101</width>
     <height>19</height>
    </rect>
   </property>
   <property name="text">
    <string>PortName</string>
   </property>
  </widget>
  <widget class="QComboBox" name="PortNamecomboBox_3">
   <property name="geometry">
    <rect>
     <x>140</x>
     <y>150</y>
     <width>87</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QLabel" name="label_13">
   <property name="geometry">
    <rect>
     <x>40</x>
     <y>350</y>
     <width>101</width>
     <height>19</height>
    </rect>
   </property>
   <property name="text">
    <string>FlowControl</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEditFlowContronl">
   <property name="geometry">
    <rect>
     <x>140</x>
     <y>350</y>
     <width>141</width>
     <height>25</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(255, 255, 255);</string>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
