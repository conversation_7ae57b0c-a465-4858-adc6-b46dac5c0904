﻿生成启动时间为 2021/3/8 11:47:41。
     1>项目“C:\Users\<USER>\Desktop\GENExample\C++\基本功能\3-1 检测GTN指令是否正常执行\3-1\3-1.vcxproj”在节点 2 上(build 个目标)。
     1>InitializeBuildStatus:
         正在创建“Debug\3-1.unsuccessfulbuild”，因为已指定“AlwaysCreate”。
       ClCompile:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /ZI /nologo /W3 /WX- /Od /Oy- /D WIN32 /D _DEBUG /D _CONSOLE /D _UNICODE /D UNICODE /Gm /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Yc"StdAfx.h" /Fp"Debug\3-1.pch" /Fo"Debug\\" /Fd"Debug\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt stdafx.cpp
         stdafx.cpp
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /ZI /nologo /W3 /WX- /Od /Oy- /D WIN32 /D _DEBUG /D _CONSOLE /D _UNICODE /D UNICODE /Gm /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Yu"StdAfx.h" /Fp"Debug\3-1.pch" /Fo"Debug\\" /Fd"Debug\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt "3-1.cpp"
         3-1.cpp
       ManifestResourceCompile:
         C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\bin\rc.exe /nologo /fo"Debug\3-1.exe.embed.manifest.res" Debug\3-1_manifest.rc 
       Link:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\link.exe /ERRORREPORT:PROMPT /OUT:"C:\Users\<USER>\Desktop\GENExample\C++\基本功能\3-1 检测GTN指令是否正常执行\Debug\3-1.exe" /INCREMENTAL /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /ManifestFile:"Debug\3-1.exe.intermediate.manifest" /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /DEBUG /PDB:"C:\Users\<USER>\Desktop\GENExample\C++\基本功能\3-1 检测GTN指令是否正常执行\Debug\3-1.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:\Users\<USER>\Desktop\GENExample\C++\基本功能\3-1 检测GTN指令是否正常执行\Debug\3-1.lib" /MACHINE:X86 "Debug\3-1.exe.embed.manifest.res"
         "Debug\3-1.obj"
         Debug\stdafx.obj
         gts.lib
       Manifest:
         C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\bin\mt.exe /nologo /verbose /out:"Debug\3-1.exe.embed.manifest" /manifest "Debug\3-1.exe.intermediate.manifest"
         C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\bin\rc.exe /nologo /fo"Debug\3-1.exe.embed.manifest.res" Debug\3-1_manifest.rc 
       LinkEmbedManifest:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\link.exe /ERRORREPORT:PROMPT /OUT:"C:\Users\<USER>\Desktop\GENExample\C++\基本功能\3-1 检测GTN指令是否正常执行\Debug\3-1.exe" /INCREMENTAL /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /ManifestFile:"Debug\3-1.exe.intermediate.manifest" /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /DEBUG /PDB:"C:\Users\<USER>\Desktop\GENExample\C++\基本功能\3-1 检测GTN指令是否正常执行\Debug\3-1.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:\Users\<USER>\Desktop\GENExample\C++\基本功能\3-1 检测GTN指令是否正常执行\Debug\3-1.lib" /MACHINE:X86 "Debug\3-1.exe.embed.manifest.res"
         "Debug\3-1.obj"
         Debug\stdafx.obj
         gts.lib
         3-1.vcxproj -> C:\Users\<USER>\Desktop\GENExample\C++\基本功能\3-1 检测GTN指令是否正常执行\Debug\3-1.exe
       FinalizeBuildStatus:
         正在删除文件“Debug\3-1.unsuccessfulbuild”。
         正在对“Debug\3-1.lastbuildstate”执行 Touch 任务。
     1>已完成生成项目“C:\Users\<USER>\Desktop\GENExample\C++\基本功能\3-1 检测GTN指令是否正常执行\3-1\3-1.vcxproj”(build 个目标)的操作。

生成成功。

已用时间 00:00:01.76
