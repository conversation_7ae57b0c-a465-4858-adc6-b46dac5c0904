#ifndef ROBOTARM_H
#define ROBOTARM_H

#include <QDialog>

#include <QTcpSocket>

namespace Ui {
class RobotArm;
}

class RobotArm : public QDialog
{
    Q_OBJECT

public:
    explicit RobotArm(QWidget *parent = nullptr);
    ~RobotArm();

    QString getROBOIpAddress() const; // 声明获取IP地址的函数
    quint16 getROBOPort() const; // 声明获取端口号的函数

    QTcpSocket* getTcpSocket() const {
        return tcpSocket;
    }

private:
    Ui::RobotArm *ui;
    QTcpSocket *tcpSocket;

signals:
    void commandIssued(QPair<QString, QString> RoboOrder);





public slots:

    void on_pushButton_RConnect_clicked(); // 槽函数的声明
    void onReadyRead();//槽函数
    void pushButton_RSendTest_Common_clicked();//槽函数
    void on_pushButton_sendMain_clicked();








};

#endif // ROBOTARM_H
