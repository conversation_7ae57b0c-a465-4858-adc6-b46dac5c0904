#ifndef DIALOG_PLC_H
#define DIALOG_PLC_H

#include <QDialog>
#include <QtSerialPort/QSerialPort>
#include <QPair>

namespace Ui {
class Dialog_Plc;
}

class Dialog_Plc : public QDialog
{
    Q_OBJECT

public:
    explicit Dialog_Plc(QWidget *parent = nullptr);
    ~Dialog_Plc();

    // 新增的方法
    QString getSerialPortName() const;
    int getBaudRate() const;
    int getDataBits() const;
    QSerialPort::StopBits getStopBits() const;
    QSerialPort::Parity getParity() const;
    QSerialPort::FlowControl getFlowControl() const;
    int getSlaveAddress() const;
    int getTimeout() const;
    int getRetryCount() const;

private:
    Ui::Dialog_Plc *ui;
    QSerialPort *serialPort;
    int slaveAddress;
    int timeout;
    int retryCount;

signals:
    void addPlcOrderToMainWindow(QPair<QString, QString> plcOrder);

private slots:
    void on_pushButton_Plc_Connect_clicked();
    void onReadyRead();
    void on_pushButton_Plc_AddToMainwinder_clicked();
};

#endif // DIALOG_PLC_H
