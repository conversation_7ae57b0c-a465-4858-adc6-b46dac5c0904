/**
 * @file ethercat.cpp
 * @brief EtherCAT通信和驅動器控制實現
 *
 * 該文件實現了以下主要功能：
 * - EtherCAT主站初始化和配置
 * - 驅動器狀態監控和控制
 * - PDO/SDO通信處理
 * - 運動控制功能
 */

// Windows headers first
#include <windows.h>

// 臨時保存原始的 NULL 定義
#ifdef NULL
#define _ORIGINAL_NULL NULL
#undef NULL
#endif

// 包含 gts.h
#include "LibraryX64/gts.h"

// 恢復原始的 NULL 定義
#ifdef _ORIGINAL_NULL
#undef NULL
#define NULL _ORIGINAL_NULL
#undef _ORIGINAL_NULL
#endif

// Project headers
#include "ethercat.h"
#include "ui_ethercat.h"

// Qt headers
#include <QMessageBox>
#include <QDir>
#include <QProcess>
#include <QTimer>
#include <QThread>
#include <QTcpSocket>
#include <QInputDialog>
#include <QLineEdit>
#include "gts.h"  // 固通運動控制卡的頭文件
#include <QCloseEvent>
#include <iostream>
#include <functional>
#include "spline.h"
#include <algorithm> // 為了 std::min 和 std::max
#include <QDebug>
#include <QtCore>
#include <QDateTime>

#include <ruckig/ruckig.hpp>

int ethercat::instanceCount = 0;
bool ethercat::isCardGloballyInitialized = false;
short ethercat::globalCardCore = 1;

// 在文件開頭添加
std::function<void()> ethercat::motionCompletedCallback;

// 在文件開頭初始化靜態成員
QMap<short, AxisReferencePosition> ethercat::axisReferences;
QMap<short, QDateTime> lastAxisCheckTimes;

// 在文件的全局作用域部分添加
QMap<int, double> ethercat::s_axisLastPos;
QMap<int, double> ethercat::s_axisLastTime;
QMap<int, bool> ethercat::axisProcessingNext;
/**
 * @brief 構造函數
 * @param parent 父窗口指針
 *
 * 初始化EtherCAT通信和UI組件：
 * 1. 設置UI界面
 * 2. 檢查管理員權限
 * 3. 初始化EtherCAT主站
 * 4. 配置PDO映射
 * 5. 設置週期性更新
 */

ethercat::ethercat(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::ethercat),
    driveDetectionTimer(new QTimer(this)),
    cyclicTimer(nullptr),
    socket(new QTcpSocket(this)),
    isCardInitialized(false),
    lastKnownStatus("Not initialized")
{
    ui->setupUi(this);

    // 初始化表單ID
    initializeTableIds();

    // 在初始化時立即檢查管理員權限
    if (!checkAndRequestAdminRights()) {
        QMessageBox::critical(nullptr, "錯誤",
            "此程序需要管理員權限才能運行。\n"
            "請右鍵點擊程序，選擇'以管理員身份運行'。");
        QTimer::singleShot(100, this, &QWidget::close);
        return;
    }

    // 初始化所有計時器並設置對象名稱
    motionTimer = new QTimer(this);
    motionTimer->setObjectName("MotionTimer");

    // 基本的網絡連接設置
    connect(socket, &QTcpSocket::connected, this, &ethercat::onConnected);
    connect(socket, &QTcpSocket::disconnected, this, &ethercat::onDisconnected);
    connect(socket, &QTcpSocket::errorOccurred, this, &ethercat::onError);
    connect(socket, &QTcpSocket::readyRead, this, &ethercat::onReadyRead);

    // 添加名稱變更的連接
    connect(ui->lineEdit_ethercat_name, &QLineEdit::textChanged,
            this, &ethercat::onNameChanged);

    setupLogging();
    setupDriveUI();



    // 使用非阻塞方式初始化控制卡
    QTimer::singleShot(100, this, [this]() {
        try {
            QApplication::setOverrideCursor(Qt::WaitCursor);
            detectMotionCards();
            QApplication::restoreOverrideCursor();
        }
        catch (const std::exception& e) {
            QApplication::restoreOverrideCursor();
            QMessageBox::warning(this, "警告",
                QString("運動控制卡初始化失敗：%1\n程序將以有限功能運行。").arg(e.what()));
        }
    });

    // 初始化Watch監控設置
    setupWatchMonitoring();
}

bool ethercat::checkAndRequestAdminRights()
{
    if (!isRunningAsAdmin()) {
        QMessageBox::StandardButton reply = QMessageBox::question(nullptr,
            "權限請求",
            "此程序需要管理員權限才能運行。\n"
            "是否以管理員身份重新啟動程序？",
            QMessageBox::Yes | QMessageBox::No);

        if (reply == QMessageBox::Yes) {
            // 獲取當前程序路徑
            QString program = QCoreApplication::applicationFilePath();

            // 創建一個新的進程來以管理員身份啟動程序
            QProcess elevatedProcess;
            QStringList params;
            params << "/c" << "powershell" << "Start-Process" << program << "-Verb" << "RunAs";

            // 啟動新進程
            elevatedProcess.startDetached("cmd.exe", params);

            // 關閉當前非管理員權限的進程
            QCoreApplication::quit();
            return false;
        }
        // 如果用戶選擇不重啟，返回 false 但不關閉程序
        return false;
    }
    return true;
}

bool ethercat::isRunningAsAdmin()
{
    BOOL isAdmin = FALSE;
    PSID adminGroup;
    SID_IDENTIFIER_AUTHORITY ntAuthority = SECURITY_NT_AUTHORITY;

    if (AllocateAndInitializeSid(&ntAuthority, 2,
        SECURITY_BUILTIN_DOMAIN_RID,
        DOMAIN_ALIAS_RID_ADMINS,
        0, 0, 0, 0, 0, 0,
        &adminGroup))
    {
        CheckTokenMembership(NULL, adminGroup, &isAdmin);
        FreeSid(adminGroup);
    }

    return isAdmin == TRUE;
}

ethercat::~ethercat() {
    if (motionTimer) {
        motionTimer->stop();
        delete motionTimer;
    }

    try {
        // 檢查是否還有其他打開的 EtherCAT 窗口
        if (instanceCount <= 1) {
            closeCard();
            isCardGloballyInitialized = false;  // 重置全局初始化標誌
        }
    } catch (...) {
        qDebug() << "Error during card cleanup in destructor";
    }

    instanceCount--;  // 減少實例計數
    delete ui;

    if (socket && socket->state() == QAbstractSocket::ConnectedState) {
        socket->disconnectFromHost();
    }
}



void ethercat::on_pushButton_ethercat_connect_clicked()
{
    if (socket->state() == QAbstractSocket::ConnectedState) {
        socket->disconnectFromHost();
    } else {
        QString ip = ui->lineEdit_ethercat_ip->text();
        int port = ui->lineEdit__ethercat_port->text().toInt();

        socket->connectToHost(ip, port);
    }
}

void ethercat::onConnected()
{
    emit connectionStatusChanged(true);
    ui->pushButton_ethercat_connect->setText("Disconnect");
    QMessageBox::information(this, "Connection", "Successfully connected to EtherCAT bus.");
}

void ethercat::onDisconnected()
{
    emit connectionStatusChanged(false);
    ui->pushButton_ethercat_connect->setText("Connect");
    QMessageBox::information(this, "Connection", "Disconnected from EtherCAT bus.");
}

void ethercat::onError(QAbstractSocket::SocketError socketError)
{
    QString errorMessage = QString("Socket error: %1").arg(socket->errorString());
    QMessageBox::critical(this, "Connection Error", errorMessage);
}







/**
 * @brief 運動控制卡初始化主函數
 * 該函數負責整個運動控制卡的初始化流程，包括：
 * 1. 硬件初始化
 * 2. EtherCAT通信建立
 * 3. 軸使能
 */
void ethercat::detectMotionCards() {
    ui->comboBox_movepic->clear();

    // 檢查是否已經全局初始化
    if (isCardGloballyInitialized) {
        qDebug() << "控制卡已經被其他實例初始化，跳過初始化流程";
        cardCore = globalCardCore;  // 使用已初始化的核心
        isCardInitialized = true;
        ui->comboBox_movepic->addItem("使用已初始化的控制卡");

        // 直接進行驅動器檢測
        detectDrives();
        return;
    }

    short sRtn;
    short sEcatSts;

    // 1. 打開控制卡 - 修改參數為 (5, 1)
    sRtn = GTN_Open(5, 1);
    if (sRtn != 0) {
        isCardInitialized = false;
        ui->comboBox_movepic->addItem("控制卡初始化失敗");
        throw std::runtime_error("控制卡訪問失敗，錯誤碼：" + QString::number(sRtn).toStdString());
    }




    // 等待復位完成
    QThread::msleep(1000);




    // 2. 初始化 EtherCAT 通信
    sRtn = GTN_InitEcatComm(cardCore);
    if (sRtn != 0) {
        closeCard();
        throw std::runtime_error("EtherCAT 通信錯誤，錯誤碼：" + QString::number(sRtn).toStdString());
    }

    // 3. 等待 EtherCAT 通信就緒 - 修改為無限循環直到成功
    do {
        sRtn = GTN_IsEcatReady(cardCore, &sEcatSts);
        if (sRtn != 0) {
            closeCard();
            throw std::runtime_error("檢查 EtherCAT 就緒狀態失敗");
        }
        QThread::msleep(100);
    } while (sEcatSts != 1);

    // 4. 啟動 EtherCAT 通信
    sRtn = GTN_StartEcatComm(cardCore);
    if (sRtn != 0) {
        closeCard();
        throw std::runtime_error("啟動 EtherCAT 通信失敗，錯誤碼：" + QString::number(sRtn).toStdString());
    }

    // 6. 加載配置文件
    sRtn = GTN_LoadConfig(cardCore, "GTS800.cfg");
    if (sRtn != 0) {
        closeCard();
        throw std::runtime_error("配置文件加載失敗，錯誤碼：" + QString::number(sRtn).toStdString());
    }

    // 5. 系統復位
    sRtn = GTN_Reset(cardCore);
    if (sRtn != 0) {
        closeCard();
        throw std::runtime_error("系統復位失敗，錯誤碼：" + QString::number(sRtn).toStdString());
    }



    // 等待復位完成
    QThread::msleep(1000);



    // 新增：初始化所有軸的補償
    for (short axis = 1; axis <= 8; axis++) {
        if (!initializeCompensation(axis)) {
            ui->comboBox_movepic->addItem(QString("軸 %1 補償初始化失敗").arg(axis));
        } else {
            ui->comboBox_movepic->addItem(QString("軸 %1 補償初始化成功").arg(axis));
        }
    }

    // 初始化各軸的參考位置
    for (short axis = 1; axis <= 8; axis++) {  // 假設有8個軸
        if (!initializeAxisReference(axis)) {
            QMessageBox::warning(this, "警告",
                QString("軸 %1 參考位置初始化失敗").arg(axis));
        }
    }

    isCardInitialized = true;
    ui->comboBox_movepic->addItem("運動控制卡初始化成功");

    // 7. 清除所有軸的狀態
    sRtn = GTN_ClrSts(cardCore, 1, 8);
    if (sRtn != 0) {
        ui->comboBox_movepic->addItem("清除軸狀態失敗");
    }

    // 等待所有操作完成
    QThread::msleep(1000);

    // 8. 調用軸檢測函數
    detectDrives();

    // 等待軸檢測和使能完成
    QThread::msleep(1000);

    // ================== 【新增的對準步驟】 ==================
    // 根據專家的建議，在第一次運動前對所有軸進行位置校準（清零）
    // 這可以確保控制器的邏輯位置與物理位置在程序啟動時就達成一致，從而避免首次運動的"瞬移"問題。
    qDebug() << "===== [對準步驟] 檢查所有軸的絕對位置並緩慢歸零 =====";

    // 先檢查所有軸的當前位置
    for (short axis = 1; axis <= 8; ++axis) {
        long status;
        // 檢查軸是否真實存在並已使能 (位9=1表示已使能，位10=1表示運動中)
        if (GTN_GetSts(cardCore, axis, &status) == 0 && (status & 0x200)) {
            qDebug() << QString("軸 %1 狀態檢查: 0x%2").arg(axis).arg(status, 0, 16);

            // 確認軸真的已經使能，而且不在運動中
            if (!(status & 0x400)) { // 0x400 表示運動中，確保軸處於停止狀態
            // 【修改】改用 EtherCAT 編碼器絕對位置來判斷是否需要歸零，而非規劃位置
            long ecatEncPos;  // 改為 long 類型，用於 EtherCAT 編碼器位置
            // 獲取當前 EtherCAT 編碼器絕對位置（真實的物理位置）
            if (GTN_GetEcatEncPos(cardCore, axis, &ecatEncPos) == 0) {
                qDebug() << QString("  軸 %1 當前 EtherCAT 編碼器絕對位置: %2").arg(axis).arg(ecatEncPos);

                // 存儲絕對位置差異到變量
                axisAbsolutePositions[axis] = ecatEncPos;
                qDebug() << QString("  軸 %1 絕對位置差異: %2 (已存儲)").arg(axis).arg(ecatEncPos);
            } else {
                qDebug() << QString("  警告: 無法獲取軸 %1 的當前 EtherCAT 編碼器位置").arg(axis);
            }
            } else {
                qDebug() << QString("  軸 %1 正在運動中，跳過位置檢查").arg(axis);
            }
        } else {
            qDebug() << QString("  軸 %1 未使能或不存在，跳過位置檢查").arg(axis);
        }
    }

    // 輸出所有軸的絕對位置差異總結
    qDebug() << "===== 軸絕對位置差異總結 =====";
    for (auto it = axisAbsolutePositions.begin(); it != axisAbsolutePositions.end(); ++it) {
        qDebug() << QString("軸 %1: %2").arg(it.key()).arg(it.value());
    }
    // =========================================================



    // 初始化成功後設置全局標誌
    isCardGloballyInitialized = true;
    globalCardCore = cardCore;
}

/**
 * 檢測可用的軸（驅動器）
 * 檢測並列出所有已連接且正常工作的軸
 */
void ethercat::detectDrives() {
    // 清空並禁用下拉選單
    ui->comboBox_move_drive->clear();
    ui->comboBox_move_drive->setEnabled(false);

    // 檢查控制卡初始化狀態
    if (!isCardInitialized && !isCardGloballyInitialized) {
        logError("控制卡未初始化", 0);
        ui->comboBox_move_drive->setEnabled(true);
        return;
    }

    // 獲取系統中的最大軸數
    short maxAxis;
    short ioCount;
    short sRtn = GTN_GetEcatSlaves(cardCore, &maxAxis, &ioCount);
    if (sRtn != 0) {
        logError("獲取最大軸數失敗", sRtn);
        ui->comboBox_move_drive->setEnabled(true);
        return;
    }

    qDebug() << "配置文件中的最大軸數:" << maxAxis;

    // 遍歷每個軸
    for (short axis = 1; axis <= maxAxis; axis++) {
        // 檢查軸模式
        unsigned short drvMode;
        sRtn = GTN_GetEcatAxisMode(cardCore, axis, &drvMode);
        if (sRtn != 0) {
            continue;  // 跳過未配置的軸
        }

        // 獲取當前軸狀態
        long status;
        sRtn = GTN_GetSts(cardCore, axis, &status);
        if (sRtn != 0) {
            logError("獲取軸狀態失敗", sRtn, axis);
            continue;
        }

        // 檢查軸的使能狀態
        if (!(status & (0x200 | 0x400))) {
            // 軸未使能，嘗試使能
            if (enableAxis(axis)) {
                qDebug() << QString("軸 %1 自動使能成功").arg(axis);
                ui->comboBox_movepic->addItem(QString("軸 %1 自動使能成功").arg(axis));

                // 重新獲取使能後的狀態
                sRtn = GTN_GetSts(cardCore, axis, &status);
                if (sRtn != 0) {
                    logError("獲取軸狀態失敗", sRtn, axis);
                    continue;
                }
            } else {
                qDebug() << QString("軸 %1 自動使能失敗").arg(axis);
                ui->comboBox_movepic->addItem(QString("軸 %1 自動使能失敗").arg(axis));
                continue;
            }
        }

        // 根據狀態位生成狀態文本
        QString statusText;
        if (status & 0x400) {
            statusText = "已使能";
        } else if (status & 0x200) {
            statusText = "準備好";
        } else {
            statusText = "未使能";
        }

        // 添加軸信息到下拉選單
        QString axisInfo = QString("軸 %1 - %2").arg(axis).arg(statusText);
        ui->comboBox_move_drive->addItem(axisInfo);

        // 記錄詳細狀態到日誌
        QString detailStatus = QString("軸 %1 狀態: 0x%2")
                             .arg(axis)
                             .arg(status, 0, 16);
        qDebug() << detailStatus;
        ui->comboBox_movepic->addItem(detailStatus);
    }

    // 如果沒有檢測到任何軸
    if (ui->comboBox_move_drive->count() == 0) {
        ui->comboBox_move_drive->addItem("未檢測到可用的軸");
    }

    // 啟用下拉選單
    ui->comboBox_move_drive->setEnabled(true);
}
/**
 * 使能單個軸
 * axis: 軸號
 * return: 使能是否成功
 */
bool ethercat::enableAxis(short axis) {
    // 檢查控制卡初始化狀態
    if (!isCardInitialized) {
        logError("控制卡未初始化", 0, axis);
        return false;
    }

    // 檢查軸配置
    unsigned short drvMode;
    short sRtn = GTN_GetEcatAxisMode(cardCore, axis, &drvMode);
    if (sRtn != 0) {
        logError("軸未在配置文件中啟用", sRtn, axis);
        return false;
    }



    // 清除軸狀態
    sRtn = GTN_ClrSts(cardCore, axis);
    if (sRtn != 0) {
        logError("清除軸狀態失敗", sRtn, axis);
        return false;
    }

    // 2. 設置為點位運動模式
    sRtn = GTN_PrfTrap(cardCore, axis);
    if (sRtn != 0) {
        logError("設置運動模式失敗enableAxis", sRtn, axis);
        return false;
    }

    // 3. 設置運動參數
    TTrapPrm trap;
    trap.acc = 1;
    trap.dec = 1;
    trap.smoothTime = 25;
    sRtn = GTN_SetTrapPrm(cardCore, axis, &trap);
    if (sRtn != 0) {
        logError("設置運動參數失敗01", sRtn, axis);
        return false;
    }

    // 使能軸
    sRtn = GTN_AxisOn(cardCore, axis);
    if (sRtn != 0) {
        logError("使能失敗", sRtn, axis);
        return false;
    }

    // 等待使能完成
    Sleep(500);

    // 檢查使能狀態
    long status;
    sRtn = GTN_GetSts(cardCore, axis, &status);
    if (sRtn != 0) {
        logError("獲取軸狀態失敗", sRtn, axis);
        return false;
    }

    // 檢查是否準備好或已使能
    if (!(status & (0x200 | 0x400))) {
        logError("使能後狀態異常", sRtn, axis, status);
        return false;
    }

        // 監控補償效果
    double prfPos, encPos;
    long ecatEncPos;  // 只宣告一次，使用 long 類型
    GTN_GetPrfPos(cardCore, axis, &prfPos);
    GTN_GetEncPos(cardCore, axis, &encPos);
    GTN_GetEcatEncPos(cardCore, axis, &ecatEncPos);

    qDebug() << "===== 位置監控 =====";
    qDebug() << "規劃位置:" << prfPos;
    qDebug() << "編碼器位置:" << encPos;
    qDebug() << "EtherCAT編碼器絕對位置:" << ecatEncPos;
    qDebug() << "補償量:" << (encPos - prfPos);

    // 獲取補償模式
    short cycleMode, revCompCycle;  // 需要兩個short類型參數
    if (GTN_GetLeadScrewCompMode(cardCore, axis, &cycleMode, &revCompCycle) == 0) {
        qDebug() << "補償模式:" << (cycleMode == 1 ? "啟用" : "禁用");
        qDebug() << "補償循環模式:" << revCompCycle;
    }

    // 在 enableAxis 函數中添加，位置在 GTN_GetEcatAxisMode 之後
    //short slavePdo;
    //short sRtn = GTN_GetEcatSlavePdo(cardCore, axis, &slavePdo);
    // 替換為：
    // 正確的 PDO 讀取配置
    unsigned short object = 0x6063;        // Position actual value
    unsigned char objectSubIndex = 0x00;   // SubIndex
    unsigned int dataSize = 4;             // 4 bytes for position data
    unsigned char pdoData[4];              // Buffer for position data

    sRtn = GTN_GetEcatSlavePdo(cardCore,
                            (unsigned short)axis,  // station
                            object,                // index
                            objectSubIndex,        // subIndex
                            pdoData,              // pData
                            dataSize);            // data_size

    if (sRtn == 0) {
        qDebug() << QString("軸 %1 PDO 數據:").arg(axis);
        for(unsigned int i = 0; i < dataSize; i++) {
            qDebug() << QString("  Byte %1: 0x%2")
                        .arg(i)
                        .arg(pdoData[i], 2, 16, QChar('0'));
        }
    }

    // 添加詳細的 PDO 信息診斷
    qDebug() << QString("軸 %1 診斷信息:").arg(axis);
    qDebug() << QString("- EtherCAT 編碼器位置: %1").arg(ecatEncPos);

    // 如果需要，可以添加位置比例轉換
    //double actualPosition = ecatEncPos / 10000.0;  // 假設比例因子為 10000
    //qDebug() << QString("- 轉換後的實際位置: %1").arg(actualPosition);

    return true;
}

/**
 * @brief 統一的錯誤記錄函數
 * @param message 錯誤信息
 * @param errorCode 錯誤代碼
 * @param axis 可選的軸號，默認為-1表示非軸相關錯誤
 * @param status 可選的軸狀態，默認為0
 */
void ethercat::logError(const QString& message, short errorCode, short axis, long status) {
    QString errorMsg = QString("[錯誤] %1 (錯誤碼: %2)").arg(message).arg(errorCode);

    if (axis != -1) {
        errorMsg += QString(" - 軸: %1").arg(axis);
    }

    if (status != 0) {
        errorMsg += QString(" - 狀態: 0x%1").arg(status, 0, 16);
    }

    qDebug() << errorMsg;
    ui->comboBox_movepic->addItem(errorMsg);
}





/**
 * @brief 更新當前選中軸的狀態顯示
 * 該函數由定時器定期調用，用於實時更新軸的狀態信息
 */
void ethercat::updateDriveStatus(bool readFromCard) {
    int currentDrive = ui->comboBox_move_drive->currentIndex();
    if (currentDrive < 0) {
        ui->lineEdit_drive_sis->setText("未選擇軸");
        return;
    }

    short axis = currentDrive + 1;
    long status;
    QString positionInfo;

    if (readFromCard) {
        // 從控制卡讀取狀態和位置速度
        if (GTN_GetSts(cardCore, axis, &status) != 0) {
            logError("狀態讀取失敗", -1, axis);
            return;
        }
        double currentPos, currentVel;
        GTN_GetPrfPos(cardCore, axis, &currentPos);
        GTN_GetPrfVel(cardCore, axis, &currentVel);
        positionInfo = QString("位置: %.2f, 速度: %.2f").arg(currentPos).arg(currentVel);
    } else {
        status = currentStatus; // 使用已有狀態
        positionInfo = QString("位置: %1").arg(currentPosition);
    }

    QString statusText = getAxisStatusText(status);
    updateStatusDisplay(axis, status, statusText, positionInfo);
}


/**
 * @brief 當用戶在comboBox_move_drive下拉框中選擇不同軸時觸發的槽函數
 * @param index 下拉框中選中項的索引值（從0開始）
 *
 * 功能：
 * 1. 讀取選中軸的當前狀態
 * 2. 在lineEdit_drive_sis中顯示該軸的狀態信息
 * 3. 根據狀態更新顯示顏色
 */
void ethercat::onDriveSelected(int index) {
    // 檢查是否有選中有效的軸
    // index < 0 表示沒有選中任何軸（比如清空下拉框時）
    if (index < 0) {
        ui->lineEdit_drive_sis->setText("未選擇軸");
        return;
    }

    // 將下拉框的索引轉換為實際的軸號
    // 因為軸號從1開始，而下拉框索引從0開始，所以需要+1
    short axis = index + 1;
    long status;  // 用於存儲讀取到的軸狀態

    // 調用運動控制卡API讀取軸的當前狀態
    // GTN_GetSts返回0表示成功，非0表示失敗
    if (GTN_GetSts(cardCore, axis, &status) != 0) {
        ui->lineEdit_drive_sis->setText("軸狀態讀取失敗");
        return;
    }

    // 將狀態碼轉換為可讀的文本信息
    // getAxisStatusText函數會解析status中的各個位，轉換為對應的狀態描述
    QString statusText = getAxisStatusText(status);
    ui->lineEdit_drive_sis->setText(statusText);

    // 根據狀態更新顯示顏色
    // 例如：報警狀態顯示紅色，正常運行顯示綠色等
    updateStatusColor(status);
}


/**
 * @brief 設置驅動器相關的UI界面
 *
 * 該函數負責初始化和配置與驅動器控制相關的UI元素：
 * 1. 設置只讀文本框屬性，用於顯示狀態信息
 * 2. 調用驅動器檢測函數
 * 3. 設置刷新按鈕的樣式
 */
void ethercat::setupDriveUI() {
    // 設置狀態顯示文本框為只讀模式，防止用戶手動修改
    ui->lineEdit_drive_sis->setReadOnly(true);

    // 調用檢測函數，檢查並初始化可用的運動控制卡
    detectMotionCards();

    // 設置刷新按鈕的樣式表
    // 定義按鈕的正常、按下和禁用狀態的外觀
    ui->pushButton_refresh_drives->setStyleSheet(
        // 正常狀態：淺灰背景，深灰邊框，圓角效果
        "QPushButton {"
        "    background-color: #f0f0f0;"  // 背景色：淺灰
        "    border: 2px solid #c0c0c0;"  // 邊框：2像素實線深灰
        "    border-radius: 4px;"         // 圓角：4像素
        "    padding: 4px;"               // 內邊距：4像素
        "}"
        // 按下狀態：較深的背景色和邊框
        "QPushButton:pressed {"
        "    background-color: #e0e0e0;"  // 背景色：中灰
        "    border-color: #a0a0a0;"      // 邊框色：深灰
        "}"
        // 禁用狀態：最淺的背景色和邊框
        "QPushButton:disabled {"
        "    background-color: #d0d0d0;"  // 背景色：最淺灰
        "    border-color: #b0b0b0;"      // 邊框色：中灰
        "}"
    );
}

/**
 * @brief 刷新按鈕點擊事件處理函數
 *
 * 當用戶點擊刷新按鈕時：
 * 1. 禁用按鈕並更改外觀，提供視覺反饋
 * 2. 執行驅動器檢測
 * 3. 延遲1秒後恢復按鈕狀態
 */
void ethercat::on_pushButton_refresh_drives_clicked() {
    // 禁用按鈕並更改文本，防止重複點擊
    ui->pushButton_refresh_drives->setEnabled(false);
    ui->pushButton_refresh_drives->setText("刷新中...");
    // 設置禁用狀態的樣式
    ui->pushButton_refresh_drives->setStyleSheet(
        "background-color: #e0e0e0;"    // 背景色：中灰
        "border-color: #a0a0a0;"        // 邊框色：深灰
    );

    // 執行驅動器檢測操作
    detectDrives();

    // 使用定時器延遲1秒後恢復按鈕狀態
    QTimer::singleShot(1000, this, [this]() {
        // 重新啟用按鈕
        ui->pushButton_refresh_drives->setEnabled(true);
        // 恢復原始文本
        ui->pushButton_refresh_drives->setText("刷新");
        // 恢復原始樣式
        ui->pushButton_refresh_drives->setStyleSheet(
            "QPushButton {"
            "    background-color: #f0f0f0;"  // 背景色：淺灰
            "    border: 2px solid #c0c0c0;"  // 邊框：2像素實線深灰
            "    border-radius: 4px;"         // 圓角：4像素
            "    padding: 4px;"               // 內邊距：4像素
            "}"
        );
    });
}







/**
 * @brief 創建EtherCAT通信協議的數據幀
 *
 * @param command  命令類型（如：讀/寫SDO、PDO等）
 * @param index    對象字典索引（16位）
 * @param subIndex 對象字典子索引（8位）
 * @param data     要發送的數據
 * @return QByteArray 返回組裝好的EtherCAT數據幀
 *
 * 數據幀格式：
 * +--------+--------+--------+--------+--------+--------+--------+
 * | 幀頭   | 命令   | 索引高字節 | 索引低字節 | 子索引 | 數據長度 | 數據... |
 * | 0x88   | 1字節  | 1字節     | 1字節     | 1字節   | 1字節    | N字節   |
 * +--------+--------+--------+--------+--------+--------+--------+
 *
 * 說明：
 * 1. 幀頭(0x88)：固定值，表示這是一個EtherCAT數據幀
 * 2. 命令：指示操作類型，如：
 *    - 0x40：SDO讀取請求
 *    - 0x23：SDO寫入請求
 *    - 0x60：SDO讀取響應
 *    - 0x43：SDO寫入響應
 * 3. 索引和子索引：指定要訪問的對象字典條目
 * 4. 數據長度：後續數據的字節數
 * 5. 數據：實際要傳輸的數據內容
 */
QByteArray ethercat::createEtherCATFrame(uint8_t command, uint16_t index,
                                        uint8_t subIndex, const QByteArray &data)
{
    QByteArray frame;

    // 添加幀頭 0x88（EtherCAT命令標識符）
    frame.append(char(0x88));

    // 添加命令類型（如：SDO讀/寫）
    frame.append(char(command));

    // 添加16位索引值（分高低字節）
    frame.append((index >> 8) & 0xFF);    // 高字節
    frame.append(index & 0xFF);           // 低字節

    // 添加8位子索引值
    frame.append(subIndex);

    // 添加數據長度
    frame.append(char(data.size()));

    // 如果有數據，則添加到幀中
    if (!data.isEmpty()) {
        frame.append(data);
    }

    return frame;
}



/**
 * @brief 發送驅動器控制命令
 * @param cmd DriveCommand結構體，包含命令類型和參數
 *
 * 該函數實現CiA402驅動器狀態機的控制：
 *
 * 控制字(0x6040)位定義：
 * Bit 0-3: 狀態機控制
 *   - 0x000F: 使能操作
 *   - 0x0007: 關閉使能
 *   - 0x0006: 開啟驅動器
 *   - 0x0002: 快速停止
 *   - 0x0000: 關閉驅動器
 * Bit 7: 故障復位(0x0080)
 *
 * 對象字典索引：
 * - 0x6040: 控制字
 * - 0x607A: 目標位置
 * - 0x6081: 運行速度
 *
 * @note 所有命令通過SDO(服務數據對象)方式發送
 */
void ethercat::sendDriveCommand(const DriveCommand &cmd)
{
    uint16_t slavePos = cmd.slavePosition;  // 獲取從站位置

    switch(cmd.type) {
        case DriveCommand::ENABLE_OPERATION:
            // 使能操作：設置控制字為0x000F
            // Bit 0-3: 1111b - 使能操作狀態
            ecMaster.writeSDO(slavePos, 0x6040, 0x00, 0x000F);
            break;

        case DriveCommand::SET_POSITION:
            // 設置目標位置：寫入位置值到0x607A
            ecMaster.writeSDO(slavePos, 0x607A, 0x00, cmd.targetValue);
            break;

        case DriveCommand::SET_VELOCITY:
            // 設置運行速度：寫入速度值到0x6081
            ecMaster.writeSDO(slavePos, 0x6081, 0x00, cmd.targetValue);
            break;

        case DriveCommand::QUICK_STOP:
            // 快速停止：設置控制字為0x0002
            ecMaster.writeSDO(slavePos, 0x6040, 0x00, 0x0002);
            break;

        case DriveCommand::FAULT_RESET:
            // 故障復位：設置控制字的Bit 7
            ecMaster.writeSDO(slavePos, 0x6040, 0x00, 0x0080);
            break;

        case DriveCommand::DISABLE_OPERATION:
            // 關閉使能：設置控制字為0x0007
            ecMaster.writeSDO(slavePos, 0x6040, 0x00, 0x0007);
            break;

        case DriveCommand::SWITCH_ON:
            // 開啟驅動器：設置控制字為0x0006
            ecMaster.writeSDO(slavePos, 0x6040, 0x00, 0x0006);
            break;

        case DriveCommand::SWITCH_OFF:
            // 關閉驅動器：設置控制字為0x0000
            ecMaster.writeSDO(slavePos, 0x6040, 0x00, 0x0000);
            break;
    }
}



void ethercat::transitionToDriveState(DriveStatus targetState)
{
    DriveCommand cmd;
    switch(targetState) {
        case DS_OPERATION_ENABLED:
            cmd.type = DriveCommand::ENABLE_OPERATION;
            break;
        case DS_READY_TO_SWITCH_ON:
            cmd.type = DriveCommand::DISABLE_OPERATION;
            break;
        // 需要添加其他狀態轉換
        case DS_SWITCHED_ON:
            cmd.type = DriveCommand::SWITCH_ON;
            break;
        case DS_QUICK_STOP_ACTIVE:
            cmd.type = DriveCommand::QUICK_STOP;
            break;
        case DS_FAULT:
            cmd.type = DriveCommand::FAULT_RESET;
            break;
    }
    sendDriveCommand(cmd);
}
////////////////////////////////////////////////
/**
 * @brief 處理軸響應
 * @param data 接收到的數據
 *
 * 處理軸返回的數據：
 * - 0x6041: 更新狀態字
 * - 0x6064: 更新實際位置
 */
void ethercat::handleDriveResponse(const QByteArray &data)
{
    // 基本驗證
    if (data.isEmpty()) {
        ui->textEdit_ethercat_back_m->append("收到空響應");
        return;
    }

    // 記錄原始數據
    ui->textEdit_ethercat_back_m->append(QString("收到響應: 0x%1").arg(QString(data.toHex())));

    try {
        // 檢查數據長度
        if (data.size() < 2) {
            throw std::runtime_error("響應數據長度不足");
        }

        // 解析命令類型
        uint8_t command = static_cast<uint8_t>(data[0]);

        // 處理讀取響應
        if (command == 0x2F) {
            if (data.size() < 8) {
                throw std::runtime_error("讀取響應數據長度不足");
            }

            // 解析索引和子索引
            uint16_t index = (static_cast<uint8_t>(data[2]) << 8) |
                            static_cast<uint8_t>(data[3]);
            uint8_t subIndex = static_cast<uint8_t>(data[4]);

            // 根據不同索引處理數據
            switch(index) {
                case 0x6041: { // 狀態字
                    uint16_t statusWord = (static_cast<uint8_t>(data[6]) << 8) |
                                         static_cast<uint8_t>(data[7]);
                    updateDriveStatus(statusWord & 0x0F);

                    ui->textEdit_ethercat_back_m->append(
                        QString("軸狀態更新: 0x%1").arg(statusWord, 4, 16, QChar('0'))
                    );
                    break;
                }
                case 0x6064: { // 實際位置
                    if (data.size() >= 10) {
                        int32_t position = (static_cast<uint8_t>(data[6]) << 24) |
                                        (static_cast<uint8_t>(data[7]) << 16) |
                                        (static_cast<uint8_t>(data[8]) << 8) |
                                         static_cast<uint8_t>(data[9]);
                        updatePositionDisplay(position);

                        ui->textEdit_ethercat_back_m->append(
                            QString("位置更新: %1").arg(position)
                        );
                    }
                    break;
                }
                default:
                    ui->textEdit_ethercat_back_m->append(
                        QString("未處理的索引: 0x%1").arg(index, 4, 16, QChar('0'))
                    );
                    break;
            }
        } else {
            ui->textEdit_ethercat_back_m->append(
                QString("未知命令: 0x%1").arg(command, 2, 16, QChar('0'))
            );
        }
    }
    catch (const std::exception& e) {
        ui->textEdit_ethercat_back_m->append(QString("處理響應時出錯: %1").arg(e.what()));
    }
}


////////////////////////////////////////////////////////////////
void ethercat::onMoveButtonClicked()
{
    if (!isCardInitialized) {
        QMessageBox::warning(this, "錯誤", "控制卡未初始化");
        return;
    }

    int targetPosition = ui->spinBox_position->value();
    int targetVelocity = ui->spinBox_velocity->value();

    moveToPosition(targetPosition, targetVelocity);
}

/**
 * @brief 移動到指定位置
 * @param position 目標位置
 * @param velocity 運行速度
 *
 * 執行位置控制：
 * 1. 使能驅動器
 * 2. 設置目標位置
 * 3. 設置運行速度
 *
 * @note 使用100ms延時確保命令按順序執行
 */

/////////////////初始化補償函數/////////////////////////////
bool ethercat::initializeCompensation(short axis) {
    try {
        qDebug() << "開始初始化補償 - 軸號:" << axis;

        const int LEAD_SCREW_N = 20;  // 增加到20個點

        // 正向補償值數組
        std::vector<long> compPos = {
            1000, 1100, 1200, 1300, 1400,
            1500, 1600, 1700, 1800, 1900,
            2000, 2100, 2200, 2300, 2400,
            2500, 2600, 2700, 2800, 3000
        };

        // 負向補償值數組
        std::vector<long> compNeg = {
            1000, 1100, 1200, 1300, 1400,
            1500, 1600, 1700, 1800, 1900,
            2000, 2100, 2200, 2300, 2400,
            2500, 2600, 2700, 2800, 3000
        };

        // 設置補償參數
        short sRtn = GTN_SetLeadScrewComp(cardCore, axis, LEAD_SCREW_N,
                                       100, 3000000,  // 使用與示例相同的範圍
                                       compPos.data(), compNeg.data());
        if (sRtn != 0) {
            logError("設置補償數據失敗", sRtn, axis);
            return false;
        }

        // 直接啟用補償
        sRtn = GTN_EnableLeadScrewComp(cardCore, axis, 1);
        if (sRtn != 0) {
            logError("啟用補償失敗", sRtn, axis);
            return false;
        }

        qDebug() << "補償初始化成功 - 軸號:" << axis;
        return true;
    }
    catch (const std::exception& e) {
        qDebug() << "補償初始化異常:" << e.what();
        return false;
    }
}



void ethercat::moveToPosition(int position, int velocity) {
    if (!isCardInitialized) {
        QMessageBox::warning(this, "錯誤", "控制卡未初始化");
        return;
    }

    short axis = ui->comboBox_move_drive->currentIndex() + 1;
    short sRtn;

    // 檢查軸狀態
    long status;
    sRtn = GTN_GetSts(cardCore, axis, &status);
    if (sRtn != 0) {
        logError("獲取軸狀態失敗", sRtn, axis);
        return;
    }

    // 檢查使能狀態
    if (!(status & 0x400)) {
        if (!enableAxis(axis)) {
            QMessageBox::warning(this, "警告", "軸未完全使能，請檢查伺服狀態");
            return;
        }
    }

    // 如果是歸零操作
    if (position == 0) {
        // 2. 切換到回零模式
        sRtn = GTN_SetHomingMode(cardCore, axis, 6);
        if (sRtn != 0) {
            logError("設置回零模式失敗", sRtn, axis);
            return;
        }

        // 3. 設置回零參數
        sRtn = GTN_SetEcatHomingPrm(cardCore, axis, 1, velocity, velocity/2, velocity*10, 0, 0);
        if (sRtn != 0) {
            logError("設置回零參數失敗", sRtn, axis);
            return;
        }

        // 4. 啟動回零
        sRtn = GTN_StartEcatHoming(cardCore, axis);
        if (sRtn != 0) {
            logError("啟動回零失敗", sRtn, axis);
            return;
        }

        // 5. 等待回零完成
        unsigned short homeStatus;
        do {
            sRtn = GTN_GetEcatHomingStatus(cardCore, axis, &homeStatus);
            QThread::msleep(10);
        } while (homeStatus != 3);

        // 6. 切換回位置控制模式
        sRtn = GTN_SetHomingMode(cardCore, axis, 8);
        if (sRtn != 0) {
            logError("切換位置模式失敗", sRtn, axis);
            return;
        }

        ui->comboBox_movepic->addItem(QString("軸 %1 歸零完成").arg(axis));
    }
    else {
        // 正常運動模式，補償已經啟用，無需重新初始化

        // 設置點位運動模式
        sRtn = GTN_PrfTrap(cardCore, axis);
        if (sRtn != 0) {
            logError("設置運動模式失敗", sRtn, axis);
            return;
        }

        // 設置運動參數（優化波形平滑度）
        TTrapPrm trap;
        trap.acc = velocity * 10;     // 降低加速度係數，從100倍降到10倍
        trap.dec = velocity * 10;     // 降低減速度係數
        trap.smoothTime = 50;         // 大幅增加平滑時間，從2增加到50

        sRtn = GTN_SetTrapPrm(cardCore, axis, &trap);
        if (sRtn != 0) {
            logError("設置運動參數失敗02", sRtn, axis);
            return;
        }

        // 設置目標位置和速度
        sRtn = GTN_SetPos(cardCore, axis, position);
        if (sRtn != 0) {
            logError("設置位置失敗", sRtn, axis);
            return;
        }

        sRtn = GTN_SetVel(cardCore, axis, velocity);
        if (sRtn != 0) {
            logError("設置速度失敗", sRtn, axis);
            return;
        }

        // 啟動運動
        sRtn = GTN_Update(cardCore, 1 << (axis - 1));
        if (sRtn != 0) {
            logError("啟動運動失敗", sRtn, axis);
            return;
        }

        ui->comboBox_movepic->addItem(
            QString("軸 %1 開始運動 - 目標位置: %2, 速度: %3")
            .arg(axis)
            .arg(position)
            .arg(velocity)
        );

        // 等待運動完成
        double actualPosition;
        do {
            GTN_GetSts(cardCore, axis, &status);
            GTN_GetPrfPos(cardCore, axis, &actualPosition);

            // 獲取編碼器位置用於計算補償量
            double encPosition;
            GTN_GetEncPos(cardCore, axis, &encPosition);

            // 計算實際補償量（編碼器位置和規劃位置的差值）
            double compensationValue = encPosition - actualPosition;

            // 使用 qDebug 輸出補償監控信息
            qDebug() << QString("軸 %1 補償監控 - 規劃位置: %.3f, 編碼器位置: %.3f, 補償量: %.3f")
                        .arg(axis)
                        .arg(actualPosition)
                        .arg(encPosition)
                        .arg(compensationValue);

            if (!(status & 0x004) && !(status & 0x008)) {
                QThread::msleep(10);
                GTN_GetSts(cardCore, axis, &status);
                if (!(status & 0x004) && !(status & 0x008)) {
                    break;
                }
            }
            QThread::msleep(100); // 增加延時以避免過多的顯示
        } while (true);

        ui->comboBox_movepic->addItem(
            QString("軸 %1 運動完成 - 實際位置: %2")
            .arg(axis)
            .arg(actualPosition)
        );
    }
}

void ethercat::updatePositionDisplay(int32_t position)
{
    currentPosition = position;
    // 在狀態顯示中添加位置信息
    QString currentStatus = ui->lineEdit_drive_sis->text();
    ui->lineEdit_drive_sis->setText(QString("%1 位置:%2").arg(currentStatus).arg(position));
}

QByteArray ethercat::createDriveCommand(uint16_t index, uint8_t subIndex, const QByteArray &data, int slavePosition)
{
    QByteArray command;
    command.append(static_cast<char>(slavePosition));  // 從站地址
    command.append(static_cast<char>(0x23));          // 寫命令
    command.append(static_cast<char>(index >> 8));    // 索引高字節
    command.append(static_cast<char>(index & 0xFF));  // 索引低字節
    command.append(static_cast<char>(subIndex));      // 子索引
    command.append(data);                             // 數據負載
    return command;
}

/**
 * @brief 發送驅動器命令
 * @param command 要發送的命令數據
 * @param slavePosition 目標從站位置
 *
 * 通過EtherCAT發送命令到指定驅動器：
 * 1. 檢查通信狀態
 * 2. 發送命令
 * 3. 記錄發送日誌
 */
void ethercat::sendCommandToDrive(const QByteArray &command, int slavePosition)
{
    if (socket->state() == QAbstractSocket::ConnectedState) {
        socket->write(command);
        ui->textEdit_ethercat_back_m->append(QString("發送到驅動器 %1: 0x%2")
            .arg(slavePosition)
            .arg(QString(command.toHex())));
    }
}








//////////////////////////close/////////////////////////////
void ethercat::closeEvent(QCloseEvent *event) {
    if (isClosing) {
        event->accept();
        return;
    }
    isClosing = true;

    try {
        logCloseEvent("Start", "Beginning close process");

        // 斷開所有計時器
        if (motionTimer) {
            motionTimer->stop();
            motionTimer->disconnect();
            logCloseEvent("Timer", "Disconnected timer MotionTimer");
        }

        if (driveDetectionTimer) {
            driveDetectionTimer->stop();
            driveDetectionTimer->disconnect();
            logCloseEvent("Timer", "Disconnected timer DriveDetectionTimer");
        }

        if (cyclicTimer) {
            cyclicTimer->stop();
            cyclicTimer->disconnect();
            logCloseEvent("Timer", "Disconnected timer CyclicTimer");
        }

        // 檢查卡的狀態並記錄
        if (isCardInitialized) {
            logCloseEvent("CloseCard", "Cleaning up card resources");
            // 執行卡的清理工作
        } else {
            logCloseEvent("CloseCard", QString("Card status: %1").arg(lastKnownStatus));
        }

        logCloseEvent("Final", "Close process completed");

    } catch (const std::exception& e) {
        logCloseEvent("Error", QString("Exception during close: %1").arg(e.what()));
    }

    isClosing = false;
    event->accept();
}




void ethercat::closeCard() {
    if (!isCardInitialized) {
        logCloseEvent("CloseCard", "Card not initialized, skipping cleanup");
        return;
    }

    try {
        logCloseEvent("CloseCard", "Starting card cleanup");

        // 獲取實際連接的軸數量
        short slaveCount = 0;
        short ioCount = 0;
        short sRtn = GTN_GetEcatSlaves(cardCore, &slaveCount, &ioCount);

        if (sRtn != 0) {
            logCloseEvent("CloseCard", QString("Failed to get slave count: %1").arg(sRtn));
            return;
        }

        logCloseEvent("CloseCard", QString("Found %1 slaves").arg(slaveCount));

        // 依次處理每個軸
        for (short axis = 1; axis <= slaveCount; ++axis) {
            logCloseEvent("CloseCard", QString("Processing axis %1").arg(axis));

            // 先停止運動
            sRtn = GTN_Stop(cardCore, 1 << (axis - 1), 0);
            if (sRtn != 0) {
                logCloseEvent("CloseCard", QString("Failed to stop axis %1: %2").arg(axis).arg(sRtn));
                continue;
            }

            // 等待軸停止，但設置超時
            long status;
            int timeout = 50; // 5秒超時
            while (timeout > 0) {
                GTN_GetSts(cardCore, axis, &status);
                if (!(status & 0x400)) break;  // 如果軸已停止
                QThread::msleep(100);
                timeout--;
            }

            if (timeout <= 0) {
                logCloseEvent("CloseCard", QString("Timeout waiting for axis %1 to stop").arg(axis));
            }

            // 關閉軸
            sRtn = GTN_AxisOff(cardCore, axis);
            if (sRtn != 0) {
                logCloseEvent("CloseCard", QString("Failed to turn off axis %1: %2").arg(axis).arg(sRtn));
            }
        }

        logCloseEvent("CloseCard", "All axes processed, terminating EtherCAT communication");

        // 終止EtherCAT通訊
        sRtn = GTN_TerminateEcatComm(cardCore);
        if (sRtn != 0) {
            logCloseEvent("CloseCard", QString("Failed to terminate EtherCAT: %1").arg(sRtn));
        }

        // 關閉控制卡
        GTN_Close();
        isCardInitialized = false;

        logCloseEvent("CloseCard", "Card cleanup completed successfully");

    } catch (const std::exception& e) {
        logCloseEvent("CloseCard", QString("Exception during cleanup: %1").arg(e.what()));
        isCardInitialized = false;
    } catch (...) {
        logCloseEvent("CloseCard", "Unknown exception during cleanup");
        isCardInitialized = false;
    }
}

///////////////////////////////////////////////////////














void ethercat::executeMotionSegment(const MotionSegment &segment)
{
    if (!isCardInitialized) {
        ui->comboBox_movepic->addItem("控制卡未初始化");
        return;
    }

    short axis = ui->comboBox_move_drive->currentIndex() + 1;
    short sRtn;

    // 設置為梯形速度模式
    sRtn = GTN_PrfTrap(cardCore, axis);
    if (sRtn != 0) {
        ui->comboBox_movepic->addItem("設置運動模式失敗executeMotionSegment");
        return;
    }

    // 設置運動參數
    TTrapPrm trap;
    sRtn = GTN_GetTrapPrm(cardCore, axis, &trap);
    if (sRtn != 0) {
        ui->comboBox_movepic->addItem("獲取運動參數失敗");
        return;
    }

    trap.acc = fabs(segment.velocity) * 1.5;  // 降低加速度係數到1.5倍
    trap.dec = fabs(segment.velocity) * 1.5;  // 降低減速度係數到1.5倍
    trap.smoothTime = 50;                     // 增加平滑時間到50

    sRtn = GTN_SetTrapPrm(cardCore, axis, &trap);
    if (sRtn != 0) {
        ui->comboBox_movepic->addItem("設置運動參數失敗03");
        return;
    }

    // 設置速度
    sRtn = GTN_SetVel(cardCore, axis, segment.velocity * segment.direction);
    if (sRtn != 0) {
        ui->comboBox_movepic->addItem("設置速度失敗");
        return;
    }

    // 啟動運動
    sRtn = GTN_Update(cardCore, 1 << (axis - 1));
    if (sRtn != 0) {
        ui->comboBox_movepic->addItem("啟動運動失敗");
        return;
    }

    ui->comboBox_movepic->addItem(QString("軸 %1 開始運動，速度: %.2f，方向: %2")
        .arg(axis)
        .arg(segment.velocity)
        .arg(segment.direction > 0 ? "正向" : "反向"));
}

void ethercat::setupMotionProfile()
{
    motionSegments.clear();

    // 添加運動段：0-1秒，速度60rpm，正向
    MotionSegment segment;
    segment.startTime = 0.0;
    segment.endTime = 1.0;
    segment.velocity = 60.0;
    segment.direction = 1;
    motionSegments.append(segment);

    // 設置定時器
    motionTimer = new QTimer(this);
    connect(motionTimer, &QTimer::timeout, this, [this]() {
        static int currentSegment = 0;
        if (currentSegment < motionSegments.size()) {
            executeMotionSegment(motionSegments[currentSegment]);
            currentSegment++;
        } else {
            motionTimer->stop();
            // 停止運動
            short axis = ui->comboBox_move_drive->currentIndex() + 1;
            GTN_SetVel(cardCore, axis, 0);
            GTN_Update(cardCore, 1 << (axis - 1));
        }
    });

    // 啟動定時器，每100ms檢查一次
    motionTimer->start(100);
}

struct TimeControlledMotion {
    double startTime;    // 開始時間（秒）
    double endTime;      // 結束時間（秒）
    double velocity;     // 速度（pulse/s）
    int direction;       // 方向：1正向，-1反向
    bool continuePrevious; // 新增：是否繼承上一個動作的結束速度
};

void ethercat::executeTimeControlledMotion(const TimeControlledMotion& motion)
{
    int currentDrive = ui->comboBox_move_drive->currentIndex();
    if (currentDrive < 0) return;

    short axis = currentDrive + 1;
    short sRtn;

    // 獲取當前速度和狀態
    double currentVel;
    long status;
    sRtn = GTN_GetVel(cardCore, axis, &currentVel);
    sRtn = GTN_GetSts(cardCore, axis, &status);

    double initialPos;
    GTN_GetPrfPos(cardCore, axis, &initialPos);  // 獲取初始位置

    // 設置為 Jog 模式
    sRtn = GTN_PrfJog(cardCore, axis);
    if (sRtn != 0) {
        ui->comboBox_movepic->addItem("設置運動模式失敗executeTimeControlledMotion");
        return;
    }

    // 設置 Jog 運動參數
    TJogPrm jog;
    sRtn = GTN_GetJogPrm(cardCore, axis, &jog);
    if (sRtn != 0) {
        ui->comboBox_movepic->addItem("獲取運動參數失敗");
        return;
    }

    // 根據不同情況設置加速度和平滑係數
    if (motion.continuePrevious &&
        ((currentVel > 0 && motion.direction > 0) ||
         (currentVel < 0 && motion.direction < 0))) {
        // 同向運動且需要繼承速度
        jog.acc = fabs(motion.velocity - fabs(currentVel)) * 1.5;  // 降低加速度係數
        jog.dec = fabs(motion.velocity - fabs(currentVel)) * 1.5;  // 降低減速度係數
        jog.smooth = 0.8;  // 增加平滑係數到0.8
    } else {
        // 反向運動或不需要繼承速度
        jog.acc = fabs(motion.velocity) * 1.5;  // 降低加速度係數到1.5
        jog.dec = fabs(motion.velocity) * 1.5;  // 降低減速度係數到1.5
        jog.smooth = 0.5;  // 增加平滑係數到0.5
    }

    sRtn = GTN_SetJogPrm(cardCore, axis, &jog);
    if (sRtn != 0) {
        ui->comboBox_movepic->addItem("設置運動參數失敗04");
        return;
    }

    // 設置目標速度（考慮方向）
    double targetVel = motion.velocity * motion.direction;
    sRtn = GTN_SetVel(cardCore, axis, targetVel);
    if (sRtn != 0) {
        ui->comboBox_movepic->addItem("設置速度失敗");
        return;
    }

    sRtn = GTN_Update(cardCore, 1 << (axis - 1));
    if (sRtn != 0) {
        ui->comboBox_movepic->addItem("啟動運動失敗");
        return;
    }

    QString motionType;
    if (motion.continuePrevious &&
        ((currentVel > 0 && motion.direction > 0) ||
         (currentVel < 0 && motion.direction < 0))) {
        motionType = "繼承速度";
    } else {
        motionType = "標準運動";
    }

    ui->comboBox_movepic->addItem(QString("軸 %1 開始%4運動，速度: %2，方向: %3")
        .arg(axis)
        .arg(motion.velocity)
        .arg(motion.direction > 0 ? "正向" : "反向")
        .arg(motionType));
}
/////////////////////////////處理主窗口命令發射的問題///////////////////////////
QString ethercat::getName() const
{
    return ui->lineEdit_ethercat_name->text();
}





void ethercat::sendCommand(const QString& command) {
    if (!pendingPVTMotions.isEmpty()) {
        // 如果當前有運動在執行，將命令加入隊列
        commandQueue.enqueue(command);
        return;
    }

    qDebug() << "\n=== ethercat::sendCommand 開始 ===";
    qDebug() << "收到命令:" << command;
    qDebug() << "命令包含 :PA?" << command.contains(":PA");
    qDebug() << "命令包含 TA?" << command.contains("TA");
    qDebug() << "命令包含 SPA?" << command.contains("SPA");

    // 檢查是否為 PVT 命令
    if (command.contains(":PA") && command.contains("TA") && command.contains("SPA")) {
        qDebug() << "確認為PVT命令，轉交給parsePVTCommand處理";
        parsePVTCommand(command);
        if (!pendingPVTMotions.isEmpty()) {
            qDebug() << "PVT命令解析成功，準備執行";
            executePVTMotions();
        } else {
            qDebug() << "警告: PVT命令解析後沒有待執行的動作";
        }
        qDebug() << "=== sendCommand 結束 ===\n";
        return;
    }

    qDebug() << "不是PVT命令，檢查其他命令類型";
    // 檢查是否為點到點運動命令
    QRegularExpression pRegex("P(-?\\d+)-(-?\\d+)\\s+SP(\\d+)");
    QRegularExpressionMatch match = pRegex.match(command);
    if (match.hasMatch()) {
        executePointToPointMotion(command);
        return;
    }

    // 檢查是否為時間控制運動命令
    QRegularExpression tRegex("T\\d+(\\.\\d+)?-\\d+(\\.\\d+)?\\s+V\\d+(\\.\\d{1,2})?\\s+D[+-]?\\d+");
    if (command.contains(tRegex)) {
        parseMotionCommand(command);
        return;
    }

    // 檢查是否為十六進制數據命令
    if (command.startsWith("0x") || command.startsWith("0X")) {
        QString hexCommand = command.mid(2);  // 移除 "0x" 前綴
        QByteArray data = QByteArray::fromHex(hexCommand.toUtf8());

        if (data.size() >= 3) {
            uint16_t index = (data[0] << 8) | data[1];
            uint8_t subIndex = data[2];
            QByteArray payload = data.mid(3);

            // 將 payload 轉換為 int32_t
            int32_t value = 0;
            if (payload.size() >= 4) {
                value = (static_cast<int32_t>(static_cast<unsigned char>(payload[0])) << 24) |
                       (static_cast<int32_t>(static_cast<unsigned char>(payload[1])) << 16) |
                       (static_cast<int32_t>(static_cast<unsigned char>(payload[2])) << 8) |
                       static_cast<int32_t>(static_cast<unsigned char>(payload[3]));
            }

            // 獲取當前選擇的驅動器
            int currentDrive = ui->comboBox_move_drive->currentIndex();
            if (currentDrive >= 0) {
                // 通過 EtherCAT 主站發送命令
                if (ecMaster.writeSDO(currentDrive + 1, index, subIndex, value)) {
                    qDebug() << "Command sent to drive" << (currentDrive + 1)
                             << "Index:" << QString::number(index, 16)
                             << "SubIndex:" << QString::number(subIndex, 16)
                             << "Value:" << value;
                } else {
                    ui->comboBox_movepic->addItem("發送SDO命令失敗");
                }
            } else {
                ui->comboBox_movepic->addItem("未選擇驅動器");
            }
        } else {
            ui->comboBox_movepic->addItem("無效的十六進制命令格式");
        }
        return;
    }

    // 如果命令格式都不匹配，添加錯誤提示
    ui->comboBox_movepic->addItem("未識別的命令格式: " + command);
}

////////////////////自定義運動命令//////////////////////////////////
void ethercat::parseMotionCommand(const QString& command)
{

    // 清理舊的定時器
    if (motionTimer) {
        motionTimer->stop();
        delete motionTimer;
        motionTimer = nullptr;
    }
    // 解析命令格式：T0-1 V60 D1
    // T: 時間範圍，V: 速度，D: 方向
    motionQueue.clear();
    currentMotionIndex = 0;

    QStringList parts = command.split(" ", Qt::SkipEmptyParts);
    TimeControlledMotion motion;
    motion.continuePrevious = false;  // 默認不繼承速度

    foreach(const QString& part, parts) {
        if (part.startsWith("T")) {
            QStringList times = part.mid(1).split("-");
            if (times.size() == 2) {
                motion.startTime = times[0].toDouble();
                motion.endTime = times[1].toDouble();
            }
        }
        else if (part.startsWith("V")) {
            motion.velocity = part.mid(1).toDouble();
        }
        else if (part.startsWith("D")) {
            QString dirStr = part.mid(1);
            // 檢查是否包含 'C' 標記
            if (dirStr.contains('C')) {
                motion.continuePrevious = true;
                dirStr.remove('C');
            }
            motion.direction = dirStr.toInt();
        }
    }

    motionQueue.append(motion);

    // 開始執行運動
    elapsedTimer.start();
    motionTimer = new QTimer(this);
    connect(motionTimer, &QTimer::timeout, this, &ethercat::processNextMotion);
    motionTimer->start(10); // 每10ms檢查一次
}

void ethercat::processNextMotion()
{
    short axis = ui->comboBox_move_drive->currentIndex() + 1;
    static double initialPos = 0;
    static bool isFirstMotion = true;

    if (isFirstMotion) {
        GTN_GetPrfPos(cardCore, axis, &initialPos);
        qDebug() << QString("Initial position at motion start: %1").arg(initialPos);
        isFirstMotion = false;

        // 重要：在運動序列完成時重置標誌
        connect(this, &ethercat::motionCompleted, [this]() {
            isFirstMotion = true;
        });
    }




    if (currentMotionIndex >= motionQueue.size()) {
        short axis = ui->comboBox_move_drive->currentIndex() + 1;
        GTN_SetVel(cardCore, axis, 0);
        GTN_Update(cardCore, 1 << (axis - 1));

        // 等待運動完全停止
        long status;
        do {
            GTN_GetSts(cardCore, axis, &status);
            if (!(status & 0x004) && !(status & 0x008)) {
                break;
            }
            QThread::msleep(10);
        } while (true);

        if (motionTimer) {
            motionTimer->stop();
            delete motionTimer;
            motionTimer = nullptr;
        }

        qDebug() << "Motion sequence completed";
        emit motionCompleted();
        return;
    }

    double currentTime = elapsedTimer.elapsed() / 1000.0;
    const TimeControlledMotion& motion = motionQueue[currentMotionIndex];

    if (currentTime >= motion.startTime && currentTime <= motion.endTime) {
        // 執行運動前先清除狀態
        short axis = ui->comboBox_move_drive->currentIndex() + 1;
        GTN_ClrSts(cardCore, axis);

        executeTimeControlledMotion(motion);

        // 等待達到目標速度
        double targetVel = motion.velocity * motion.direction;
        int timeout = 100; // 1秒超時
        do {
            double actualVel;
            GTN_GetVel(cardCore, axis, &actualVel);
            if (fabs(actualVel - targetVel) < 1) { // 速度誤差在0.1以內
                break;
            }
            QThread::msleep(10);
            timeout--;
        } while (timeout > 0);

        if (timeout <= 0) {
            qDebug() << "Warning: Timeout waiting for target velocity";
        }
    }
    else if (currentTime > motion.endTime) {
        short axis = ui->comboBox_move_drive->currentIndex() + 1;

        // 先減速到0
        GTN_SetVel(cardCore, axis, 0);
        GTN_Update(cardCore, 1 << (axis - 1));

        // 等待完全停止並監控補償
        long status;
        double actualVel;
        do {
            GTN_GetSts(cardCore, axis, &status);
            GTN_GetVel(cardCore, axis, &actualVel);

            // 獲取編碼器位置和規劃位置用於計算補償量
            double encPos, prfPos;
            GTN_GetEncPos(cardCore, axis, &encPos);
            GTN_GetPrfPos(cardCore, axis, &prfPos);

            // 獲取實際補償值
            double pitchError, crossError, backlashError, encPos_comp, prfPos_comp;
            short sRtn = GTN_GetCompensate(cardCore, axis, &pitchError, &crossError,
                                         &backlashError, &encPos_comp, &prfPos_comp);

            // 計算位置差異並輸出到調試窗口
            double compensationValue = encPos - prfPos;
            qDebug() << QString("Axis %1 監控:").arg(axis);
            qDebug() << QString("編碼器位置: %1").arg(encPos, 0, 'f', 3);
            qDebug() << QString("規劃位置: %1").arg(prfPos, 0, 'f', 3);
            qDebug() << QString("位置差異: %1").arg(compensationValue, 0, 'f', 3);
            qDebug() << "實際補償值:";
            qDebug() << QString("- 螺距補償: %1").arg(pitchError, 0, 'f', 3);
            qDebug() << QString("- 交叉補償: %1").arg(crossError, 0, 'f', 3);
            qDebug() << QString("- 反向間隙: %1").arg(backlashError, 0, 'f', 3);
            qDebug() << QString("- 總補償量: %1").arg(pitchError + crossError + backlashError, 0, 'f', 3);

            if (sRtn == 0) {
                QString compensationInfo = QString(
                    "實際補償值:\n"
                    "- 螺距補償: %1\n"
                    "- 交叉補償: %2\n"
                    "- 反向間隙: %3\n"
                    "- 總補償量: %4"
                ).arg(pitchError, 0, 'f', 3)
                .arg(crossError, 0, 'f', 3)
                .arg(backlashError, 0, 'f', 3)
                .arg(pitchError + crossError + backlashError, 0, 'f', 3);

                qDebug() << compensationInfo;
            }

            if (!(status & 0x004) && !(status & 0x008) && fabs(actualVel) < 0.1) {
                break;
            }
            QThread::msleep(10);
        } while (true);

        // 計算總移動脈衝數
        double finalPos;
        GTN_GetPrfPos(cardCore, axis, &finalPos);  // 獲取最終位置
        double pulseDiff = fabs(finalPos - initialPos);  // 計算脈衝差值
        // 檢查是否需要位置補償
        //if (compensatePosition(axis, targetPosition, finalPos)) {
           // qDebug() << "Position compensation performed";
        //}
        qDebug() << QString("Motion completed - Total pulses moved: %1").arg(pulseDiff);

        currentMotionIndex++;
        if (currentMotionIndex >= motionQueue.size()) {
            if (motionTimer) {
                motionTimer->stop();
                delete motionTimer;
                motionTimer = nullptr;
            }
            qDebug() << "Motion sequence completed";
            emit motionCompleted();
        }
    }
}

QString ethercat::getAxisStatusText(long status) {
    QStringList statusFlags;

    if (status & 0x400) statusFlags << "已使能";
    if (status & 0x200) statusFlags << "準備好";
    if (status & 0x100) statusFlags << "到位";
    if (status & 0x004) statusFlags << "正向運動";
    if (status & 0x008) statusFlags << "反向運動";

    return statusFlags.isEmpty() ? "未使能" : statusFlags.join(", ");
}

void ethercat::updateStatusDisplay(short axis, long status, const QString& statusText, const QString& positionInfo) {
    QString displayText = QString("軸 %1: %2 | %3").arg(axis).arg(statusText).arg(positionInfo);
    ui->lineEdit_drive_sis->setText(displayText);
    updateStatusColor(status);
}

void ethercat::updateStatusColor(long status) {
    QString styleSheet;
    if (status & 0x200) {  // 報警狀態
        styleSheet = "QLineEdit { background-color: #ffcccc; color: #cc0000; }";
    } else if (status & 0x400) {  // 已使能
        styleSheet = "QLineEdit { background-color: #ccffcc; color: #006600; }";
    } else {  // 正常未使能狀態
        styleSheet = "QLineEdit { background-color: #ffffff; color: #000000; }";
    }
    ui->lineEdit_drive_sis->setStyleSheet(styleSheet);
}

QString ethercat::getErrorMessage(short errorCode) {
    switch (errorCode) {
        case 1: return "參數錯誤";
        case 2: return "軸號超出範圍";
        case 3: return "運動中斷";
        case 4: return "硬件未就緒";
        case 7: return "指令執行錯誤";
        case -1: return "通信錯誤";
        default: return QString("未知錯誤 (代碼: %1)").arg(errorCode);
    }
}


//////////////////////////////界面中輸入框輸入運動命令功能///////////////////

void ethercat::on_pushButton_ethercat_send_m_clicked()
{
    int currentDrive = ui->comboBox_move_drive->currentIndex();
    if (currentDrive < 0) {
        QMessageBox::warning(this, "错误", "请先选择驱动器。");
        return;
    }

    QString message = ui->textEdit_ethercat_send_massage->toPlainText();

    // 檢查是否為運動控制命令格式 (T0-1 V60 D1)
    QRegularExpression regex("T\\d+-\\d+\\s+V\\d+\\s+D[+-]?\\d+");
    if (message.contains(regex)) {
        parseMotionCommand(message);
        return;
    }

    // 检查是否为十六进制数据
    if (message.startsWith("0x") || message.startsWith("0X")) {
        message = message.mid(2);  // 移除 "0x" 前缀
        QByteArray data = QByteArray::fromHex(message.toUtf8());

        if (data.size() >= 3) {
            uint16_t index = (data[0] << 8) | data[1];
            uint8_t subIndex = data[2];
            QByteArray payload = data.mid(3);

            // 將 payload 轉換為 int32_t
            int32_t value = 0;
            if (payload.size() >= 4) {
                value = (static_cast<int32_t>(static_cast<unsigned char>(payload[0])) << 24) |
                       (static_cast<int32_t>(static_cast<unsigned char>(payload[1])) << 16) |
                       (static_cast<int32_t>(static_cast<unsigned char>(payload[2])) << 8) |
                       static_cast<int32_t>(static_cast<unsigned char>(payload[3]));
            }

            // 通過 EtherCAT 主站發送命令
            if (ecMaster.writeSDO(currentDrive, index, subIndex, value)) {
                qDebug() << "Command sent to drive" << currentDrive
                         << "Index:" << QString::number(index, 16)
                         << "SubIndex:" << QString::number(subIndex, 16)
                         << "Value:" << value;
            }
        }
    }
}

void ethercat::onReadyRead()
{
    QByteArray data = socket->readAll();
    handleDriveResponse(data);
}
///////////////////////////////////程序崩潰日志///////////////////////////////

void ethercat::logCloseEvent(const QString& stage, const QString& message) {
    QString logMessage = QString("[%1] Close Event - %2: %3")
        .arg(QDateTime::currentDateTime().toLocalTime().toString("yyyy-MM-dd HH:mm:ss.zzz"))
        .arg(stage)
        .arg(message);

    // 獲取應用程序路徑
    QString appPath = QCoreApplication::applicationDirPath();
    QString logFilePath = appPath + "/logs";

    // 確保日誌目錄存在
    QDir dir(logFilePath);
    if (!dir.exists()) {
        if (!dir.mkpath(".")) {
            logFilePath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/logs";
            dir.setPath(logFilePath);
            if (!dir.mkpath(".")) {
                qDebug() << "無法創建日誌目錄：" << logFilePath;
                return;
            }
        }
    }

    // 使用當前日期作為文件名
    QString currentDate = QDate::currentDate().toString("yyyy-MM-dd");
    QString fullPath = QString("%1/ethercat_close_%2.log").arg(logFilePath).arg(currentDate);

    // 打開文件進行寫入
    QFile logFile(fullPath);
    if (!logFile.open(QIODevice::WriteOnly | QIODevice::Append | QIODevice::Text)) {
        qDebug() << "無法打開日誌文件：" << fullPath << "錯誤：" << logFile.errorString();
        return;
    }

    QTextStream stream(&logFile);
#ifdef QT_VERSION
  #if QT_VERSION < 0x060000
    stream.setCodec("UTF-8");
  #else
    stream.setEncoding(QStringConverter::Utf8);
  #endif
#else
    stream.setEncoding(QStringConverter::Utf8);
#endif

    // 寫入系統信息（僅在文件為空時）
    if (logFile.size() == 0) {
        stream << "=== System Information ===\n"
              << QString("Build Type: %1\n").arg(
                #ifdef NDEBUG
                    "Release"
                #else
                    "Debug"
                #endif
                )
              << QString("Qt Version: %1\n").arg(QT_VERSION_STR)
              << QString("OS: %1\n").arg(QSysInfo::prettyProductName())
              << QString("CPU Arch: %1\n").arg(QSysInfo::currentCpuArchitecture())
              << "=========================\n\n";
    }

    // 寫入日誌消息
    stream << logMessage << "\n";
    stream.flush();  // 確保數據被寫入磁盤
    logFile.close();

    // 輸出調試信息
    qDebug() << "日誌已寫入到：" << fullPath;
    qDebug() << "寫入內容：" << logMessage;

    // 清理舊日誌
    cleanupOldLogs(logFilePath, 30);
}

// 獲取活動連接信息
QString ethercat::getActiveConnections() {
    QString connections;
    if (socket) {
        connections += QString("Local Address: %1:%2\n")
            .arg(socket->localAddress().toString())
            .arg(socket->localPort());
        connections += QString("Peer Address: %1:%2\n")
            .arg(socket->peerAddress().toString())
            .arg(socket->peerPort());
    }
    return connections;
}

QString ethercat::getCurrentStatus() {
    QString status;
    status += QString("Card Initialized: %1\n").arg(isCardInitialized ? "Yes" : "No");
    status += QString("Last Known Status: %1\n").arg(lastKnownStatus);
    if (socket) {
        status += QString("Socket State: %1\n").arg(socket->state());
    }
    return status;
}
void ethercat::setupLogging() {
    // 獲取應用程序路徑
    QString appPath = QCoreApplication::applicationDirPath();
    QString logFilePath = appPath + "/logs";

    // 嘗試創建日誌目錄
    QDir dir(logFilePath);
    if (!dir.exists()) {
        if (!dir.mkpath(".")) {
            // 如果無法在程序目錄創建，則使用用戶目錄
            logFilePath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/logs";
            dir.setPath(logFilePath);
            if (!dir.mkpath(".")) {
                qDebug() << "警告：無法創建日誌目錄：" << logFilePath;
                return;
            }
        }
    }

    // 測試是否有寫入權限
    QString testFile = logFilePath + "/test.txt";
    QFile file(testFile);
    if (file.open(QIODevice::WriteOnly)) {
        file.write("Test");
        file.close();
        file.remove();  // 刪除測試文件
        qDebug() << "日誌系統初始化成功，路徑：" << logFilePath;
    } else {
        qDebug() << "警告：無法寫入日誌目錄：" << logFilePath;
    }
}

// 清理舊日誌文件
void ethercat::cleanupOldLogs(const QString& logPath, int daysToKeep) {
    QDir dir(logPath);
    QFileInfoList files = dir.entryInfoList(QStringList() << "ethercat_close_*.log",
                                          QDir::Files | QDir::NoDotAndDotDot);

    QDate cutoffDate = QDate::currentDate().addDays(-daysToKeep);

    for (const QFileInfo& file : files) {
        // 從文件名中提取日期
        QString dateStr = file.baseName().mid(15, 10); // "ethercat_close_" 長度為15
        QDate fileDate = QDate::fromString(dateStr, "yyyy-MM-dd");

        if (fileDate.isValid() && fileDate < cutoffDate) {
            QFile::remove(file.absoluteFilePath());
        }
    }
}

////////////////更改窗口名稱///////////////////////////////////////

void ethercat::onNameChanged(const QString& name)
{
    if (!name.isEmpty()) {
        setWindowTitle(name);
    } else {
        setWindowTitle("EtherCAT Connection");  // 默認標題
    }
}

void ethercat::setUniqueName()
{
    instanceCount++;
    QString uniqueName = QString("EtherCAT_%1").arg(instanceCount);
    ui->lineEdit_ethercat_name->setText(uniqueName);
    // setText 會觸發 textChanged 信號，自動更新窗口標題
}
////////////////////////////////////////////////////////////////////

//////////界面頂端輸入測試軸運動功能////////////////////////////////

void ethercat::on_pushButton_move_clicked()
{
    int currentDrive = ui->comboBox_move_drive->currentIndex();
    if (currentDrive < 0) {
        QMessageBox::warning(this, "警告", "請先選擇要控制的軸");
        return;
    }

    short axis = currentDrive + 1;

    // 檢查軸狀態
    long status;
    double actualPos;
    short sRtn = GTN_GetSts(cardCore, axis, &status);
    if (sRtn != 0) {
        logError("獲取軸狀態失敗", sRtn, axis);
        return;
    }

    // 嚴格檢查使能狀態，必須有 0x400 位
    if (!(status & 0x400)) {
        if (!enableAxis(axis)) {
            QMessageBox::warning(this, "警告", "軸未完全使能，請檢查伺服狀態");
            return;
        }
    }

    // 獲取目標位置和速度
    double targetPosition = ui->spinBox_position->value();
    double targetVelocity = ui->spinBox_velocity->value();

    // 設置點位運動模式
    sRtn = GTN_PrfTrap(cardCore, axis);
    if (sRtn != 0) {
        logError("設置運動模式失敗", sRtn, axis);
        return;
    }

    // 設置運動參數
    TTrapPrm trap;
    trap.acc = targetVelocity * 10;
    trap.dec = targetVelocity * 10;
    trap.smoothTime = 25;

    sRtn = GTN_SetTrapPrm(cardCore, axis, &trap);
    if (sRtn != 0) {
        logError("設置運動參數失敗05", sRtn, axis);
        return;
    }

    // 設置目標位置和速度
    sRtn = GTN_SetPos(cardCore, axis, targetPosition);
    if (sRtn != 0) {
        logError("設置位置失敗", sRtn, axis);
        return;
    }

    sRtn = GTN_SetVel(cardCore, axis, targetVelocity);
    if (sRtn != 0) {
        GTN_GetSts(cardCore, axis, &status);
        qDebug() << "錯誤: 設置速度失敗";
        qDebug() << "當前軸狀態:" << QString::number(status, 16);
        QMessageBox::warning(this, "警告", "設置速度失敗");
        return;
    }

    // 啟動運動
    sRtn = GTN_Update(cardCore, 1 << (axis - 1));
    if (sRtn != 0) {
        logError("啟動運動失敗", sRtn, axis);
        return;
    }

    ui->comboBox_movepic->addItem(
        QString("軸 %1 開始運動 - 目標位置: %2, 速度: %3")
        .arg(axis)
        .arg(targetPosition)
        .arg(targetVelocity)
    );

    // 在運動啟動後，等待運動完成
    do {
        GTN_GetSts(cardCore, axis, &status);
        GTN_GetPrfPos(cardCore, axis, &actualPos);
        qDebug() << QString("當前位置: %1").arg(actualPos);

        if (!(status & 0x400)) {
            QThread::msleep(50);
            GTN_GetSts(cardCore, axis, &status);
            if (!(status & 0x400)) {
                break;
            }
        }
        QThread::msleep(10);
    } while (true);
}

//////////////////////點位運動模式/////////////////////////////////

void ethercat::parsePointToPointCommand(const QString& command, PointToPointMotion& motion)
{
    qDebug() << "開始解析命令:" << command;  // 新增：輸出原始命令

    // 修改正則表達式以更好地處理負號
    QRegularExpression regex("P(-?\\d+)-(-?\\d+)\\s+SP(\\d+)");
    QRegularExpressionMatch match = regex.match(command);

    if (match.hasMatch()) {
        bool ok1, ok2, ok3;
        motion.startPosition = match.captured(1).toInt(&ok1);
        motion.endPosition = match.captured(2).toInt(&ok2);
        motion.speed = match.captured(3).toInt(&ok3);

        qDebug() << "命令解析結果:";
        qDebug() << "  原始命令:" << command;
        qDebug() << "  解析成功:" << (ok1 && ok2 && ok3);
        qDebug() << "  起始位置:" << motion.startPosition;
        qDebug() << "  終點位置:" << motion.endPosition;
        qDebug() << "  速度:" << motion.speed;
    } else {
        qDebug() << "命令格式不匹配，詳細信息:";
        qDebug() << "  原始命令:" << command;
        qDebug() << "  正則表達式:" << regex.pattern();
    }
}

void ethercat::executePointToPointMotion(const QString& command)
{
    qDebug() << "開始執行點位運動命令:" << command;
    PointToPointMotion motion;
    parsePointToPointCommand(command, motion);

    // 驗證解析結果
    if (motion.speed <= 0) {
        qDebug() << "命令解析失敗或速度無效";
        return;
    }

    int currentDrive = ui->comboBox_move_drive->currentIndex();
    if (currentDrive < 0) {
        QMessageBox::warning(this, "警告", "請先選擇要控制的軸");
        return;
    }

    short axis = currentDrive + 1;

    // 檢查軸狀態
    long status;
    short sRtn = GTN_GetSts(cardCore, axis, &status);
    if (sRtn != 0) {
        logError("獲取軸狀態失敗", sRtn, axis);
        return;
    }

    // 檢查使能狀態
    if (!(status & 0x400)) {
        if (!enableAxis(axis)) {
            QMessageBox::warning(this, "警告", "軸未完全使能，請檢查伺服狀態");
            return;
        }
    }

    // 在設置速度之前添加
    TTrapPrm trap;
    trap.acc = motion.speed * 2;    // 可以根據實際需求調整係數
    trap.dec = motion.speed * 2;    // 可以根據實際需求調整係數
    trap.smoothTime = 25;           // 平滑時間

    sRtn = GTN_SetTrapPrm(cardCore, axis, &trap);
    if (sRtn != 0) {
        logError("設置運動參數失敗", sRtn, axis);
        return;
    }

    // 直接設置目標位置和速度
    sRtn = GTN_SetPos(cardCore, axis, motion.endPosition);
    if (sRtn != 0) {
        logError("設置位置失敗", sRtn, axis);
        return;
    }

    sRtn = GTN_SetVel(cardCore, axis, motion.speed);
    if (sRtn != 0) {
        logError("設置速度失敗", sRtn, axis);
        return;
    }

    // 啟動運動
    sRtn = GTN_Update(cardCore, 1 << (axis - 1));
    if (sRtn != 0) {
        logError("啟動運動失敗", sRtn, axis);
        return;
    }

    ui->comboBox_movepic->addItem(
        QString("軸 %1 開始點位運動 - 起始位置: %2, 終點位置: %3, 速度: %4")
        .arg(axis)
        .arg(motion.startPosition)
        .arg(motion.endPosition)
        .arg(motion.speed)
    );

    // 等待運動完成
    double actualPosition;
    int compensationMessageCounter = 0;
    const int COMPENSATION_MESSAGE_INTERVAL = 5;  // 每5次循環輸出一次

    do {
        GTN_GetSts(cardCore, axis, &status);
        GTN_GetPrfPos(cardCore, axis, &actualPosition);

        // 獲取編碼器位置用於計算補償量
        double encPosition;
        GTN_GetEncPos(cardCore, axis, &encPosition);

        // 計算實際補償量
        double compensationValue = encPosition - actualPosition;

        // 使用計數器控制輸出頻率
        if (++compensationMessageCounter >= COMPENSATION_MESSAGE_INTERVAL) {
            compensationMessageCounter = 0;  // 重置計數器

            // 輸出補償監控信息
            qDebug() << QString("軸 %1 補償監控 - 規劃位置: %2, 編碼器位置: %3, 補償量: %4")
                        .arg(axis)
                        .arg(actualPosition, 0, 'f', 3)
                        .arg(encPosition, 0, 'f', 3)
                        .arg(compensationValue, 0, 'f', 3);
        }

        // 檢查是否到達目標位置
        if (fabs(actualPosition - motion.endPosition) < 1.0) {  // 允許1個單位的誤差
            if (!(status & 0x004) && !(status & 0x008)) {  // 同時確保停止運動
                QThread::msleep(50);  // 增加延時確保完全停止

                GTN_GetSts(cardCore, axis, &status);
                if (!(status & 0x004) && !(status & 0x008)) {
                    qDebug() << "Motion truly completed at position:" << actualPosition;
                    emit motionCompleted();
                    QThread::msleep(100);  // 發送信號後再等待
                    break;
                }
            }
        }
        QThread::msleep(50);  // 主循環延時
    } while (true);

    // 在執行下一個命令之前額外等待
    QThread::msleep(100);
}

//////////////////多軸運動//////////////////



bool ethercat::enableMultiAxis(const QList<short>& axes)
{
    for (short axis : axes) {
        long status;
        GTN_GetSts(globalCardCore, axis, &status);
        if (!(status & (0x200 | 0x400))) {  // 修改為檢查準備好或已使能
            // 使用單獨的靜態函數處理使能
            if (!enableAxisStatic(axis)) {
                return false;
            }
        }
    }
    return true;
}

// 添加靜態使能函數
bool ethercat::enableAxisStatic(short axis)
{
    short sRtn = GTN_AxisOn(globalCardCore, axis);
    if (sRtn != 0) {
        return false;
    }
    return true;
}

void ethercat::executeMultiAxisMotion(const QList<MultiAxisMotion>& motions)
{


    // 設置所有軸的運動參數
    int axisMask = 0;
    for (const auto& motion : motions) {
        TTrapPrm trap;
        trap.acc = motion.speed * 2;
        trap.dec = motion.speed * 2;
        trap.smoothTime = 25;

        short sRtn = GTN_SetTrapPrm(cardCore, motion.axis, &trap);
        if (sRtn != 0) {
            qDebug() << "設置運動參數失敗，軸:" << motion.axis;
            return;
        }

        sRtn = GTN_SetPos(cardCore, motion.axis, motion.endPosition);
        if (sRtn != 0) {
            qDebug() << "設置位置失敗，軸:" << motion.axis;
            return;
        }

        sRtn = GTN_SetVel(cardCore, motion.axis, motion.speed);
        if (sRtn != 0) {
            qDebug() << "設置速度失敗，軸:" << motion.axis;
            return;
        }

        axisMask |= (1 << (motion.axis - 1));
    }

    // 同步啟動所有軸
    GTN_Update(cardCore, axisMask);

    // 監控運動完成情況
    int compensationMessageCounter = 0;
    const int COMPENSATION_MESSAGE_INTERVAL = 5;

    do {
        bool allComplete = true;
        for (const auto& motion : motions) {
            long status;
            double actualPosition, encPosition;

            GTN_GetSts(cardCore, motion.axis, &status);
            GTN_GetPrfPos(cardCore, motion.axis, &actualPosition);
            GTN_GetEncPos(cardCore, motion.axis, &encPosition);

            // 計算補償量並輸出監控信息
            double compensationValue = encPosition - actualPosition;
            if (++compensationMessageCounter >= COMPENSATION_MESSAGE_INTERVAL) {
                compensationMessageCounter = 0;
                qDebug() << QString("軸 %1 監控 - 目標: %2, 當前: %3, 編碼器: %4, 補償: %5")
                            .arg(motion.axis)
                            .arg(motion.endPosition)
                            .arg(actualPosition, 0, 'f', 3)
                            .arg(encPosition, 0, 'f', 3)
                            .arg(compensationValue, 0, 'f', 3);
            }

            // 檢查是否到達目標位置
            if (fabs(actualPosition - motion.endPosition) >= 1.0 ||
                (status & 0x004) || (status & 0x008)) {
                allComplete = false;
                break;
            }
        }

        if (allComplete) {
            qDebug() << "所有軸運動完成";

            // 更新所有參與運動的軸的位置
            for (const auto& motion : motions) {
                // 更新絕對位置為目標位置
                absolutePositions[motion.axis] = motion.endPosition;

                // 獲取並保存最後實際位置
                double actualPos;
                if (GTN_GetPrfPos(cardCore, motion.axis, &actualPos) == 0) {
                    qDebug() << QString("軸 %1 最終位置: %2")
                                .arg(motion.axis)
                                .arg(actualPos, 0, 'f', 3);
                }
            }

            // 保存所有軸的位置到文件
            savePositionsToFile();

            // 額外等待確保完全停止
            QThread::msleep(100);
            break;
        }

        QThread::msleep(50);
    } while (true);

    // 運動完成後的額外等待
    QThread::msleep(100);
}

short ethercat::getCurrentAxis() const
{
    return ui->comboBox_move_drive->currentIndex() + 1;
}


/// @brief /////////////////////初始化時設置各軸的參考位置///////////////
/// @param axis
/// @return
bool ethercat::initializeAxisReference(short axis)
{
    AxisReferencePosition ref;
    ref.axis = axis;
    ref.homePosition = 0.0;  // 初始化為0

    // 獲取當前位置
    double currentPos;
    if (GTN_GetPrfPos(globalCardCore, axis, &currentPos) == 0) {  // 使用 globalCardCore
        ref.currentPosition = currentPos;
    } else {
        qDebug() << QString("獲取軸 %1 當前位置失敗").arg(axis);
        ref.currentPosition = 0.0;
    }

    // 從配置文件或其他來源讀取軟限位
    // 這裡暫時設置一個較大的範圍
    ref.softLimitPos = 1000000.0;
    ref.softLimitNeg = -1000000.0;
    ref.isReferenced = false;

    // 存儲參考位置信息
    axisReferences[axis] = ref;

    qDebug() << QString("軸 %1 參考位置初始化:").arg(axis);
    qDebug() << QString("  當前位置: %1").arg(ref.currentPosition);
    qDebug() << QString("  原點位置: %1").arg(ref.homePosition);

    return true;
}

bool ethercat::updateAxisReference(short axis)
{
    if (!axisReferences.contains(axis)) {
        qDebug() << QString("軸 %1 未初始化參考位置").arg(axis);
        return false;
    }

    double currentPos;
    if (GTN_GetPrfPos(globalCardCore, axis, &currentPos) == 0) {
        axisReferences[axis].currentPosition = currentPos;
        qDebug() << QString("軸 %1 當前位置更新為: %2")
                    .arg(axis)
                    .arg(currentPos);
        return true;
    }

    qDebug() << QString("更新軸 %1 位置失敗").arg(axis);
    return false;
}

////////////////絕對位置

// 文件操作相關函數
QString ethercat::getPositionsFilePath() {
    return QCoreApplication::applicationDirPath() + "/config/axis_positions.json";
}

void ethercat::savePositionsToFile()
{
    QString filePath = getPositionsFilePath();
    QDir().mkpath(QFileInfo(filePath).path());

    QJsonObject positionsObj;
    QFile readFile(filePath);
    if (readFile.open(QIODevice::ReadOnly)) {
        QJsonDocument loadDoc = QJsonDocument::fromJson(readFile.readAll());
        positionsObj = loadDoc.object();
        readFile.close();
    }

    // 保存所有軸的位置
    for (auto it = absolutePositions.constBegin(); it != absolutePositions.constEnd(); ++it) {
        int axis = it.key();
        QJsonObject axisInfo;

        // 如果已存在該軸的信息，保留原有的 absolutePosition
        if (positionsObj.contains(QString::number(axis))) {
            QJsonObject existingInfo = positionsObj[QString::number(axis)].toObject();
            axisInfo["absolutePosition"] = existingInfo["absolutePosition"];
        } else {
            // 新軸位置，使用當前值
            axisInfo["absolutePosition"] = QString::number(it.value());
        }

        // 只更新最後實際位置
        double lastPos;
        if (GTN_GetPrfPos(cardCore, axis, &lastPos) == 0) {
            axisInfo["lastPosition"] = QString::number(static_cast<long>(lastPos));
        }

        positionsObj[QString::number(axis)] = axisInfo;
    }

    QFile writeFile(filePath);
    if (writeFile.open(QIODevice::WriteOnly)) {
        QJsonDocument doc(positionsObj);
        writeFile.write(doc.toJson());
    }
}

void ethercat::loadPositionsFromFile() {
    QFile file(getPositionsFilePath());
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "無法打開位置配置文件進行讀取";
        return;
    }

    QJsonDocument doc = QJsonDocument::fromJson(file.readAll());
    QJsonObject positionsObj = doc.object();

    absolutePositions.clear();
    for (auto it = positionsObj.constBegin(); it != positionsObj.constEnd(); ++it) {
        int axis = it.key().toInt();
        QJsonObject axisInfo = it.value().toObject();
        absolutePositions[axis] = axisInfo["absolutePosition"].toString().toLong();
    }
}

// 設置絕對位置的槽函數
void ethercat::on_pushButton_pp_Position_clicked()
{
    int currentDrive = ui->comboBox_move_drive->currentIndex();
    if (currentDrive < 0) {
        QMessageBox::warning(this, "警告", "請先選擇要控制的軸");
        return;
    }

    short axis = currentDrive + 1;
    bool ok;
    long position = ui->lineEdit_PP_Position->text().toLong(&ok);

    if (!ok) {
        QMessageBox::warning(this, "警告", "請輸入有效的位置值");
        return;
    }

    // 檢查軸狀態並使能
    long status;
    GTN_GetSts(cardCore, axis, &status);
    qDebug() << "運動前軸狀態:" << QString::number(status, 16);

    if (!(status & 0x400)) {
        if (!enableAxis(axis)) {
            QMessageBox::warning(this, "警告", "軸使能失敗");
            return;
        }
        GTN_GetSts(cardCore, axis, &status);
        qDebug() << "使能後軸狀態:" << QString::number(status, 16);
    }

    // 更新配置文件中的位置信息
    QString filePath = getPositionsFilePath();
    QDir().mkpath(QFileInfo(filePath).path());  // 確保目錄存在

    QJsonObject positionsObj;
    QFile file(filePath);

    // 讀取現有文件內容
    if (file.open(QIODevice::ReadOnly)) {
        QJsonDocument doc = QJsonDocument::fromJson(file.readAll());
        positionsObj = doc.object();
        file.close();
    }

    // 更新或創建軸的信息
    QJsonObject axisInfo;
    if (positionsObj.contains(QString::number(axis))) {
        axisInfo = positionsObj[QString::number(axis)].toObject();
    }

    // 更新絕對位置
    axisInfo["absolutePosition"] = QString::number(position);

    // 獲取並更新最後位置
    double currentPos;
    if (GTN_GetPrfPos(cardCore, axis, &currentPos) == 0) {
        axisInfo["lastPosition"] = QString::number(static_cast<long>(currentPos));
    }

    // 保存回文件
    positionsObj[QString::number(axis)] = axisInfo;

    if (!file.open(QIODevice::WriteOnly)) {
        qDebug() << "無法打開文件進行寫入:" << file.errorString();
        QMessageBox::warning(this, "警告", "無法保存位置信息");
        return;
    }

    QJsonDocument saveDoc(positionsObj);
    file.write(saveDoc.toJson());
    file.close();

    // 更新內存中的位置
    absolutePositions[axis] = position;

    qDebug() << "已更新軸" << axis << "的絕對位置為:" << position;

    // 設置點位運動模式
    short sRtn = GTN_PrfTrap(cardCore, axis);
    if (sRtn != 0) {
        logError("設置運動模式失敗", sRtn, axis);
        return;
    }

    // 設置運動參數
    TTrapPrm trap;
    trap.acc = 0.5;
    trap.dec = 0.5;
    trap.smoothTime = 50;

    sRtn = GTN_SetTrapPrm(cardCore, axis, &trap);
    if (sRtn != 0) {
        logError("設置運動參數失敗", sRtn, axis);
        return;
    }

    // 設置速度
    sRtn = GTN_SetVel(cardCore, axis, 50.0);
    if (sRtn != 0) {
        logError("設置速度失敗", sRtn, axis);
        return;
    }

    // 設置目標位置
    sRtn = GTN_SetPos(cardCore, axis, position);
    if (sRtn != 0) {
        logError("設置位置失敗", sRtn, axis);
        return;
    }

    // 啟動運動
    sRtn = GTN_Update(cardCore, 1 << (axis - 1));
    if (sRtn != 0) {
        logError("啟動運動失敗", sRtn, axis);
        return;
    }

    // 等待運動完成並監控位置
    double actualPos;
    do {
        GTN_GetSts(cardCore, axis, &status);
        GTN_GetPrfPos(cardCore, axis, &actualPos);
        qDebug() << "當前位置:" << actualPos;

        if (!(status & 0x400)) {
            QThread::msleep(50);
            GTN_GetSts(cardCore, axis, &status);
            if (!(status & 0x400)) {
                break;
            }
        }
        QThread::msleep(10);
    } while (true);

    QMessageBox::information(this, "成功",
        QString("軸 %1 已移動到位置 %2").arg(axis).arg(position));
}

void ethercat::restoreAxisPositions()
{
    QFile file(getPositionsFilePath());
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "無法打開位置配置文件進行讀取";
        return;
    }

    QJsonDocument doc = QJsonDocument::fromJson(file.readAll());
    QJsonObject positionsObj = doc.object();

    for (auto it = absolutePositions.constBegin(); it != absolutePositions.constEnd(); ++it) {
        short axis = it.key();
        long targetPosition = it.value();

        // 檢查軸狀態並使能
        if (!enableAxis(axis)) continue;

        // 先讀取當前位置作為參考
        double currentPos;
        GTN_GetPrfPos(cardCore, axis, &currentPos);

        // 從JSON中獲取該軸的信息
        QJsonObject axisInfo = positionsObj[QString::number(axis)].toObject();
        if (axisInfo.isEmpty()) continue;  // 如果找不到該軸的信息則跳過

        long lastPosition = axisInfo["lastPosition"].toString().toLong();
        long moveDistance = targetPosition - (lastPosition - static_cast<long>(currentPos));

        // 設置點位運動模式和參數
        if (GTN_PrfTrap(cardCore, axis) != 0) continue;

        TTrapPrm trap;
        trap.acc = 0.25;
        trap.dec = 0.25;
        trap.smoothTime = 25;
        if (GTN_SetTrapPrm(cardCore, axis, &trap) != 0) continue;

        // 移動到計算後的位置
        GTN_SetPos(cardCore, axis, moveDistance);
        GTN_Update(cardCore, 1 << (axis - 1));

        // 等待運動完成
        long status;
        do {
            GTN_GetSts(cardCore, axis, &status);
            if (!(status & 0x400)) break;
            QThread::msleep(10);
        } while (true);

        qDebug() << QString("軸 %1 已恢復到絕對位置 %2").arg(axis).arg(targetPosition);
    }
}
//////////////////////HOME_POINT//////////////////////


void ethercat::on_pushButton_backhome_clicked()
{
    qDebug() << "=== 開始執行回原點操作 ===";

    // 確保位置信息已加載
    loadPositionsFromFile();

    // 1. 檢查選擇的軸
    int currentDrive = ui->comboBox_move_drive->currentIndex();
    qDebug() << "當前選擇的軸索引:" << currentDrive;

    if (currentDrive < 0) {
        QMessageBox::warning(this, "警告", "請先選擇要控制的軸");
        return;
    }
    short axis = currentDrive + 1;

    // 2. 獲取當前位置
    double currentPos;
    if (GTN_GetPrfPos(cardCore, axis, &currentPos) != 0) {
        qDebug() << "錯誤: 無法獲取當前位置";
        return;
    }
    qDebug() << "當前位置:" << currentPos;

    // 3. 從配置文件讀取初始位置
    QFile file(getPositionsFilePath());
    if (!file.open(QIODevice::ReadOnly)) {
        QMessageBox::warning(this, "警告", "無法讀取配置文件");
        return;
    }

    QJsonDocument doc = QJsonDocument::fromJson(file.readAll());
    QJsonObject axisInfo = doc.object()[QString::number(axis)].toObject();

    if (axisInfo.isEmpty()) {
        QMessageBox::warning(this, "警告", "未找到該軸的位置信息");
        return;
    }

    // 4. 獲取目標位置和最後位置
    long targetPos = axisInfo["absolutePosition"].toString().toLong();
    long lastPosition = axisInfo["lastPosition"].toString().toLong();

    // 5. 使能軸
    if (!enableAxis(axis)) {
        QMessageBox::warning(this, "警告", "軸使能失敗");
        return;
    }

    // 6. 設置運動參數
    if (GTN_PrfTrap(cardCore, axis) != 0) {
        QMessageBox::warning(this, "警告", "設置運動模式失敗");
        return;
    }

    TTrapPrm trap;
    trap.acc = 0.25;
    trap.dec = 0.25;
    trap.smoothTime = 50;

    if (GTN_SetTrapPrm(cardCore, axis, &trap) != 0) {
        QMessageBox::warning(this, "警告", "設置運動參數失敗");
        return;
    }

    // 7. 設置目標位置和速度
    // 7. 計算移動距離（使用與 restoreAxisPositions 相同的計算方式）
    long moveDistance = targetPos - (lastPosition - static_cast<long>(currentPos));
    double speed = 50.0;
    if (moveDistance < 0) {
        speed = -speed;
    }

    qDebug() << "準備設置位置和速度:";
    qDebug() << "移動距離:" << moveDistance;
    qDebug() << "速度:" << speed;

    if (GTN_SetPos(cardCore, axis, moveDistance) != 0) {
        qDebug() << "錯誤: 設置位置失敗";
        QMessageBox::warning(this, "警告", "設置位置失敗");
        return;
    }

    // 在設置速度之前檢查軸狀態
    long status;
    GTN_GetSts(cardCore, axis, &status);
    if (!(status & (0x200 | 0x400))) {
        qDebug() << "錯誤: 軸狀態異常 status:" << QString::number(status, 16);
        QMessageBox::warning(this, "警告", "軸狀態異常，請重新使能");
        return;
    }

    if (GTN_SetVel(cardCore, axis, speed) != 0) {
        GTN_GetSts(cardCore, axis, &status);
        qDebug() << "錯誤: 設置速度失敗";
        qDebug() << "當前軸狀態:" << QString::number(status, 16);
        QMessageBox::warning(this, "警告", "設置速度失敗");
        return;
    }

    // 8. 啟動運動
    if (GTN_Update(cardCore, 1 << (axis - 1)) != 0) {
        QMessageBox::warning(this, "警告", "啟動運動失敗");
        return;
    }

    // 9. 等待運動完成
    do {
        GTN_GetSts(cardCore, axis, &status);
        double actualPos;
        GTN_GetPrfPos(cardCore, axis, &actualPos);

        // 獲取編碼器位置
        double encPos;
        GTN_GetEncPos(cardCore, axis, &encPos);

        // 獲取 EtherCAT 編碼器絕對位置
        long ecatEncPos;  // 使用 long 類型
        GTN_GetEcatEncPos(cardCore, axis, &ecatEncPos);

        qDebug() << QString("軸 %1 監控 - 規劃位置: %.3f, 編碼器位置: %.3f, EtherCAT編碼器位置: %2")
                    .arg(axis)
                    .arg(actualPos)
                    .arg(encPos)
                    .arg(ecatEncPos);

        if (!(status & 0x400)) {
            QThread::msleep(50);
            GTN_GetSts(cardCore, axis, &status);
            if (!(status & 0x400)) {
                break;
            }
        }
        QThread::msleep(100);
    } while (true);

    qDebug() << "=== 運動完成 ===";

    // 先打印文件路徑，用於調試
    qDebug() << "嘗試打開文件路徑:" << getPositionsFilePath();

    // 使用新的變量名來避免重複宣告
    QFile updateFile(getPositionsFilePath());
    if (!updateFile.exists()) {
        qDebug() << "配置文件不存在";
        return;
    }

    if (!updateFile.open(QIODevice::ReadWrite)) {
        qDebug() << "無法打開配置文件，錯誤:" << updateFile.errorString();
        QMessageBox::warning(this, "警告", "無法打開配置文件，請檢查文件權限");
        return;
    }

    QJsonDocument updateDoc = QJsonDocument::fromJson(updateFile.readAll());
    QJsonObject root = updateDoc.object();
    QJsonObject updateAxisInfo = root[QString::number(axis)].toObject();

    // 獲取當前實際位置
    double finalPos;
    if (GTN_GetPrfPos(cardCore, axis, &finalPos) == 0) {
        updateAxisInfo["lastPosition"] = QString::number(static_cast<long>(finalPos));
        root[QString::number(axis)] = updateAxisInfo;

        updateFile.seek(0);
        updateFile.resize(0);
        updateFile.write(QJsonDocument(root).toJson());
        qDebug() << "已更新軸" << axis << "的最後位置為:" << finalPos;
    }
    updateFile.close();

    QMessageBox::information(this, "完成",
        QString("軸 %1 已回到初始位置").arg(axis));

    qDebug() << "目標位置(absolutePosition):" << targetPos;
    qDebug() << "最後位置(lastPosition):" << lastPosition;
    qDebug() << "當前位置(currentPos):" << currentPos;
    qDebug() << "計算的移動距離:" << moveDistance;
}


// PVT命令解析函數
// command: 要解析的PVT命令字符串
void ethercat::parsePVTCommand(const QString& command) {
    qDebug() << "\n=== parsePVTCommand 開始 ===";
    qDebug() << "準備解析PVT命令:" << command;

    // 從命令中提取軸名稱
    QString motorName = command.left(command.indexOf(":")).trimmed();

    // 使用軸名稱映射獲取軸號
    short axis = getAxisNumber(motorName);  // 確保這個函數能正確映射軸名稱到軸號

    if (axis <= 0) {
        qDebug() << "錯誤: 無效的軸號:" << axis;
        return;
    }

    // 檢查軸狀態
    long status;
    if (GTN_GetSts(cardCore, axis, &status) == 0) {
        qDebug() << "軸" << axis << "當前狀態:" << QString("0x%1").arg(status, 0, 16);

        // 如果軸未使能，嘗試使能
        if (!(status & (0x200 | 0x400))) {
            if (!enableAxis(axis)) {
                qDebug() << "錯誤: 軸" << axis << "使能失敗";
                return;
            }
        }
    }

    // 更新正則表達式以匹配新的命令格式，包括可選的起始/終端速度
    // 格式: <軸名>:PA<起始位置>-<終點位置> TA<起始時間>-<終止時間> SPA<主速度>[/<起始速度>][/<終端速度>]
    QRegularExpression regex("\"?\\S+:PA(-?\\d+)-(-?\\d+)\\s+TA([\\d.]+)-([\\d.]+)\\s+SPA([\\d.]+)(?:/([\\d.]+))?(?:/([\\d.]+))?\"?");
    QRegularExpressionMatch match = regex.match(command);

    if (!match.hasMatch()) {
        qDebug() << "錯誤: PVT命令格式不匹配";
        return;
    }

    PVTMotion motion;
    motion.axis = axis;
    motion.axisName = motorName;
    motion.startPos = match.captured(1).toDouble();
    motion.endPos = match.captured(2).toDouble();

    // 檢查是否有雙負號情況 (PA0--200000)
    if (command.contains("PA") && command.contains("--")) {
        // 如果命令中包含雙負號，確保endPos是負數
        motion.endPos = -std::abs(motion.endPos);
        qDebug() << "檢測到雙負號，將終點位置調整為:" << motion.endPos;
    }

    // 檢查 PA-200000-0 這種情況，確保起點是負值
    if (command.contains("PA-") && !command.contains("--")) {
        // 確保startPos是負數
        motion.startPos = -std::abs(motion.startPos);
        qDebug() << "檢測到起點負值，確保起點位置為:" << motion.startPos;
    }

    motion.startTime = match.captured(3).toDouble();
    motion.endTime = match.captured(4).toDouble();
    motion.speed = match.captured(5).toDouble();


    // 解析可選的起始和終端速度
    // 獲取運動方向
    int direction = (motion.endPos > motion.startPos) ? 1 : -1;


    // 設置默認值（根據主速度計算）
    motion.startVelocity = motion.speed * 0.1 * direction;  // 默認為主速度的10%
    motion.endVelocity = motion.speed * 0.1 * direction;    // 默認為主速度的10%

    // 檢查命令是否包含起始速度
    if (match.captured(6).length() > 0) {
        double startVel = match.captured(6).toDouble();

        // 確保速度方向與運動方向一致
        if ((startVel * direction) < 0 && startVel != 0) {
            startVel = -startVel;  // 調整方向
        }

        motion.startVelocity = startVel;
        qDebug() << "設置起始速度:" << startVel;
    }

    // 檢查命令是否包含終端速度
    if (match.captured(7).length() > 0) {
        double endVel = match.captured(7).toDouble();

        // 確保速度方向與運動方向一致
        if ((endVel * direction) < 0 && endVel != 0) {
            endVel = -endVel;  // 調整方向
        }

        motion.endVelocity = endVel;
        qDebug() << "設置終端速度:" << endVel;
    }

    // 檢查速度繼承（對於多段運動）
    if (multiSegmentPVTs.contains(axis) && !multiSegmentPVTs[axis].segments.isEmpty()) {
        // 獲取前一段運動的終端速度
        double prevEndVelocity = multiSegmentPVTs[axis].segments.last().endVelocity;

        // 如果命令中沒有明確指定起始速度，則繼承前一段的終端速度
        if (match.captured(6).length() == 0) {
            motion.startVelocity = prevEndVelocity;
            qDebug() << "繼承上一段運動的終端速度:" << prevEndVelocity << "作為當前段的起始速度";
        }
    }

    // 添加到多段PVT數據結構
    if (!multiSegmentPVTs.contains(axis)) {
        MultiSegmentPVT segPVT;
        segPVT.axis = axis;
        segPVT.axisName = motorName;
        multiSegmentPVTs[axis] = segPVT;
    }

    multiSegmentPVTs[axis].segments.append(motion);

    qDebug() << "成功解析PVT命令，軸號:" << axis
             << "軸名稱:" << motorName
             << "起始位置:" << motion.startPos
             << "結束位置:" << motion.endPos
             << "時間:" << motion.startTime << "-" << motion.endTime
             << "主速度:" << motion.speed
             << "起始速度:" << motion.startVelocity
             << "終端速度:" << motion.endVelocity;
}





//功能：處理多段PVT運動（multiSegmentPVTs），將多段運動數據轉換為PVT點，然後執行以下步驟：
//檢查軸狀態，若軸正在運動則延遲執行
//清除軸錯誤狀態
//設置軸為PVT模式
//上傳PVT數據
//選擇PVT表
//啟動PVT運動

 void ethercat::executePVTMotions() {
    qDebug() << "\n=== executePVTMotions 開始 ===";
    qDebug() << "multiSegmentPVTs包含" << multiSegmentPVTs.size() << "個軸的數據:";

    for (auto it = multiSegmentPVTs.begin(); it != multiSegmentPVTs.end(); ++it) {
        qDebug() << "  軸" << it.key() << " (" << it.value().axisName
                 << ") 包含" << it.value().segments.size() << "段運動";
    }

    short mask = 0;
    bool needDelay = false;

    // 檢查所有軸的運動狀態
    for (auto it = multiSegmentPVTs.begin(); it != multiSegmentPVTs.end(); ++it) {
        short axis = it.key();
        long status;

        // 獲取軸的狀態
        if (GTN_GetSts(cardCore, axis, &status) == 0) {
            // 檢查軸是否正在運動（0x400是運動中的狀態位）
            if (status & 0x400) {
                needDelay = true;
                qDebug() << "軸" << axis << "仍在運動中，延遲執行";
                break;
            }

            // 清除軸狀態，為新的運動做準備
            GTN_ClrSts(cardCore, axis);
            QThread::msleep(50);  // 給控制卡一些時間處理清除狀態的命令
        }
    }

    // 如果有軸正在運動，延遲執行
    if (needDelay) {
        QTimer::singleShot(200, this, [this]() {
            executePVTMotions();  // 200ms後重試
        });
        return;
    }

    // 為每個軸處理PVT數據
    for (auto it = multiSegmentPVTs.begin(); it != multiSegmentPVTs.end(); ++it) {
        short axis = it.key();
        const MultiSegmentPVT& multiSegment = it.value();

        qDebug() << "處理軸" << axis << " (" << multiSegment.axisName
                 << ") 的PVT數據，共" << multiSegment.segments.size() << "段";

        // 設置軸為PVT模式
        if (GTN_PrfPvt(cardCore, axis) != 0) {
            qDebug() << "錯誤: 設置PVT模式失敗，軸:" << axis;
            return;
        }

        // === 修改開始: 使用PvtTableComplete替代點生成和PvtTable ===

        // 用於存儲關鍵點數據
        QVector<double> times;           // 時間點
        QVector<double> positions;       // 位置點
        QVector<double> coeffA;          // 多項式係數a（會由函數自動計算）
        QVector<double> coeffB;          // 多項式係數b（會由函數自動計算）
        QVector<double> coeffC;          // 多項式係數c（會由函數自動計算）
        double startVelocity = 0;        // 整段運動的起始速度
        double endVelocity = 0;          // 整段運動的終端速度
        double lastEndTime = 0;          // 上一段的結束時間

        // 處理每一段運動
        int segmentCount = 0;
        for (const PVTMotion& segment : multiSegment.segments) {
            // 僅添加該段的起點和終點（Complete描述方式只需要關鍵點）

            // 第一個點的時間為相對於前一段的結束時間
            double segmentStartTime = (segment.startTime + lastEndTime) * 1000; // 轉為毫秒
            double segmentEndTime = (segment.endTime + lastEndTime) * 1000;     // 轉為毫秒

            // 添加該段的起點和終點
            times.append(segmentStartTime);
            times.append(segmentEndTime);
            positions.append(segment.startPos);
            positions.append(segment.endPos);

            // 為每個點分配係數空間（將在GTN_PvtTableComplete中計算）
            coeffA.append(0);
            coeffA.append(0);
            coeffB.append(0);
            coeffB.append(0);
            coeffC.append(0);
            coeffC.append(0);

            // 記錄第一段的起始速度
            if (segmentCount == 0) {
                startVelocity = segment.startVelocity;
                qDebug() << "第一段起始速度:" << startVelocity;
            }

            // 記錄最後一段的終端速度
            if (segmentCount == multiSegment.segments.size() - 1) {
                endVelocity = segment.endVelocity;
                qDebug() << "最後一段終端速度:" << endVelocity;
            }

            lastEndTime = segment.endTime;
            segmentCount++;
        }

        // 準備好係數數組的副本（GTN_PvtTableComplete會修改這些數組）
        QVector<double> timesCopy = times;
        QVector<double> positionsCopy = positions;
        QVector<double> coeffACopy = coeffA;
        QVector<double> coeffBCopy = coeffB;
        QVector<double> coeffCCopy = coeffC;

        // 添加詳細調試輸出
        qDebug() << "\n=== 軸" << axis << "實際下載到控制卡的關鍵點數據 ===";
        qDebug() << "關鍵點數量:" << timesCopy.size();

        // 打印全部關鍵點的詳細數據
        for (int i = 0; i < timesCopy.size(); i += 2) {
            if (i + 1 < timesCopy.size()) {
                qDebug() << "段" << (i/2) << ": 起始時間=" << timesCopy[i]
                        << "ms, 起始位置=" << positionsCopy[i]
                        << ", 終止時間=" << timesCopy[i+1]
                        << "ms, 終止位置=" << positionsCopy[i+1];
            }
        }

        qDebug() << "整段運動的起始速度:" << startVelocity << ", 終端速度:" << endVelocity;

        // 明確使用軸號作為表ID - 確保與軸號一致
        short tableId = axis;  // 例如：100W -> 軸1 -> 表ID 1
        qDebug() << "軸" << axis << "使用表ID:" << tableId << "（與軸號相同）";

        // 使用Complete描述方式上傳PVT數據
        int result = GTN_PvtTableComplete(cardCore, tableId, timesCopy.size(),
                                        timesCopy.data(), positionsCopy.data(),
                                        coeffACopy.data(), coeffBCopy.data(), coeffCCopy.data(),
                                        startVelocity, endVelocity);

        if (result != 0) {
            qDebug() << "錯誤: 軸" << axis << "下載PVT數據失敗，錯誤碼:" << result;
            return;
        }

        // === 修改結束: 使用PvtTableComplete替代點生成和PvtTable ===

        // 選擇該軸的PVT表 (這部分不變)
        if (GTN_PvtTableSelect(cardCore, axis, tableId) != 0) {
            qDebug() << "錯誤: 軸" << axis << "選擇PVT表格失敗";
            return;
        }

        // 將該軸添加到啟動掩碼中
        mask |= (1 << (axis - 1));
    }

    qDebug() << "啟動所有軸的PVT運動，最終掩碼值:" << QString("0x%1").arg(mask, 0, 16);

    // 檢查所有軸是否就緒 (以下代碼保持不變)
    for (auto it = axisPVTCache.begin(); it != axisPVTCache.end(); ++it) {
        short axis = it.key();
        long sts;

        // 清除軸錯誤
        GTN_ClrSts(cardCore, axis);

        // 直接嘗試使能軸（與廠商示例相同）
        qDebug() << "使能軸" << axis;
        GTN_AxisOn(cardCore, axis);
        Sleep(1000);  // 等待使能完成

        // 確保軸處於PVT模式
        if (GTN_PrfPvt(cardCore, axis) != 0) {
            qDebug() << "錯誤: 設置軸" << axis << "為PVT模式失敗";
            // 從掩碼中移除該軸
            mask &= ~(1 << (axis - 1));
        }
    }

    // 如果所有軸都已移除，則不啟動
    if (mask == 0) {
        qDebug() << "錯誤: 沒有可用的軸可以啟動";
        return;
    }

    // 啟動PVT運動
    qDebug() << "\n===== 準備啟動PVT運動 =====";
    qDebug() << "掩碼:" << QString("0x%1").arg(mask, 0, 16);
    qDebug() << "當前狀態: isExecutingCommand=" << isExecutingCommand
             << ", pendingSequences數量=" << pendingSequences.size();

    // 打印每個軸的狀態
    for (auto it = axisPVTCache.begin(); it != axisPVTCache.end(); ++it) {
        short axis = it.key();
        qDebug() << "軸" << axis << "狀態: "
                 << "命令就緒=" << axisCommandReady.value(axis, false)
                 << ", 隊列長度=" << (axisCommandQueues.contains(axis) ? axisCommandQueues[axis].size() : 0);
    }

    int result = GTN_PvtStart(cardCore, mask);
    qDebug() << "GTN_PvtStart 返回結果:" << result;
    if (result != 0) {
        qDebug() << "錯誤: 啟動PVT運動失敗，錯誤碼:" << result;

        // 替代方案：直接輸出常見錯誤碼的含義
        switch(result) {
            case 1:
                qDebug() << "錯誤描述: 指令執行錯誤";
                break;
            case 7:
                qDebug() << "錯誤描述: 指令參數錯誤";
                break;
            case -1:
                qDebug() << "錯誤描述: 軸狀態錯誤";
                break;
            case -6:
                qDebug() << "錯誤描述: 軸未使能";
                break;
            default:
                qDebug() << "錯誤描述: 未知錯誤，請查閱控制卡手冊";
                break;
        }
    } else {
        qDebug() << "成功啟動PVT運動";

        // 啟動運動監控
        monitorAllPVTMotions();
    }
}


//////////////////////////////////////////////////////////////////////////////////////

// 在ethercat.cpp中添加新函數，用於處理多軸同步命令
void ethercat::processSensorCommand(const QString& command) {
    // 檢查是否是多軸命令
    if (command.contains(",")) {
        // 將整個命令作為一個序列中的一個命令發送
        QStringList commands;
        commands.append(command);
        sendPVTCommands(commands);
    } else {
        // 單軸命令也按序列處理
        QStringList commands;
        commands.append(command);
        sendPVTCommands(commands);
    }
}

//////////////////////////////////////////////////////////////////////////////////////

//////////////////////////////////////////////////////////////////////////////////////



///////////////////////////////////////////////////////////////////////////////////////

// 在 ethercat.cpp 中添加新函數實現
short ethercat::getAxisNumber(const QString& axisName) const {
    if (axisName == "100W") return 1;
    if (axisName == "200W") return 2;
    if (axisName == "300W") return 3;
    if (axisName == "400W") return 4;
    if (axisName == "500W") return 5;
    if (axisName == "600W") return 6;
    if (axisName == "700W") return 7;

    // 如果未匹配到，則輸出警告並返回默認軸號
    qDebug() << "警告: 未知的軸名稱" << axisName << "，使用默認軸號1";
    return 1;
}


//表單ID對
///////////////////////////////////////////////////////////////////////////////////////
// 初始化表單ID對
void ethercat::initializeTableIds() {
    qDebug() << "\n===== [初始化表單ID] =====";
    qDebug() << "- 當前表單映射大小：" << axisTableIds.size();

    // 不僅在 axisTableIds 為空時初始化，還確保每個軸都有對應的表單ID
    for (int axis = 1; axis <= 8; ++axis) {  // 支援最多8個軸
        if (!axisTableIds.contains(axis)) {
            TableIdPair pair;
            pair.primaryId = axis;
            pair.secondaryId = axis + 10;  // 次表單ID比主表單ID大10
            pair.usingPrimary = true;      // 默認使用主表單

            axisTableIds[axis] = pair;
            qDebug() << "為軸" << axis << "設置表單ID對: 主=" << pair.primaryId << ", 次=" << pair.secondaryId;
        } else {
            // 已存在的表單ID對，確認其有效性
            TableIdPair& pair = axisTableIds[axis];
            if (pair.primaryId <= 0 || pair.secondaryId <= 0) {
                // 修復無效的表單ID
                pair.primaryId = (pair.primaryId <= 0) ? axis : pair.primaryId;
                pair.secondaryId = (pair.secondaryId <= 0) ? axis + 10 : pair.secondaryId;
                qDebug() << "修復軸" << axis << "的無效表單ID對: 主=" << pair.primaryId << ", 次=" << pair.secondaryId;
            } else {
                qDebug() << "軸" << axis << "的表單ID對已存在: 主=" << pair.primaryId << ", 次=" << pair.secondaryId;
            }
        }
    }

    qDebug() << "- 初始化後表單映射大小：" << axisTableIds.size();
}

////////////////////////////////////////////////////////////////////////////////////

//////// from   processSensorCommandQueue();
//////////////PVT运动发出讯号后的接收函数 step1

/**
 * @brief 發送PVT命令序列到控制系統
 *
 * 該函數接收一組PVT命令，創建命令序列並添加到待處理隊列中。
 * 如果當前沒有命令在執行，則立即開始執行新添加的命令序列。
 *
 * @param commands 要執行的PVT命令列表
 */
void ethercat::sendPVTCommands(const QStringList& commands) {
    /**
     * 【PVT命令批處理系統】
     * 功能：收集和批處理多個電機的PVT命令，等待所有命令就緒或超時後統一執行
     *
     * 工作流程：
     * 1. 收集命令 - 將來自不同電機的命令收集到同一批次中
     * 2. 等待完成 - 等待10秒或直到所有預期電機的命令都到齊
     * 3. 執行命令 - 將命令加入執行隊列並按順序執行
     *
     * 關鍵變量說明：
     * - batchCommands: 存儲每個批次的所有命令
     * - batchMotors: 批次ID -> 該批次已收到的電機列表
     * - batchTimestamps: 批次ID -> 批次創建時間
     * - currentBatchId: 當前正在處理的批次標識符
     */

    // ===== 【批次管理變量】 =====
    static QMap<QString, QStringList> batchCommands; // 批次ID -> 該批次所有命令
    static QMap<QString, QSet<QString>> batchMotors; // 批次ID -> 該批次已收到的電機列表
    static QMap<QString, QDateTime> batchTimestamps; // 批次ID -> 批次創建時間
    static QString currentBatchId = "";              // 當前正在處理的批次ID

    qDebug() << "\n===== 【PVT命令批處理開始】 收到" << commands.size() << "條命令 =====";

    // ===== 【批次初始化】 =====
    // 如果沒有進行中的批次則創建新批次
    if (currentBatchId.isEmpty()) {
        currentBatchId = QDateTime::currentDateTime().toString("yyyyMMddhhmmsszzz");
        batchTimestamps[currentBatchId] = QDateTime::currentDateTime();
        qDebug() << "創建新批次: ID=" << currentBatchId;
    }

    // 確保批次數據結構已初始化
    if (!batchCommands.contains(currentBatchId)) {
        batchCommands[currentBatchId] = QStringList();
        batchMotors[currentBatchId] = QSet<QString>();
    }

    // ===== 【命令解析與收集】 =====
    // 添加命令到當前批次
    batchCommands[currentBatchId].append(commands);

    // 從命令中提取電機名稱並存儲
    for (const QString& cmd : commands) {
        QString cmdMotorName = cmd.left(cmd.indexOf(":")).trimmed();
        if (!cmdMotorName.isEmpty()) {
            batchMotors[currentBatchId].insert(cmdMotorName);
        }
    }

    qDebug() << "當前批次(" << currentBatchId << ")已收集: "
             << batchMotors[currentBatchId].size() << "個電機";

    // ===== 【批次完成條件檢查】 =====
    // 檢查是否已收集到所有電機的命令
    QSet<QString> expectedMotors = batchMotors[currentBatchId];
    bool allMotorsReceived = true;

    // 檢查是否所有預期電機都已收到命令
    for (const QString& motor : expectedMotors) {
        if (!batchMotors[currentBatchId].contains(motor)) {
            allMotorsReceived = false;
            break;
        }
    }

    // 檢查批次是否已超過10秒時限
    bool isTimeout = batchTimestamps[currentBatchId].msecsTo(QDateTime::currentDateTime()) > 10000;

    // ===== 【批次執行處理】 =====
    // 如果已收集完所有命令或超時，則進行處理
    if (allMotorsReceived || isTimeout) {
        // 將電機集合轉換為列表以便顯示
        QStringList collectedMotors;
        for (const QString& motor : batchMotors[currentBatchId]) {
            collectedMotors.append(motor);
        }

        qDebug() << "批次完成條件滿足: "
                 << (allMotorsReceived ? "已收集所有電機" : "超過10秒時限")
                 << " | 電機: " << collectedMotors.join(", ");

        // ===== 【創建命令序列】 =====
        // 將收集的命令打包為執行序列
        CommandSequence sequence;
        sequence.rawCommands = batchCommands[currentBatchId];
        sequence.currentIndex = 0;

        // 添加到待處理隊列
        pendingSequences.append(sequence);

        // 清理當前批次資源
        batchCommands.remove(currentBatchId);
        batchMotors.remove(currentBatchId);
        batchTimestamps.remove(currentBatchId);
        currentBatchId = ""; // 重置當前批次ID

        // ===== 【序列執行控制】 =====
        // 根據當前執行狀態決定是立即執行還是等待
        if (!isExecutingCommand) {
            // 沒有正在執行的命令，立即執行
            qDebug() << "立即執行命令序列";
            executeNextSequence();
        } else {
            // 已有命令在執行，設置完成後的回調
            qDebug() << "已有命令執行中，註冊完成後自動執行下一序列";

            // 使用信號連接，當前命令完成後自動執行下一個序列
            connect(this, &ethercat::commandSequenceCompleted, this, [this]() {
                qDebug() << "收到命令完成信號，執行下一序列";
                executeNextSequence();
            }, Qt::SingleShotConnection);  // 確保信號只觸發一次
        }
    } else {
        // ===== 【繼續等待更多命令】 =====
              QStringList collectedMotors;
              for (const QString& motor : batchMotors[currentBatchId]) {
                  collectedMotors.append(motor);
              }

        qDebug() << "繼續等待: 已收集" << collectedMotors.size()
                 << "個電機 | 名稱: " << collectedMotors.join(", ");

        // 計算並顯示缺少的電機
        QStringList missingMotors;
        for (const QString& motor : expectedMotors) {
            if (!batchMotors[currentBatchId].contains(motor)) {
                missingMotors.append(motor);
            }
        }

        if (!missingMotors.isEmpty()) {
            qDebug() << "尚缺: " << missingMotors.join(", ");
        }
    }

    qDebug() << "===== 【PVT命令批處理結束】 =====\n";
}

//函數會檢查系統當前是否有正在執行的命令。如果沒有正在執行的命令（isExecutingCommand為false），則立即調用executeNextSequence()函數開始執行新添加的命令序列；如果已有命令正在執行，則新添加的序列會在當前序列完成後自動執行。

//////////////PVT运动发出讯号后的接收函数 step2

void ethercat::executeNextSequence() {
    // 簡單記錄函數調用
    qDebug() << "\n===== 執行下一序列 =====";
    qDebug() << "待處理序列數量:" << pendingSequences.size();

    // 如果沒有待處理序列，標記為未執行命令並返回
    if (pendingSequences.isEmpty()) {
        isExecutingCommand = false;
        qDebug() << "所有命令序列已處理完成";
        return;
    }

    // 有待處理序列，標記為正在執行命令
    isExecutingCommand = true;

    // 取出並準備執行第一個序列
    currentSequence = pendingSequences.takeFirst();
    currentSequence.currentIndex = 0;

    qDebug() << "開始執行新序列，包含" << currentSequence.rawCommands.size() << "條命令";

    // 不清理緩存，直接執行命令序列
    executeCommandSequenceNew(currentSequence.rawCommands);
}


//////////////PVT运动发出讯号后的接收函数 step3
void ethercat::executeCommandSequence(const QStringList& commands) {
    GTN_ClearTime(cardCore, TIME_ELAPSE_PROFILE);
    qDebug() << "清除中斷執行時間統計";

    qDebug() << "\n===== [PVT斷點3-命令序列執行開始] =====";
    qDebug() << "- 命令數量：" << commands.size();
    qDebug() << "- 當前時間：" << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    qDebug() << "- 執行狀態：" << isExecutingCommand;
    qDebug() << "- 待處理序列數量：" << pendingSequences.size();




    // 【重要】確保表單ID已初始化
    initializeTableIds();

    // 清除PVT緩存，但不重置命令隊列（防止丟失命令）
    axisPVTCache.clear();

    // 解析命令到隊列
    parseCommandsToQueues(commands);
    qDebug() << "[DEBUG] 嘗試啟動Watch監控";
    // 啟動Watch監控以檢測同步問題
    //startWatchMonitoring();



    // 處理每個軸的第一個命令
    QSet<short> activeAxes;
    for (auto it = axisCommandQueues.begin(); it != axisCommandQueues.end(); ++it) {
        short axis = it.key();

        if (!it.value().isEmpty()) {
            QString command = it.value().dequeue();
            parseCommandToCache(command);
            activeAxes.insert(axis);

            // 標記第一個命令已執行
            executedAxisCommands[axis]++;
            qDebug() << "軸" << axis << "第一個命令標記為已執行";
        }
    }

    qDebug() << "\n===== [PVT斷點3.3-處理第一個命令完成] =====";
    qDebug() << "- 已激活的轴数量：" << activeAxes.size();

    // 按照廠商流程：先清除錯誤，再使能軸，然後設置PVT模式
    short mask = 0;
    for (short axis : activeAxes) {
        // 1. 先清除軸錯誤
        GTN_ClrSts(cardCore, axis);
        QThread::msleep(50);

        // 2. 檢查軸是否已經使能，如果沒有才使能
        long status;
        if (GTN_GetSts(cardCore, axis, &status) == 0) {
            // 檢查使能狀態位 (0x200)
            if (!(status & 0x200)) {
                // 軸未使能，需要使能
                GTN_AxisOn(cardCore, axis);
                qDebug() << "軸" << axis << "未使能，正在使能";
                Sleep(1000);  // 等待使能完成
            }
        }

        // 3. 設置為PVT模式
        if (GTN_PrfPvt(cardCore, axis) != 0) {
            qDebug() << "錯誤: 設置軸" << axis << "為PVT模式失敗";
            continue;
        }
        qDebug() << "設置軸" << axis << "為PVT模式成功";



        // 4. 上傳PVT數據到當前表單
        short currentTableId = getCurrentTableId(axis);

        qDebug() << "\n===== [PVT斷點3.5-獲取當前表單ID] =====";
        qDebug() << "- 軸號：" << axis;
        qDebug() << "- 當前表單ID：" << currentTableId;

        // 添加詳細的表單ID信息打印
        if (axisTableIds.contains(axis)) {
            const TableIdPair& pair = axisTableIds[axis];
            qDebug() << "現在使用的表單ID: " << currentTableId;
        }

        uploadPVTData(axis, currentTableId);

        qDebug() << "\n===== [PVT斷點3.6-PVT數據上傳完成] =====";
        qDebug() << "- 軸號：" << axis;
        qDebug() << "- 上傳到表單ID：" << currentTableId;

        // 5. 選擇PVT表
        if (GTN_PvtTableSelect(cardCore, axis, currentTableId) != 0) {
            qDebug() << "錯誤: 軸" << axis << "選擇PVT表格失敗";
            continue;
        }

        qDebug() << "\n===== [PVT斷點3.7-選擇PVT表完成] =====";
        qDebug() << "- 軸號：" << axis;
        qDebug() << "- 選擇的表單ID：" << currentTableId;

        // 6. 添加到啟動掩碼
        mask |= (1 << (axis - 1));
    }

    // 如果沒有可用的軸，則退出
    if (mask == 0) {
        qDebug() << "錯誤: 沒有可用的軸可以啟動";
        return;
    }

    qDebug() << "\n===== [PVT斷點3.8-準備啟動PVT運動] =====";
    qDebug() << "- 啟動掩碼：" << QString("0x%1").arg(mask, 0, 16);

    // 7. 啟動PVT運動
    int result = GTN_PvtStart(cardCore, mask);

    qDebug() << "\n===== [PVT斷點3.9-PVT運動啟動結果] =====";
    qDebug() << "- 啟動結果：" << result;

    // 8. 監控運動
    if (result == 0) {
        qDebug() << "\n===== [PVT斷點3.10-開始監控PVT運動] =====";
        monitorAllPVTMotions();
    } else {
        qDebug() << "錯誤: 啟動PVT運動失敗，錯誤碼:" << result;
    }
}

//////////////PVT運動發出訊號後的接收函數 step4
//////////////PVT運動發出訊號後的接收函數 step4
void ethercat::monitorAllPVTMotions() {
    QTimer* monitorTimer = new QTimer(this);
    connect(monitorTimer, &QTimer::timeout, this, [this, monitorTimer]() {
        bool allComplete = true;

        // 檢查所有軸的運動狀態
        for (auto it = axisPVTCache.begin(); it != axisPVTCache.end(); ++it) {
            short axis = it.key();
            long status;

            if (GTN_GetSts(cardCore, axis, &status) == 0) {
                // 檢查運動是否完成
                if (~status & 0x400) {
                    // 軸已停止
                    // qDebug() << "軸" << axis << "運動完成";
                } else {
                    allComplete = false;

                    // 檢查運動進度
                    short tableId;
                    double time;
                    if (GTN_PvtStatus(cardCore, axis, &tableId, &time) == 0) {
                        // 獲取當前表單的總時間
                        double totalTime = 0;
                        if (!axisPVTCache[axis].times.isEmpty()) {
                            totalTime = axisPVTCache[axis].times.last() - axisPVTCache[axis].times.first();
                        }

                        // 計算進度百分比
                        double progress = (totalTime > 0) ? (time / totalTime * 100.0) : 0;
                        // qDebug() << "軸" << axis << "進度: " << QString::number(progress, 'f', 1) << "% | "
                        //          << "就緒狀態: " << (axisCommandReady.value(axis, false) ? "是" : "否");

                        // 在進度達到50%時就開始準備下一個命令，確保有足夠時間準備
                        if (progress >= 0.0 && progress < 60.0 && !axisCommandReady.value(axis, false)) {
                            // qDebug() << "===== 進度達到預設閾值，準備上傳下一個命令 =====";
                            bool hasNextCommand = prepareNextCommand(axis);

                            if (hasNextCommand) {
                                // qDebug() << "\n===== [命令執行信息] =====";
                                // qDebug() << "- 使用的表單ID：" << getCurrentTableId(axis);

                                // 有下一個命令，上傳表單並設置就緒標誌
                                short nextTableId = getNextTableId(axis);
                                // 確保表單ID已正確初始化
                                if (!axisTableIds.contains(axis)) {
                                    initializeTableIds();
                                    // qDebug() << "為軸" << axis << "初始化表單ID對";
                                }

                                uploadPVTData(axis, nextTableId);  // 上傳表單數據
                                axisCommandReady[axis] = true;     // 設置就緒標誌
                                // qDebug() << "軸" << axis << "有下一個命令，上傳數據到表單" << nextTableId << "並設置就緒標誌為 true";
                            } else {
                                // 沒有下一個命令時，仍然嘗試一次從待處理序列中查找
                                if (!pendingSequences.isEmpty()) {
                                    // qDebug() << "在直接隊列中未找到命令，嘗試從待處理序列中查找...";
                                    // 再次嘗試準備命令，這次會從pendingSequences中查找
                                    hasNextCommand = prepareNextCommand(axis);
                                    if (hasNextCommand) {
                                        // qDebug() << "成功從待處理序列中找到命令，上傳表單數據";
                                        short nextTableId = getNextTableId(axis);
                                        uploadPVTData(axis, nextTableId);
                                        axisCommandReady[axis] = true;
                                    } else {
                                        // 還是沒有命令，設置就緒標誌為 false
                                        axisCommandReady[axis] = false;
                                        // qDebug() << "軸" << axis << "沒有下一個命令，不上傳表單並設置就緒標誌為 false";
                                    }
                                } else {
                                    // 沒有下一個命令，不上傳表單並設置就緒標誌為 false
                                    axisCommandReady[axis] = false;
                                    // qDebug() << "軸" << axis << "沒有下一個命令，不上傳表單並設置就緒標誌為 false";
                                }
                            }
                            // qDebug() << "===== 上傳下一個命令完成 =====";
                        }

                        // 如果進度超過70%但還未準備好下一個命令，再次嘗試準備
                        if (progress >= 70.0 && progress < 80.0 && !axisCommandReady.value(axis, false)) {
                            // qDebug() << "===== 進度已達70%但命令未就緒，再次嘗試準備命令 =====";
                            bool hasNextCommand = prepareNextCommand(axis);
                            if (hasNextCommand) {
                                // qDebug() << "成功準備下一個命令";
                                short nextTableId = getNextTableId(axis);
                                uploadPVTData(axis, nextTableId);
                                axisCommandReady[axis] = true;
                            }
                        }

                        // qDebug() << "軸" << axis << "表單切換條件檢查：進度=" << progress
                        //          << "%, 閾值=85.0%, 命令就緒="
                        //          << (axisCommandReady.value(axis, false) ? "是" : "否");

                        // 降低切換表單的進度閾值，確保有足夠時間切換
                        if (progress > 85.0 && axisCommandReady.value(axis, false)) {
                            // 獲取下一個表單ID
                            short nextTableId = getNextTableId(axis);

                            // qDebug() << "軸" << axis << "表單切換: 進度" << QString::number(progress, 'f', 1)
                            //          << "% | 當前ID:" << getCurrentTableId(axis)
                            //          << " → 下一ID:" << nextTableId;

                            if (GTN_PvtTableSelect(cardCore, axis, nextTableId) == 0) {
                                // qDebug() << "軸" << axis << "成功切換到表單ID:" << nextTableId;

                                // 更新表單狀態
                                switchToNextTable(axis);

                                // 標記命令未準備好
                                axisCommandReady[axis] = false;
                            } else {
                                // 如果切換失敗，記錄錯誤
                                // qDebug() << "軸" << axis << "切換到表單ID:" << nextTableId << "失敗";
                            }
                        }
                    }
                }
            }
        }

        if (allComplete) {
            monitorTimer->stop();
            monitorTimer->deleteLater();

            // qDebug() << "\n===== 所有PVT運動完成 =====";
            // qDebug() << "當前狀態: isExecutingCommand=" << isExecutingCommand
            //          << ", pendingSequences數量=" << pendingSequences.size()
            //          << ", isWaitingForNext=" << isWaitingForNext;

            // 打印所有軸的實際位置
            for (auto it = axisPVTCache.begin(); it != axisPVTCache.end(); ++it) {
                short axis = it.key();
                double actualPos, actualVel;
                if (GTN_GetPrfPos(cardCore, axis, &actualPos) == 0 &&
                    GTN_GetPrfVel(cardCore, axis, &actualVel) == 0) {
                    qDebug() << "軸" << axis << "運動完成後的實際位置:" << actualPos
                             << "實際速度:" << actualVel;
                }
            }

            // 檢查各軸是否還有未執行的命令
            bool hasMoreCommands = false;
            QMap<short, bool> axisHasCommands;  // 記錄每個軸是否還有命令

            qDebug() << "\n===== 檢查各軸命令執行情況 =====";

            // 首先檢查每個軸的命令隊列是否為空
            for (auto it = axisCommandQueues.begin(); it != axisCommandQueues.end(); ++it) {
                    short axis = it.key();
                bool hasCommand = !it.value().isEmpty();
                axisHasCommands[axis] = hasCommand;

                qDebug() << "軸" << axis << "命令隊列剩餘:" << it.value().size()
                         << "個命令, 狀態:" << (hasCommand ? "非空" : "空");

                if (hasCommand) {
                    hasMoreCommands = true;
                    qDebug() << "軸" << axis << "還有命令需要執行";
                }
            }

            // 如果檢測到還有命令需要執行
            if (hasMoreCommands) {
                qDebug() << "檢測到軸命令未全部執行，處理下一條命令";

                // 創建一個新的活動軸集合
                QSet<short> activeAxes;

                // 對每個有命令的軸進行處理
                for (auto it = axisHasCommands.begin(); it != axisHasCommands.end(); ++it) {
                    short axis = it.key();
                    bool hasCommand = it.value();

                    if (hasCommand) {
                        // 確保隊列非空再進行處理
                        if (!axisCommandQueues[axis].isEmpty()) {
                            QString command = axisCommandQueues[axis].dequeue();
                            qDebug() << "從軸" << axis << "的隊列中取出命令:" << command;

                        // 清除這個軸的緩存
                        if (axisPVTCache.contains(axis)) {
                            PVTDataCache newCache;
                            newCache.isDirty = true;
                            newCache.isUploaded = false;
                            axisPVTCache[axis] = newCache;
                        }

                            // 解析命令到緩存
                        parseCommandToCache(command);
                        activeAxes.insert(axis);
                        } else {
                            qDebug() << "警告: 軸" << axis << "被標記為有命令，但隊列實際為空";
                        }
                    }
                }

                // 如果有活動軸，開始處理下一條命令
                if (!activeAxes.isEmpty()) {
                    qDebug() << "開始處理" << activeAxes.size() << "個軸的下一條命令";
                    short mask = 0;

                    for (short axis : activeAxes) {
                        // 清除軸錯誤
                        GTN_ClrSts(cardCore, axis);
                        QThread::msleep(20);

                        // 設置PVT模式
                        if (GTN_PrfPvt(cardCore, axis) != 0) {
                            qDebug() << "錯誤: 設置軸" << axis << "為PVT模式失敗";
                            continue;
                        }

                        // 獲取當前表單ID
                        short currentTableId = getCurrentTableId(axis);

                        // 上傳PVT數據
                        uploadPVTData(axis, currentTableId);

                        // 選擇PVT表單
                        if (GTN_PvtTableSelect(cardCore, axis, currentTableId) != 0) {
                            qDebug() << "錯誤: 軸" << axis << "選擇PVT表格失敗";
                            continue;
                        }

                        // 添加到啟動掩碼
                        mask |= (1 << (axis - 1));
                    }

                    // 啟動PVT運動
                    if (mask != 0) {
                        int result = GTN_PvtStart(cardCore, mask);
                        if (result == 0) {
                            qDebug() << "成功啟動下一條PVT命令，繼續監控";

                            // 更新已執行命令計數
                            for (short axis : activeAxes) {
                                if (totalAxisCommands.contains(axis)) {
                                    executedAxisCommands[axis]++;
                                    qDebug() << "軸" << axis << "已執行命令數增加到:" << executedAxisCommands[axis];
                                }
                                axisCommandReady[axis] = false;
                            }

                            // 不發出完成信號，繼續監控
                            QTimer::singleShot(10, this, &ethercat::monitorAllPVTMotions);
                            return;
                        } else {
                            qDebug() << "啟動PVT運動失敗，錯誤碼:" << result;
                        }
                    } else {
                        qDebug() << "沒有有效的軸掩碼，無法啟動PVT運動";
                    }
                } else {
                    qDebug() << "雖然檢測到有命令，但沒有實際處理的活動軸";
                }
            }

            // 如果沒有更多命令，或無法處理剩餘命令，完成序列
            qDebug() << "沒有更多命令需要執行或無法處理，完成序列";


            // 在QTimer::singleShot之前添加時間統計檢查
            unsigned long profileTime, profileTimeMax;
            GTN_GetTime(cardCore, TIME_ELAPSE_PROFILE, &profileTime, &profileTimeMax);
            if (profileTimeMax > 1000) {  // 如果最大執行時間超過1000微秒(1毫秒)
                qDebug() << "警告: 控制卡中斷執行時間過長，可能存在阻塞,"
                        << "當前:" << profileTime
                        << "最大:" << profileTimeMax;


                // 記錄詳細的Watch數據以分析性能問題
               // QString filename = getWatchDataFilePath();
               // GTN_PrintWatch(cardCore, filename.toLocal8Bit().data());
                //analyzeWatchData(filename);
            }



            // 添加固定延遲，確保所有軸的運動有足夠時間完成
            QTimer::singleShot(1000, this, [this]() {  // 增加到1000ms
                // 檢查所有軸是否真正到達目標位置
                bool allReachedTarget = true;
                qDebug() << "\n===== 命令序列完成後所有軸的位置檢查 =====";

                for (auto it = axisPVTCache.begin(); it != axisPVTCache.end(); ++it) {
                    short axis = it.key();
                    double actualPos, targetPos = 0;
                    long status;

                    // 檢查軸狀態
                    if (GTN_GetSts(cardCore, axis, &status) == 0) {
                        qDebug() << "軸" << axis << "狀態:" << QString("0x%1").arg(status, 0, 16);

                        // 檢查是否還在運動中
                        if (status & 0x400) {
                            qDebug() << "警告: 軸" << axis << "仍在運動中";
                            allReachedTarget = false;
                        }
                    }

                    // 獲取實際位置
                    if (GTN_GetPrfPos(cardCore, axis, &actualPos) == 0) {
                        // 獲取目標位置（如果有）
                        if (!it.value().positions.isEmpty()) {
                            targetPos = it.value().positions.last();
                            double posError = actualPos - targetPos;
                            double errorPercentage = (targetPos != 0) ? (posError / targetPos) * 100.0 : 0;

                            qDebug() << "軸" << axis << "最終位置:" << actualPos
                                     << "目標位置:" << targetPos
                                     << "位置誤差:" << posError
                                     << "誤差百分比:" << QString::number(errorPercentage, 'f', 2) << "%";

                            // 檢查誤差是否超過容許範圍（可以根據需要調整容許誤差）
                            double tolerance = 1000.0; // 容許誤差，根據你的系統調整
                            if (std::abs(posError) > tolerance) {
                                allReachedTarget = false;
                                qDebug() << "警告: 軸" << axis << "未到達目標位置，誤差超過容許範圍";
                            }
                        } else {
                            qDebug() << "軸" << axis << "最終位置:" << actualPos
                                     << "(無目標位置數據)";
                        }
                    } else {
                        qDebug() << "軸" << axis << "無法獲取位置數據";
                    }
                }

                // 如果有軸未到達目標位置，再等待一段時間
                if (!allReachedTarget) {
                    qDebug() << "檢測到有軸未到達目標位置，再等待1000ms";

                    // 再等待1000ms，然後再次檢查
                    QTimer::singleShot(1000, this, [this]() {  // 增加到1000ms
                        qDebug() << "\n===== 再次檢查所有軸的位置 =====";

                        for (auto it = axisPVTCache.begin(); it != axisPVTCache.end(); ++it) {
                            short axis = it.key();
                            double actualPos;
                            long status;

                            // 檢查軸狀態
                            if (GTN_GetSts(cardCore, axis, &status) == 0) {
                                qDebug() << "軸" << axis << "狀態:" << QString("0x%1").arg(status, 0, 16);
                            }

                            if (GTN_GetPrfPos(cardCore, axis, &actualPos) == 0) {
                                qDebug() << "軸" << axis << "最終位置:" << actualPos;
                            }
                        }

                        // 嘗試重新使能問題軸
                        for (auto it = axisPVTCache.begin(); it != axisPVTCache.end(); ++it) {
                            short axis = it.key();
                            double actualPos, targetPos = 0;

                            if (!it.value().positions.isEmpty()) {
                                targetPos = it.value().positions.last();

                                if (GTN_GetPrfPos(cardCore, axis, &actualPos) == 0) {
                                    double posError = actualPos - targetPos;

                                    if (std::abs(posError) > 1000.0) {
                                        qDebug() << "嘗試重新使能軸" << axis;
                                        // 清除軸狀態
                                        GTN_ClrSts(cardCore, axis);
                                        QThread::msleep(100);

                                        // 重新使能
                                        if (enableAxis(axis)) {
                                            qDebug() << "軸" << axis << "重新使能成功";
                                        } else {
                                            qDebug() << "軸" << axis << "重新使能失敗";
                                        }
                                    }
                                }
                            }
                        }

                        // 無論如何，現在發出完成信號
            isExecutingCommand = false;
            // 停止監控並分析結果

            qDebug() << "發出命令序列完成信號 commandSequenceCompleted";

            emit commandSequenceCompleted();
            // 添加500ms延遲，確保命令序列完成信號處理完畢
            //qDebug() << "添加500ms延遲，確保命令序列完成信號處理完畢";
            //QThread::msleep(500);


                        // 處理待處理序列
            if (!pendingSequences.isEmpty()) {
                qDebug() << "發現還有" << pendingSequences.size() << "個序列等待執行，立即執行下一個";
                executeNextSequence();
            }
                    });
                } else {
                    // 所有軸都已到達目標位置，可以發出完成信號
                    isExecutingCommand = false;

                    qDebug() << "所有軸都已到達目標位置，發出命令序列完成信號";

                    //stopWatchMonitoring();
                    emit commandSequenceCompleted();



                    // 處理待處理序列
                    if (!pendingSequences.isEmpty()) {
                        qDebug() << "發現還有" << pendingSequences.size() << "個序列等待執行，立即執行下一個";
                        executeNextSequence();
                    }
                }
                });

            // 只在真正沒有更多序列時才設置延遲
            return;
        }
    });

    monitorTimer->start(5);  // 每5毫秒檢查一次，提高監控精度
}

/////////////////////////////////////////////////////////0312
//////////////PVT運動發出訊號後的接收函數 step5

bool ethercat::prepareNextCommand(short axis) {
    // 防止頻繁重複檢查（移到最前面）
    QDateTime currentTime = QDateTime::currentDateTime();
    if (lastAxisCheckTimes.contains(axis)) {
        qint64 msSinceLastCheck = lastAxisCheckTimes[axis].msecsTo(currentTime);
        if (msSinceLastCheck < 100) { // 至少間隔100毫秒
            return false; // 短時間內不重複檢查
        }
    }
    lastAxisCheckTimes[axis] = currentTime;

    // 防抖通過後再輸出日誌
    qDebug() << "\n===== [PVT斷點9-準備下一個命令開始] =====";
    qDebug() << "- 軸號：" << axis;
    qDebug() << "- axisCommandQueues是否為空：" << axisCommandQueues.isEmpty();
    qDebug() << "- 全局命令隊列大小：" << axisCommandQueues.size();
    qDebug() << "- 當前軸命令隊列長度：" << axisCommandQueues.value(axis).size();
    qDebug() << "- pendingSequences大小：" << pendingSequences.size();


    // 新增：確保軸的命令隊列已經初始化
    if (!axisCommandQueues.contains(axis)) {
        axisCommandQueues[axis] = QQueue<QString>();
    }

    // 如果當前軸命令隊列為空，先檢查是否有待處理序列
    if (axisCommandQueues[axis].isEmpty()) {
        // 嘗試從所有待處理序列中查找該軸的命令
        int checkedSequences = 0;
        while (!pendingSequences.isEmpty() && axisCommandQueues[axis].isEmpty() && checkedSequences < pendingSequences.size()) {
            // 取出但不移除第一個序列
            CommandSequence nextSequence = pendingSequences.first();
            bool foundCommand = false;

            // 檢查序列中是否有當前軸的命令
            for (const QString& cmd : nextSequence.rawCommands) {
                // 獲取命令中的軸名稱（如100W, 200W等）
                QString motorName = cmd.left(cmd.indexOf(":")).remove('"');
                // 檢查是否是當前軸的命令
                if (getAxisNumber(motorName) == axis) {
                    qDebug() << "在待處理序列中找到軸" << axis << "的命令: " << cmd;
                    foundCommand = true;

                    // 將完整命令添加到隊列
                    QString axisCmd = QString(cmd).remove('"').trimmed();
                    axisCommandQueues[axis].enqueue(axisCmd);
                }
            }

            // 如果在這個序列中找到了命令，將其移出待處理序列
            if (foundCommand) {
                pendingSequences.removeFirst();
                qDebug() << "從待處理序列中取出序列，剩餘" << pendingSequences.size() << "個序列";
            } else {
                // 如果沒找到，移到下一個序列
                checkedSequences++;
                qDebug() << "在當前序列中未找到軸" << axis << "的命令，檢查下一個序列";

                // 將當前序列移到隊列尾部，檢查下一個
                pendingSequences.append(pendingSequences.takeFirst());
            }
        }
    }

    // 再次檢查命令隊列是否有命令
    if (axisCommandQueues[axis].isEmpty()) {
        qDebug() << "軸" << axis << "沒有更多命令，無法準備下一個命令";
        return false;
    }

    // 從隊列中取出命令並處理
    QString command = axisCommandQueues[axis].dequeue();
    qDebug() << "準備軸" << axis << "的下一個命令:" << command;

    // 清除該軸的緩存
    if (axisPVTCache.contains(axis)) {
        PVTDataCache newCache;
        newCache.isDirty = true;
        newCache.isUploaded = false;
        axisPVTCache[axis] = newCache;
    } else {
        // 如果該軸沒有緩存，創建一個新的
        PVTDataCache newCache;
        newCache.isDirty = true;
        newCache.isUploaded = false;
        axisPVTCache[axis] = newCache;
    }

    // 解析命令到緩存
    parseCommandToCache(command);

    // 上傳到下一個表單
    short nextTableId = getNextTableId(axis);

    // 添加詳細的下一個表單ID信息
    if (axisTableIds.contains(axis)) {
        const TableIdPair& pair = axisTableIds[axis];
        qDebug() << "\n======== 準備下一個表單ID信息 ========";
        qDebug() << "軸" << axis << "準備下一個表單: 主表單ID=" << pair.primaryId << ", 次表單ID=" << pair.secondaryId;
        qDebug() << "即將上傳到的表單ID: " << nextTableId << " (是" << (nextTableId == pair.primaryId ? "主" : "次") << "表單)";
        qDebug() << "======== 準備下一個表單ID信息結束 ========\n";
    }

    qDebug() << "\n===== [PVT斷點9.8-上傳數據到下一個表單] =====";
    qDebug() << "- 軸號：" << axis;
    qDebug() << "- 表單ID：" << nextTableId;

    uploadPVTData(axis, nextTableId);

    // 標記該軸的命令已準備好
    axisCommandReady[axis] = true;

    qDebug() << "\n===== [PVT斷點9.9-設置命令就緒標誌] =====";
    qDebug() << "- 軸號：" << axis;
    qDebug() << "- 命令就緒狀態：已設置為就緒";
    qDebug() << "軸" << axis << "的下一個命令準備完成，設置axisCommandReady=true，表單ID=" << nextTableId;

    return true;
}

/////////////////////////////////////////////////////////////////////////////////////

/////////////////////////////////////////////////////////////////////////////////////




// 批量上傳PVT數據到控制卡  功能：將軸緩存中的PVT數據（時間、位置、速度）上傳到控制卡
//，並選擇對應的PVT表。
void ethercat::uploadPVTData(short axis, short tableId) {
    qDebug() << "\n===== [PVT斷點5-數據上傳開始] =====";
    qDebug() << "- 軸號：" << axis;
    qDebug() << "- 表單ID：" << tableId;
    qDebug() << "- 當前線程：" << QThread::currentThreadId();
    qDebug() << "- 軸命令就緒狀態：" << axisCommandReady[axis];
    qDebug() << "- PVT數據緩存狀態：" << (axisPVTCache.contains(axis) ? "存在" : "不存在");
    //axisCommandReady 是一個指示軸是否已經準備好下一段運動數據的狀態標誌，在確保PVT運動連續性和平滑性方面扮演著至關重要的角色。當您看到日誌中 軸命令就緒狀態：false 時，它表示該軸還沒有準備好下一個運動數據，可能是因為還沒到準備的時間點（進度<80%），或者是已經沒有更多命令可執行。


    // 1. 檢查緩存是否存在
    if (!axisPVTCache.contains(axis)) {
        qDebug() << "錯誤: 軸" << axis << "沒有PVT緩存數據";
        return;
    }

    // 打印表單ID的詳細信息
    if (axisTableIds.contains(axis)) {
        const TableIdPair& pair = axisTableIds[axis];
        qDebug() << "========== 表單ID詳細信息 ==========";
        qDebug() << "軸" << axis << "的表單ID對: 主表單=" << pair.primaryId << ", 次表單=" << pair.secondaryId;
        qDebug() << "目前使用的是: " << (pair.usingPrimary ? "主表單" : "次表單") << ", ID值=" << getCurrentTableId(axis);
        qDebug() << "下一個將使用的是: " << (!pair.usingPrimary ? "主表單" : "次表單") << ", ID值=" << getNextTableId(axis);
        qDebug() << "========== 表單ID詳細信息結束 ==========";
    }

    qDebug() << "\n===== [PVT斷點5.2-表單ID信息] =====";
    qDebug() << "- 軸號：" << axis;
    qDebug() << "- 當前表單ID：" << getCurrentTableId(axis);
    qDebug() << "- 下一個表單ID：" << getNextTableId(axis);
    qDebug() << "- 目標表單ID：" << tableId;

    // 2. 獲取緩存數據
    PVTDataCache& cache = axisPVTCache[axis];

    qDebug() << "\n===== [PVT斷點5.3-數據點檢查] =====";
    qDebug() << "- 軸號：" << axis;
    qDebug() << "- 數據點數量：" << cache.times.size();
    qDebug() << "- 數據點有效性：" << (cache.times.size() >= 2 && cache.times.size() % 2 == 0 ? "有效" : "無效");

    if (cache.times.size() < 2 || cache.times.size() % 2 != 0) {
        qDebug() << "錯誤: 軸" << axis << "的PVT數據點數量無效:" << cache.times.size();
        return;
    }

    // 如果未指定表單ID，使用當前表單ID
    if (tableId == -1) {
        tableId = getCurrentTableId(axis);
    }

    // 打印關鍵點信息
    qDebug() << "上傳PVT數據到軸" << axis << "，表單ID:" << tableId;
    if (axisTableIds.contains(axis)) {
        const TableIdPair& pair = axisTableIds[axis];
        bool isUsingCurrentTable = (tableId == getCurrentTableId(axis));
        bool isPrimary = (tableId == pair.primaryId);
        qDebug() << "此表單是: " << (isPrimary ? "主表單" : "次表單")
                 << ", 目前" << (isUsingCurrentTable ? "正在使用" : "未使用")
                 << ", 這是" << (isUsingCurrentTable ? "第一個" : "第二個") << "上傳的表單";
    }

    qDebug() << "\n===== [PVT斷點5.4-實際位置和速度] =====";

    // 使用廠商API獲取實際數據
    double prfPos, prfVel;
    GTN_GetPrfPos(cardCore, axis, &prfPos);
    GTN_GetPrfVel(cardCore, axis, &prfVel);
    qDebug() << "- 軸號：" << axis;
    qDebug() << "- 實際位置：" << prfPos;
    qDebug() << "- 實際速度：" << prfVel;
    qDebug() << "軸" << axis << "當前實際位置:" << prfPos << "實際速度:" << prfVel;

    // 為Complete描述方式準備數據
    QVector<double> timesCopy = cache.times;
    QVector<double> positionsCopy = cache.positions;

    // 為多項式係數分配空間 (會由GTN_PvtTableComplete計算)
    QVector<double> coeffA(cache.times.size(), 0.0);
    QVector<double> coeffB(cache.times.size(), 0.0);
    QVector<double> coeffC(cache.times.size(), 0.0);

    // 使用Complete描述方式中的起始和終端速度
    double startVelocity = cache.startVelocity;
    double endVelocity = cache.endVelocity;

    qDebug() << "\n===== [PVT斷點5.5-PVT數據準備] =====";
    qDebug() << "- 軸號：" << axis;
    qDebug() << "- 時間點數量：" << timesCopy.size();
    qDebug() << "- 位置點數量：" << positionsCopy.size();
    qDebug() << "- 起始速度：" << startVelocity;
    qDebug() << "- 終端速度：" << endVelocity;

    qDebug() << "計劃時間值:" << timesCopy;
    qDebug() << "計劃位置值:" << positionsCopy;
    qDebug() << "起始速度:" << startVelocity << "，終端速度:" << endVelocity;

    qDebug() << "使用表ID:" << tableId;

    qDebug() << "\n===== [PVT斷點5.6-調用PVT上傳API] =====";
    qDebug() << "- 軸號：" << axis;
    qDebug() << "- 表單ID：" << tableId;
    qDebug() << "- 數據點數量：" << cache.times.size();

    // 上傳PVT數據前清除時間統計
    GTN_ClearTime(cardCore, TIME_ELAPSE_PROFILE);
    qDebug() << "清除中斷執行時間統計";


    // 使用Complete描述方式上傳PVT數據
    int result = GTN_PvtTableComplete(cardCore, tableId, cache.times.size(),
                                    timesCopy.data(), positionsCopy.data(),
                                    coeffA.data(), coeffB.data(), coeffC.data(),
                                    startVelocity, endVelocity);

    qDebug() << "\n===== [PVT斷點5.7-PVT上傳結果] =====";
    qDebug() << "- 軸號：" << axis;
    qDebug() << "- 表單ID：" << tableId;
    qDebug() << "- 上傳結果：" << result;

    if (result != 0) {
        qDebug() << "錯誤: 軸" << axis << "下載PVT數據失敗，錯誤碼:" << result;
        qDebug() << "詳細信息:";
        qDebug() << "- 時間數據:" << timesCopy;
        qDebug() << "- 位置數據:" << positionsCopy;
        qDebug() << "- 起始速度:" << startVelocity;
        qDebug() << "- 終端速度:" << endVelocity;
        qDebug() << "- 係數A:" << coeffA;
        qDebug() << "- 係數B:" << coeffB;
        qDebug() << "- 係數C:" << coeffC;
        return;
    }

    // 上傳後獲取時間統計
    unsigned long currentTime, maxTime;
    GTN_GetTime(cardCore, TIME_ELAPSE_PROFILE, &currentTime, &maxTime);
    qDebug() << "軸" << axis << "上傳Table ID" << tableId
             << "中斷執行時間:" << currentTime
             << "最大值:" << maxTime;

    // 如果是當前表單ID，則選擇該表單
    if (tableId == getCurrentTableId(axis)) {
        qDebug() << "\n===== [PVT斷點5.8-選擇當前表單] =====";
        qDebug() << "- 軸號：" << axis;
        qDebug() << "- 表單ID：" << tableId;

        if (GTN_PvtTableSelect(cardCore, axis, tableId) != 0) {
            qDebug() << "錯誤: 軸" << axis << "選擇PVT表格失敗";
            return;
        }
        qDebug() << "- 選擇表單結果：成功";
    }

    qDebug() << "\n===== [PVT斷點5.9-數據上傳完成] =====";
    qDebug() << "- 軸號：" << axis;
    qDebug() << "- 表單ID：" << tableId;
    qDebug() << "- 上傳狀態：成功";

    qDebug() << "軸" << axis << "成功上傳數據到表單ID:" << tableId;
    qDebug() << "===== 上傳PVT數據完成 =====\n";
    cache.isUploaded = true;
}

/////// 解析命令並填充緩存   功能：解析單條PVT命令字符串，提取軸名稱、起點位置、
///////終點位置、起始時間、結束時間、速度等參數，並計算出PVT點，存入軸的緩存中。
void ethercat::parseCommandToCache(const QString& command) {
    qDebug() << "parseCommandToCache 開始處理命令: " << command;
    qDebug() << "當前緩存中的軸數量: " << axisPVTCache.size();

    // 從命令中提取軸名稱
    QString motorName = command.left(command.indexOf(":")).trimmed();
    short axis = getAxisNumber(motorName);  // 使用新函數，傳入軸名稱

    qDebug() << "解析命令: 軸名稱=" << motorName << " 映射到軸號=" << axis;

    // 修改正則表達式以匹配新的命令格式，包括可選的起始/終端速度
    // 格式: <軸名>:PA<起始位置>-<終點位置> TA<起始時間>-<終止時間> SPA<主速度>[/<起始速度>][/<終端速度>]
    QRegularExpression regex("\"?\\S+:PA(-?\\d+)-(-?\\d+)\\s+TA([\\d.]+)-([\\d.]+)\\s+SPA([\\d.]+)(?:/([\\d.]+))?(?:/([\\d.]+))?\"?");
    QRegularExpressionMatch match = regex.match(command);

    if (!match.hasMatch()) {
        qDebug() << "錯誤: PVT命令格式不匹配:" << command;
        return;
    }

    // 提取參數
    double startPos = match.captured(1).toDouble();
    double endPos = match.captured(2).toDouble();

    // 檢查是否有雙負號情況 (PA0--200000)
    if (command.contains("PA") && command.contains("--")) {
        // 如果命令中包含雙負號，確保endPos是負數
        endPos = -std::abs(endPos);
        qDebug() << "檢測到雙負號，將終點位置調整為:" << endPos;
    }

    // 檢查 PA-200000-0 這種情況，確保起點是負值
    if (command.contains("PA-") && !command.contains("--")) {
        // 確保startPos是負數
        startPos = -std::abs(startPos);
        qDebug() << "檢測到起點負值，確保起點位置為:" << startPos;
    }

    double startTime = match.captured(3).toDouble();
    double endTime = match.captured(4).toDouble();
    double speed = match.captured(5).toDouble();

    // 解析可選的起始和終端速度
    // 獲取運動方向
    int direction = (endPos > startPos) ? 1 : -1;

    // 設置起始速度 - 直接使用第一個數字作為起始速度
    double startVelocity = speed;
    // 如果需要，根據運動方向調整速度符號
    if ((startVelocity != 0) && ((startVelocity * direction) < 0)) {
        startVelocity = -startVelocity;
    }

    // 終端速度默認為0，與廠商示例一致
    double endVelocity = 0;

    // 檢查命令是否包含第二個速度參數(作為終端速度)
    if (match.captured(6).length() > 0) {
        endVelocity = match.captured(6).toDouble();

        // 如果需要，根據運動方向調整速度符號
        if ((endVelocity != 0) && ((endVelocity * direction) < 0)) {
            endVelocity = -endVelocity;
        }

        qDebug() << "設置終端速度:" << endVelocity;
    }

    // 忽略第三個參數(match.captured(7))，因為我們使用SPA<起始速度>/<終端速度>格式

    // 如果這個軸還沒有緩存，創建一個
    if (!axisPVTCache.contains(axis)) {
        PVTDataCache newCache;
        newCache.isDirty = true;
        newCache.isUploaded = false;

        // 新增：存儲起始和終端速度
        newCache.startVelocity = startVelocity;
        newCache.endVelocity = endVelocity;

        axisPVTCache[axis] = newCache;
    }

    PVTDataCache& cache = axisPVTCache[axis];

    // === 修改部分：不再使用calculatePVTPoints生成中間點 ===
    // 只存儲關鍵點（起點和終點）

    // 創建兩個關鍵點（起點和終點）
    double lastTime = cache.times.isEmpty() ? 0 : cache.times.last();

    // 添加起點
    cache.times.append(lastTime + startTime * 1000);  // 轉為毫秒
    cache.positions.append(startPos);

    // 添加終點
    cache.times.append(lastTime + endTime * 1000);  // 轉為毫秒
    cache.positions.append(endPos);

    // 存儲該段的起始和終端速度
    if (cache.times.size() <= 2) {  // 第一段運動
        cache.startVelocity = startVelocity;
    }
    cache.endVelocity = endVelocity;  // 更新為最新段的終端速度

    cache.isDirty = true;

    qDebug() << "命令解析成功，將2個關鍵點添加到軸" << axis << "的緩存";
    qDebug() << "起點位置:" << startPos << "，終點位置:" << endPos;
    qDebug() << "起始速度:" << startVelocity << "，終端速度:" << endVelocity;

    qDebug() << "處理完成後緩存中的軸數量: " << axisPVTCache.size();
}
////////////////////////////////////////////////////////////////////////////////////////////////
// 動態修改PVT點數據
void ethercat::modifyPVTPoint(short axis, int pointIndex, double newPosition, double newVelocity) {
    if (!axisPVTCache.contains(axis)) {
        qDebug() << "錯誤: 軸" << axis << "沒有PVT緩存";
        return;
    }

    PVTDataCache& cache = axisPVTCache[axis];

    if (pointIndex < 0 || pointIndex >= cache.positions.size()) {
        qDebug() << "錯誤: 點索引" << pointIndex << "超出範圍(0-" << (cache.positions.size()-1) << ")";
        return;
    }

    qDebug() << "修改軸" << axis << "的點" << pointIndex
             << "，位置: " << cache.positions[pointIndex] << " -> " << newPosition
             << "，速度: " << cache.velocities[pointIndex] << " -> " << newVelocity;

    // 修改緩存中的數據
    cache.positions[pointIndex] = newPosition;
    cache.velocities[pointIndex] = newVelocity;
    cache.isDirty = true;

    // 如果已經上傳過數據，則需要重新上傳
    if (cache.isUploaded) {
        uploadPVTData(axis);
    }
}



void ethercat::monitorPVTMotion() {
    // 獲取所有正在運動的軸
    QList<short> activeAxes;
    for (auto it = axisPVTCache.begin(); it != axisPVTCache.end(); ++it) {
        activeAxes.append(it.key());
    }

    if (activeAxes.isEmpty()) {
        qDebug() << "沒有軸在進行PVT運動";
        return;
    }

    // 創建監控定時器
    QTimer* monitorTimer = new QTimer(this);
    connect(monitorTimer, &QTimer::timeout, [this, activeAxes, monitorTimer]() {
        bool allCompleted = true;

        for (short axis : activeAxes) {
            short tableId;
            double t;
            double prfVel;
            double prfPos;
            long sts;

            // 獲取PVT運動狀態
            if (GTN_PvtStatus(cardCore, axis, &tableId, &t) == 0) {
                // 獲取規劃速度
                GTN_GetPrfVel(cardCore, axis, &prfVel);
                // 獲取規劃位置
                GTN_GetPrfPos(cardCore, axis, &prfPos);
                // 獲取軸狀態
                GTN_GetSts(cardCore, axis, &sts);
 ///////////////////////////////////////////////////////////////////////////////////
                // 獲取軸名稱
                QString axisName;
                if (axis >= 1 && axis <= 7) {
                    axisName = QString("%1W").arg(axis * 100);
                }

                qDebug() << QString("【實時監控】軸%1%2運動監控: 表ID=%3 時間=%4ms 實際速度=%5 實際位置=%6 狀態=0x%7")
                         .arg(axis)
                         .arg(axisName.isEmpty() ? "" : "(" + axisName + ")")
                         .arg(tableId)
                         .arg(t)
                         .arg(prfVel)
                         .arg(prfPos)
                         .arg(sts, 0, 16);

                // 檢查運動是否完成 (tableId < 0 表示完成)
                if (tableId < 0) {
                    qDebug() << QString("【實時監控】軸%1%2 PVT運動已完成")
                             .arg(axis)
                             .arg(axisName.isEmpty() ? "" : "(" + axisName + ")");
                } else {
                    allCompleted = false;
                }
            }
        }

        // 如果所有軸都完成了運動，停止監控
        if (allCompleted) {
            qDebug() << "【實時監控】所有PVT運動完成";
            monitorTimer->stop();
            monitorTimer->deleteLater();
        }
    });

    // 每50毫秒檢查一次，提高監控頻率
    monitorTimer->start(50);
    qDebug() << "【實時監控】開始監控PVT運動，每50ms更新一次";
}


// 功能：讀取並記錄指定軸的實時位置、速度和狀態
// axis: 軸號

void ethercat::logAxisRealTimeData(short axis) {
    double prfPos, prfVel;
    long sts;

    GTN_GetPrfPos(cardCore, axis, &prfPos);
    GTN_GetPrfVel(cardCore, axis, &prfVel);
    GTN_GetSts(cardCore, axis, &sts);

    qDebug() << "軸" << axis << "實時數據: 位置=" << prfPos
             << "速度=" << prfVel
             << "狀態=" << QString("0x%1").arg(sts, 0, 16);
}






// 獲取當前表單ID
short ethercat::getCurrentTableId(short axis) {
    if (!axisTableIds.contains(axis)) {
        qDebug() << "警告: 軸" << axis << "沒有表單ID對，使用軸號作為表單ID";
        return axis;
    }

    const TableIdPair& pair = axisTableIds[axis];
    return pair.usingPrimary ? pair.primaryId : pair.secondaryId;
}

////////////////////////////////////////////////////////////////////////////////////

// 獲取下一個表單ID
short ethercat::getNextTableId(short axis) {
    qDebug() << "\n===== [PVT斷點7-獲取下一個表單ID] =====";
    qDebug() << "- 軸號：" << axis;
    qDebug() << "- 表單映射狀態：" << (axisTableIds.contains(axis) ? "存在" : "不存在");

    if (!axisTableIds.contains(axis)) {
        qDebug() << "警告: 軸" << axis << "沒有表單ID對，使用軸號+10作為下一個表單ID";
        qDebug() << "- 返回默認下一個表單ID：" << (axis + 10);
        return axis + 10;
    }

    const TableIdPair& pair = axisTableIds[axis];
    short nextId = pair.usingPrimary ? pair.secondaryId : pair.primaryId;

    qDebug() << "- 當前使用：" << (pair.usingPrimary ? "主表單" : "次表單");
    qDebug() << "- 下一個將使用：" << (!pair.usingPrimary ? "主表單" : "次表單");
    qDebug() << "- 返回下一個表單ID：" << nextId;

    return nextId;
}

////////////////////////////////////////////////////////////////////////////////////

// 切換到下一個表單
void ethercat::switchToNextTable(short axis) {

    // 切換前清除時間統計
    GTN_ClearTime(cardCore, TIME_ELAPSE_PROFILE);
    qDebug() << "\n===== [PVT斷點8-切換到下一個表單] =====";
    qDebug() << "- 軸號：" << axis;
    qDebug() << "- 表單映射狀態：" << (axisTableIds.contains(axis) ? "存在" : "不存在");

    if (!axisTableIds.contains(axis)) {
        qDebug() << "警告: 軸" << axis << "沒有表單ID對，無法切換表單";
        return;
    }

    TableIdPair& pair = axisTableIds[axis];
    qDebug() << "- 切換前使用：" << (pair.usingPrimary ? "主表單" : "次表單");
    qDebug() << "- 切換前表單ID：" << getCurrentTableId(axis);

    pair.usingPrimary = !pair.usingPrimary;

    qDebug() << "- 切換後使用：" << (pair.usingPrimary ? "主表單" : "次表單");
    qDebug() << "- 切換後表單ID：" << getCurrentTableId(axis);

    // 切換後獲取時間統計
    unsigned long currentTime, maxTime;
    GTN_GetTime(cardCore, TIME_ELAPSE_PROFILE, &currentTime, &maxTime);
    short nextTable = getCurrentTableId(axis);  // 添加這行來聲明nextTable變數
    qDebug() << "軸" << axis << "切換到Table ID" << nextTable
             << "中斷執行時間:" << currentTime
             << "最大值:" << maxTime;
}

/////////////////////////////////////////////////////////////////////////////////////

// 添加命令到隊列
void ethercat::addCommandToQueue(short axis, const QString& command) {
    if (!axisCommandQueues.contains(axis)) {
        axisCommandQueues[axis] = QQueue<QString>();
    }

    axisCommandQueues[axis].enqueue(command);
    qDebug() << "將命令添加到軸" << axis << "的隊列，當前隊列長度:" << axisCommandQueues[axis].size();
}

// 解析命令到隊列
void ethercat::parseCommandsToQueues(const QStringList& commands) {
    // 清除現有隊列
    axisCommandQueues.clear();

    for (const QString& command : commands) {
        if (command.contains(",")) {
            // 多軸命令
            QStringList axisCommands = command.split(",", Qt::SkipEmptyParts);
            for (const QString& axisCmd : axisCommands) {
                QString trimmedCmd = axisCmd.trimmed();
                QString motorName = trimmedCmd.left(trimmedCmd.indexOf(":")).trimmed();
                short axis = getAxisNumber(motorName);

                addCommandToQueue(axis, trimmedCmd);
            }
        } else {
            // 單軸命令
            QString trimmedCmd = command.trimmed();
            QString motorName = trimmedCmd.left(trimmedCmd.indexOf(":")).trimmed();
            short axis = getAxisNumber(motorName);

            addCommandToQueue(axis, trimmedCmd);
        }
    }

    // 初始化命令準備狀態
    axisCommandReady.clear();
    for (auto it = axisCommandQueues.begin(); it != axisCommandQueues.end(); ++it) {
        axisCommandReady[it.key()] = false;
    }

    // 初始化命令計數
    totalAxisCommands.clear();
    executedAxisCommands.clear();
    for (auto it = axisCommandQueues.begin(); it != axisCommandQueues.end(); ++it) {
        totalAxisCommands[it.key()] = it.value().size();
        executedAxisCommands[it.key()] = 0;
        qDebug() << "軸" << it.key() << "總命令數:" << totalAxisCommands[it.key()];
    }
}


///////////////////專門的診斷函數//////////////////



void ethercat::checkCardPerformance() {
    // 檢查各類時間模組的執行時間
    unsigned long profileTime, profileTimeMax;
    unsigned long hostCmdTime, hostCmdTimeMax;
    unsigned long etherCmdTime, etherCmdTimeMax;
    unsigned long profileCalcTime, profileCalcTimeMax;

    GTN_GetTime(cardCore, TIME_ELAPSE_PROFILE, &profileTime, &profileTimeMax);
    GTN_GetTime(cardCore, TIME_ELAPSE_HOST_COMMAND_EXECUTE, &hostCmdTime, &hostCmdTimeMax);
    GTN_GetTime(cardCore, TIME_ELAPSE_ETHER_COMMAND_EXECUTE, &etherCmdTime, &etherCmdTimeMax);
    GTN_GetTime(cardCore, TIME_ELAPSE_PROFILE_CALCULATE, &profileCalcTime, &profileCalcTimeMax);

    qDebug() << "\n===== 控制卡性能診斷 =====";
    qDebug() << "中斷執行時間(PROFILE):" << profileTime << "μs, 最大:" << profileTimeMax << "μs";
    qDebug() << "主機命令執行時間:" << hostCmdTime << "μs, 最大:" << hostCmdTimeMax << "μs";
    qDebug() << "EtherCAT命令執行時間:" << etherCmdTime << "μs, 最大:" << etherCmdTimeMax << "μs";
    qDebug() << "運動規劃計算時間:" << profileCalcTime << "μs, 最大:" << profileCalcTimeMax << "μs";

    // 清除所有統計，開始新一輪監測
    GTN_ClearTime(cardCore, TIME_ELAPSE_PROFILE);
    GTN_ClearTime(cardCore, TIME_ELAPSE_HOST_COMMAND_EXECUTE);
    GTN_ClearTime(cardCore, TIME_ELAPSE_ETHER_COMMAND_EXECUTE);
    GTN_ClearTime(cardCore, TIME_ELAPSE_PROFILE_CALCULATE);
}



// 在文件末尾添加

///////////////////// Watch監測功能 /////////////////////

// 設置Watch監測
void ethercat::setupWatchMonitoring() {
    qDebug() << "\n===== 設置Watch監測 =====";

    // 步驟1：加載Watch配置文件
    short result = GTN_LoadWatchConfig(cardCore, "WATCH.ini");
    if (result == 0) {
        qDebug() << "Watch配置文件加載成功";
    } else {
        qDebug() << "Watch配置文件加載失敗，錯誤碼：" << result;
    }
}
// 開始Watch監測
void ethercat::startWatchMonitoring() {
    qDebug() << "\n===== 開始Watch監測 =====";

    // 步驟2：啟動Watch監測
    short result = GTN_WatchOn(cardCore, 0, 0, 0);
    if (result == 0) {
        isWatchMonitoringActive = true;
        qDebug() << "Watch監測已啟動，等待問題複現...";
    } else {
        qDebug() << "Watch監測啟動失敗，錯誤碼：" << result;
    }
}

// 停止Watch監測
void ethercat::stopWatchMonitoring() {
    qDebug() << "\n===== 停止Watch監測 =====";

    // 步驟3：停止Watch監測
    short result = GTN_WatchOff(cardCore);
    isWatchMonitoringActive = false;
    qDebug() << "Watch監測已停止";

    // 確保設備靜止
    qDebug() << "請確保所有軸都已停止運動...";
    QThread::msleep(1000);

    // 步驟4：保存Watch數據
    qDebug() << "開始將Watch數據寫入文件，此過程禁止任何操作...";
    result = GTN_PrintWatch(cardCore, "watch.txt", 0, 0);

    if (result == 0) {
        qDebug() << "Watch數據已成功保存到：watch.txt";
    } else {
        qDebug() << "Watch數據保存失敗，錯誤碼：" << result;
    }
}



/////////////////////////////////////SUPER_PT_MODE///////////////////////////////////



// 定義靜態成員變數
QMap<short, QThread*> ethercat::s_ptThreads;
QMap<short, QObject*> ethercat::s_ptObjects;
bool ethercat::s_ptThreadsInitialized = false;
QMutex ethercat::s_ptMutex;

// 添加以下靜態成員變數定義
QMap<QString, QStringList> ethercat::Super1_finaldata;
QMap<QString, QPair<int, QStringList>> ethercat::Super2_finaldata;
QMutex ethercat::s_commandMutex;
QTimer* ethercat::s_autoAnalysisTimer = nullptr;

// 添加這些靜態成員變量的定義
QMap<QString, QThread*> ethercat::s_axisThreads;
QMap<QString, QQueue<QString>> ethercat::s_axisCommandQueues;



void ethercat::SUPERPT_1(const QString& commandString) {
    qDebug() << "\n===== [SUPERPT_1] 開始處理特殊PT命令 =====";
    qDebug() << "- 收到的命令：" << commandString;

    // 解析收到的命令有多少行
    QStringList commandLines = commandString.split("\\\\");
    int lineCount = commandLines.size();
    qDebug() << "- 命令行數：" << lineCount;

    // 解析收到的命令總共有多少軸
    QSet<QString> axisSet;
    for (const QString &line : commandLines) {
        if (!line.isEmpty()) {
            QStringList parts = line.split(":");
            if (parts.size() >= 1) {
                QString axisCode = parts[0];
                if (!axisCode.isEmpty()) {
                    axisSet.insert(axisCode);
                }
            }
        }
    }
    int axisCount = axisSet.size();
    qDebug() << "- 命令軸數：" << axisCount << "，包含的軸：" << QStringList(axisSet.begin(), axisSet.end());

    // 統計當前命令中各軸的命令數量
    QMap<QString, int> axisCommandCount;
    for (const QString &line : commandLines) {
        if (!line.isEmpty()) {
            QStringList parts = line.split(":");
            if (parts.size() >= 1) {
                QString axisCode = parts[0];
                if (!axisCode.isEmpty()) {
                    axisCommandCount[axisCode]++;
                }
            }
        }
    }

    qDebug() << "- 各軸命令數量：";
    for (auto it = axisCommandCount.begin(); it != axisCommandCount.end(); ++it) {
        qDebug() << "  " << it.key() << "軸：" << it.value() << "條命令";
    }

    // 使用互斥鎖保護累積命令的操作
    QMutexLocker locker(&s_commandMutex);

    // 將當前命令累積到靜態變數中
    for (const QString &line : commandLines) {
        if (!line.isEmpty()) {
            QStringList parts = line.split(":");
            if (parts.size() >= 2) {
                QString axisCode = parts[0];
                QString commandDetail = parts[1];
                if (!axisCode.isEmpty()) {
                    Super1_finaldata[axisCode].append(commandDetail);
                }
            }
        }
    }

    // 顯示目前累積的命令數量
    qDebug() << "- 目前各軸累積命令數量：";
    for (auto it = Super1_finaldata.begin(); it != Super1_finaldata.end(); ++it) {
        qDebug() << "  " << it.key() << "軸累計：" << it.value().size() << "條命令";
    }

    // 重置計時器，在5秒後自動調用 SUPERPT_2
    if (s_autoAnalysisTimer == nullptr) {
        s_autoAnalysisTimer = new QTimer();
        s_autoAnalysisTimer->setSingleShot(true);
        connect(s_autoAnalysisTimer, &QTimer::timeout, this, &ethercat::SUPERPT_2);
    }

    // 如果計時器正在運行，停止它
    if (s_autoAnalysisTimer->isActive()) {
        s_autoAnalysisTimer->stop();
    }

    // 啟動計時器，設定2秒後自動調用 SUPERPT_2 進行最終分析
    qDebug() << "- 設置2秒後自動調用 SUPERPT_2 進行最終分析";
    s_autoAnalysisTimer->start(5000);
}
void ethercat::SUPERPT_2() {
    // 使用互斥鎖保護訪問
    QMutexLocker locker(&s_commandMutex);

    qDebug() << "\n===== [SUPERPT_2] 2秒累積後的最終命令分析 =====";

    // 先清空上次的分析結果
    Super2_finaldata.clear();

    // 將數據從 Super1_finaldata 轉移到 Super2_finaldata，保留原始數據
    for (auto it = Super1_finaldata.begin(); it != Super1_finaldata.end(); ++it) {
        QString axisCode = it.key();
        QStringList commands = it.value();

        // 存儲軸代碼、命令數量和命令列表
        Super2_finaldata[axisCode] = qMakePair(commands.size(), commands);
    }

    // 輸出每個軸累積的命令總數
    qDebug() << "- 各軸累積命令總數：";
    for (auto it = Super2_finaldata.begin(); it != Super2_finaldata.end(); ++it) {
        qDebug() << "  " << it.key() << "軸累計：" << it.value().first << "條命令";
    }

    // 輸出每個軸命令的詳情
    qDebug() << "\n- 各軸累積命令詳情：";
    for (auto it = Super2_finaldata.begin(); it != Super2_finaldata.end(); ++it) {
        qDebug() << "  " << it.key() << "軸的命令：";
        QStringList commandList = it.value().second;
        for (int i = 0; i < commandList.size(); ++i) {
            qDebug() << "    命令 " << (i+1) << ": " << commandList.at(i);
        }
    }

    // 清空 Super1_finaldata，為下一輪命令累積做準備
    Super1_finaldata.clear();

    // 釋放互斥鎖，避免死鎖
    locker.unlock();

    // 直接調用 SUPERPT_3 函數，為每個軸創建線程並排隊命令
    qDebug() << "- 分析完成，即將調用 SUPERPT_3 函數創建線程...";
    SUPERPT_3();
}

void ethercat::SUPERPT_3() {
    qDebug() << "\n===== [SUPERPT_3] 開始為每個軸創建線程和命令隊列 =====";

    // 使用互斥鎖保護訪問 Super2_finaldata
    QMutexLocker locker(&s_commandMutex);

    // 建立軸的映射: 100W對應1軸, 200W對應2軸, 以此類推
    QMap<QString, int> axisMapping;
    axisMapping["100W"] = 1;
    axisMapping["200W"] = 2;
    axisMapping["300W"] = 3;
    axisMapping["400W"] = 4;
    axisMapping["500W"] = 5;
    axisMapping["600W"] = 6;
    axisMapping["700W"] = 7;

    qDebug() << "- 建立軸映射關係：";
    for (auto it = axisMapping.begin(); it != axisMapping.end(); ++it) {
        qDebug() << "  " << it.key() << " 對應 " << it.value() << "軸";
    }

    // 先清理現有的線程
    for (auto it = s_axisThreads.begin(); it != s_axisThreads.end(); ++it) {
        if (it.value() && it.value()->isRunning()) {
            it.value()->quit();
            it.value()->wait();
            delete it.value();
        }
    }
    s_axisThreads.clear();
    s_axisCommandQueues.clear();

    int axisCount = Super2_finaldata.size();
    qDebug() << "- 檢測到需要創建" << axisCount << "個線程，對應" << axisCount << "個軸";

    // 為每個軸創建線程和命令隊列
    for (auto it = Super2_finaldata.begin(); it != Super2_finaldata.end(); ++it) {
        QString axisCode = it.key();
        QStringList commands = it.value().second;

        int axisNumber = axisMapping.contains(axisCode) ? axisMapping[axisCode] : 0;
        qDebug() << "- 為" << axisCode << "(" << axisNumber << "軸) 創建線程，共" << commands.size() << "條命令";

        // 創建該軸的命令隊列並添加命令
        QQueue<QString> commandQueue;
        for (const QString& cmd : commands) {
            commandQueue.enqueue(cmd);
        }

        // 存儲命令隊列
        s_axisCommandQueues[axisCode] = commandQueue;

        // 創建線程
        QThread* thread = new QThread();
        thread->setObjectName(axisCode + "_Thread");

        // 存儲線程
        s_axisThreads[axisCode] = thread;

        // 啟動線程
        thread->start();

        qDebug() << "- " << axisCode << "(" << axisNumber << "軸) 線程已創建並啟動";
    }

    qDebug() << "- 所有線程已創建，開始處理每個軸的命令";

    // 打印當前創建的所有線程和命令隊列狀態
    qDebug() << "\n- 線程和命令隊列統計：";
    for (auto it = s_axisThreads.begin(); it != s_axisThreads.end(); ++it) {
        QString axisCode = it.key();
        QThread* thread = it.value();
        QQueue<QString> queue = s_axisCommandQueues[axisCode];
        int axisNumber = axisMapping.contains(axisCode) ? axisMapping[axisCode] : 0;

        qDebug() << "  " << axisCode << "(" << axisNumber << "軸)：";
        qDebug() << "    - 線程ID：" << thread->currentThreadId();
        qDebug() << "    - 命令數量：" << queue.size() << "條";
        qDebug() << "    - 第一條命令：" << (queue.isEmpty() ? "無" : queue.head());
    }

    SUPERPT_4();
}


void ethercat::SUPERPT_4() {
    qDebug() << "\n===== [SUPERPT_4] 開始處理每個軸的命令 =====";
    short sRtn; // 聲明 sRtn 變數

    //////////////////// ===== 新增：從 0_pose.txt 檔案讀取後的絕對位置 =====///////////////
    // QCoreApplication::applicationDirPath() - 獲取應用程式執行檔所在目錄的路徑
    QString poseFilePath = QCoreApplication::applicationDirPath() + "/0_pose.txt";
    QMap<int, long> savedAxisPositions;  // 存儲從檔案讀取的後的絕對位置（鍵=軸號，值=位置）

    // QFile::QFile() - 創建檔案操作物件，用於讀取0_pose.txt檔案
    QFile poseFile(poseFilePath);
    // QFile::open() - 打開檔案，參數ReadOnly=只讀模式，Text=文字模式
    if (poseFile.open(QIODevice::ReadOnly | QIODevice::Text)) {
        // QTextStream::QTextStream() - 創建文字流物件，用於讀取檔案內容
        QTextStream inStream(&poseFile);
        // QTextStream::setEncoding() - 設置檔案編碼為UTF-8，確保中文字符正確讀取
        inStream.setEncoding(QStringConverter::Utf8);

        // QTextStream::atEnd() - 檢查是否到達檔案結尾
        while (!inStream.atEnd()) {
            // QTextStream::readLine() - 讀取一行文字內容
            // QString::trimmed() - 去除字符串前後的空白字符
            QString line = inStream.readLine().trimmed();
            // QString::isEmpty() - 檢查字符串是否為空
            // QString::startsWith() - 檢查字符串是否以#開頭（註釋行）
            if (line.isEmpty() || line.startsWith("#")) {
                continue;  // 跳過空行和註釋行
            }

            // 解析格式：axis_1=159256
            // QString::split() - 根據"="分割字符串，返回QStringList
            QStringList parts = line.split("=");
            if (parts.size() == 2) {
                // QString::trimmed() - 去除前後空白字符
                QString axisStr = parts[0].trimmed();
                // QString::startsWith() - 檢查是否以"axis_"開頭
                if (axisStr.startsWith("axis_")) {
                    bool ok;
                    // QString::mid() - 從第5個字符開始提取子字符串（跳過"axis_"）
                    // QString::toInt() - 將字符串轉換為整數，ok用於檢查轉換是否成功
                    int axisNum = axisStr.mid(5).toInt(&ok);  // 提取軸號
                    if (ok) {
                        // QString::toLong() - 將字符串轉換為長整數
                        long position = parts[1].trimmed().toLong(&ok);
                        if (ok) {
                            // QMap::operator[] - 將軸號和位置存入Map容器
                            savedAxisPositions[axisNum] = position;
                            qDebug() << QString("從檔案讀取軸 %1 歸零位置: %2").arg(axisNum).arg(position);
                        }
                    }
                }
            }
        }
        // QFile::close() - 關閉檔案，釋放系統資源
        poseFile.close();
        qDebug() << "成功從 0_pose.txt 讀取" << savedAxisPositions.size() << "個軸的歸零位置";
    } else {
        qDebug() << "警告: 無法讀取 0_pose.txt 檔案，將使用控制卡當前位置";
    }

    // 定義SPTPOINT結構體用於存儲點位數據
    struct SPTPOINT {
        int axisNumber;           // 軸編號
        double position;          // 位置
        long timeMs;              // 時間（毫秒）
        int segmentType;          // 段類型（普通/停止等）
    };

    // 創建一個向量來存儲所有點位
    std::vector<SPTPOINT> allPoints;

    // === 取樣步長與時間偏移設定 ===
    const long  DELTA_MS = 2;                         // 取樣間隔 2 ms（GTN 規定須 ≥2ms）
    const double DELTA   = static_cast<double>(DELTA_MS) / 1000.0; // 供 Ruckig 使用的秒值

    // 每軸已累積的時間偏移（毫秒），確保多段軌跡時間嚴格遞增
    QMap<int, long> axisTimeOffsetMs;                 // key = axisNumber

    // 遍歷所有軸的命令
    for (auto it = s_axisCommandQueues.begin(); it != s_axisCommandQueues.end(); ++it) {
        QString axisCode = it.key();
        QQueue<QString>& commandQueue = it.value();

        // 獲取軸號
        int axisNumber = 0;
        if (axisCode == "100W") axisNumber = 1;
        else if (axisCode == "200W") axisNumber = 2;
        else if (axisCode == "300W") axisNumber = 3;
        else if (axisCode == "400W") axisNumber = 4;
        else if (axisCode == "500W") axisNumber = 5;
        else if (axisCode == "600W") axisNumber = 6;
        else if (axisCode == "700W") axisNumber = 7;

        if (axisNumber == 0 || commandQueue.isEmpty()) {
            continue;
        }

        // ===== 【優先級邏輯】優先使用0_pose.txt檔案中的絕對位置 =====
        // 邏輯說明：
        // 第一優先：使用0_pose.txt檔案中保存的絕對位置（如果檔案中有該軸的數據）
        // 第二優先：如果檔案中沒有該軸數據，才從控制卡讀取當前位置
        // 目的：確保位置的一致性和可重複性
        double currentEncoderPos = 0;
        // QMap::contains() - 檢查Map容器中是否包含指定的軸號
        if (savedAxisPositions.contains(axisNumber)) {
            // 【第一優先】使用檔案中的絕對位置
            // QMap::operator[] - 從Map容器中獲取指定軸號對應的位置值
            currentEncoderPos = savedAxisPositions[axisNumber];
            qDebug() << QString("軸 %1 【優先使用】檔案中的絕對位置: %2").arg(axisNumber).arg(currentEncoderPos);
        } else {
            // 【第二優先】如果檔案中沒有該軸的數據，則從控制卡讀取
            // GTN_GetEncPos() - 固通函數：獲取指定軸的編碼器當前位置
            // 參數：cardCore=控制卡核心，axisNumber=軸號，&currentEncoderPos=位置變數地址，1=讀取數量，nullptr=不需要額外參數
            sRtn = GTN_GetEncPos(cardCore, axisNumber, &currentEncoderPos, 1, nullptr);
            if (sRtn != 0) {
                qDebug() << "  嚴重錯誤: 無法獲取軸 " << axisNumber << " 的當前位置，跳過此軸。錯誤碼:" << sRtn;
                continue;
            }
            qDebug() << QString("軸 %1 【備用方案】使用控制卡當前位置: %2").arg(axisNumber).arg(currentEncoderPos);
        }

        // 【新增】獲取目標位置並顯示運動前狀態
        double targetPosition = 0;
        // QQueue::QQueue() - 複製命令隊列，用於預覽不影響原隊列
        QQueue<QString> previewQueue = commandQueue;
        // QQueue::isEmpty() - 檢查隊列是否為空
        if (!previewQueue.isEmpty()) {
            // QQueue::head() - 獲取隊列第一個元素，但不移除
            QString firstCommand = previewQueue.head();
            // QString::operator+() - 字符串連接，在命令前加上"W:"前綴
            QString modifiedCommand = "W:" + firstCommand;
            // QRegularExpression::QRegularExpression() - 創建正則表達式對象，用於匹配Point格式
            // 正則表達式解析："Point起始位置-結束位置；"格式
            // 修改正則表達式以正確處理雙負號情況
            QRegularExpression pointReg("Point(-?\\d+\\.?\\d*)--?(-?\\d+\\.?\\d*)；");
            // QRegularExpression::match() - 在字符串中搜索匹配的模式
            QRegularExpressionMatch pointMatch = pointReg.match(modifiedCommand);
            // QRegularExpressionMatch::hasMatch() - 檢查是否找到匹配的模式
            if (pointMatch.hasMatch()) {
                // 文件中的歸零位置為絕對座標，Point 命令提供的是相對脈衝值
                // 因此最終目標位置 = 歸零絕對位置 + 相對脈衝
                double relativeOffset = pointMatch.captured(2).toDouble(); // 相對脈衝
                
                // 檢查是否有雙負號情況 (Point0--47317)
                if (modifiedCommand.contains("Point") && modifiedCommand.contains("--")) {
                    // 如果命令中包含雙負號，確保endPos是負數
                    relativeOffset = -std::abs(relativeOffset);
                    qDebug() << QString("  軸 %1 檢測到雙負號，將相對位移調整為: %2").arg(axisNumber).arg(relativeOffset);
                }
                
                targetPosition = currentEncoderPos + relativeOffset;       // 絕對目標位置
            }
        }

        qDebug() << QString("===== 軸 %1 運動前狀態 =====").arg(axisNumber);
        qDebug() << QString("  歸零位置（檔案）: %1").arg(currentEncoderPos);
        qDebug() << QString("  目標位置: %1").arg(targetPosition);
        qDebug() << QString("  運動距離: %1").arg(targetPosition - currentEncoderPos);
        qDebug() << "================================";

        // 【新增】運動計劃顯示：清楚顯示從哪裡開始運動到哪裡結束
        qDebug() << QString("🎯 軸 %1 運動計劃：從位置 %2 運動到位置 %3")
                    .arg(axisNumber)
                    .arg(currentEncoderPos)
                    .arg(targetPosition);
        qDebug() << QString("📏 軸 %1 預計運動距離：%2 個脈衝")
                    .arg(axisNumber)
                    .arg(targetPosition - currentEncoderPos);



        // 【重要】設置PrfPos為從0_pose.txt檔案讀取的歸零絕對位置
        // currentEncoderPos是從0_pose.txt檔案中讀取的數字（如：axis_1=159256）
        // 這是固通控制器PT運動的必要步驟：建立參考座標系
        // GTN_SetPrfPos() - 固通函數：設置指定軸的規劃位置（參考座標原點）
        // 參數：cardCore=控制卡核心，axisNumber=軸號，currentEncoderPos=從檔案讀取的絕對位置
        // 作用：告訴控制器"這個絕對位置就是我PT運動的參考原點"
        sRtn = GTN_SetPrfPos(cardCore, axisNumber, currentEncoderPos);
        if (sRtn != 0) {
            qDebug() << "  錯誤: 對齊軸" << axisNumber << " PrfPos 失敗，錯誤碼:" << sRtn;
        } else {
            qDebug() << "  [SYNC] 已將軸" << axisNumber << " 的 PrfPos 對齊到0_pose.txt中的歸零位置: " << currentEncoderPos;
        }

        // 3. 新增DEBUG：檢查當前狀態，不強制對齊
        double finalPrfPos = 0;
        double finalEncPos = 0;
        // GTN_GetPrfPos() - 固通函數：獲取指定軸的規劃位置（參考座標原點）
        // 參數：cardCore=控制卡核心，axisNumber=軸號，&finalPrfPos=規劃位置變數地址，1=讀取數量，nullptr=不需要額外參數
        // 作用：讀取控制器中當前設定的規劃位置，用於驗證GTN_SetPrfPos是否成功
        GTN_GetPrfPos(cardCore, axisNumber, &finalPrfPos, 1, nullptr);
        // GTN_GetEncPos() - 固通函數：獲取指定軸的編碼器當前位置
        // 參數：cardCore=控制卡核心，axisNumber=軸號，&finalEncPos=編碼器位置變數地址，1=讀取數量，nullptr=不需要額外參數
        // 作用：讀取馬達編碼器的實際位置，用於比較與規劃位置的差異
        GTN_GetEncPos(cardCore, axisNumber, &finalEncPos, 1, nullptr);
        long finalStatus = 0;
        // GTN_GetSts() - 固通函數：獲取指定軸的狀態信息
        // 參數：cardCore=控制卡核心，axisNumber=軸號，&finalStatus=狀態變數地址
        // 作用：讀取軸的運動狀態（運行/停止/報警等），用於診斷軸的工作狀態
        GTN_GetSts(cardCore, axisNumber, &finalStatus);
        qDebug() << QString("  [STATUS CHECK] EncPos=%1  PrfPos=%2  檔案歸零位置=%3  Status=0x%4")
                    .arg(finalEncPos)
                    .arg(finalPrfPos)
                    .arg(currentEncoderPos)
                    .arg(finalStatus, 0, 16);

        // ----------- 2. 收集所有段的時間-位置對，使用絕對位置 -----------

        //
        std::vector<double> timeVec;
        std::vector<double> posVec;
        double lastEndTime = -1;
        double lastEndPos = 0;
        QQueue<QString> tempQueue = commandQueue;
        while (!tempQueue.isEmpty()) {
            QString command = tempQueue.dequeue();
            QString modifiedCommand = "W:" + command;
            // 修改正則表達式以正確處理雙負號情況
            QRegularExpression pointReg("Point(-?\\d+\\.?\\d*)--?(-?\\d+\\.?\\d*)；");
            QRegularExpressionMatch pointMatch = pointReg.match(modifiedCommand);
            QRegularExpression timeReg("Time([\\d.]+)-([\\d.]+)");
            QRegularExpressionMatch timeMatch = timeReg.match(modifiedCommand);
            if (!pointMatch.hasMatch() || !timeMatch.hasMatch()) {
                qDebug() << "  警告：無法解析命令格式，跳過: " << command;
                continue;
            }
            double startPos = pointMatch.captured(1).toDouble();
            double endPos = pointMatch.captured(2).toDouble();
            // 檢查是否有雙負號情況
            if (modifiedCommand.contains("Point") && modifiedCommand.contains("--")) {
                endPos = -std::abs(endPos);
                qDebug() << QString("  軸 %1 命令解析：檢測到雙負號，終點位置調整為: %2").arg(axisNumber).arg(endPos);
            }
            double cmdStartTime = timeMatch.captured(1).toDouble();
            double cmdEndTime = timeMatch.captured(2).toDouble();
            // 避免重複加點
            if (timeVec.empty() || fabs(cmdStartTime - lastEndTime) > 1e-6) {
                timeVec.push_back(cmdStartTime);
                posVec.push_back(startPos);
            }
            timeVec.push_back(cmdEndTime);
            posVec.push_back(endPos);
            lastEndTime = cmdEndTime;
            lastEndPos = endPos;
        }
        // ----------- 使用 Ruckig 生成平滑軌跡 -----------

////////////////////////////////////////////////////////////////////////////////////
#if 0
        if (timeVec.size() >= 2) {
            // 1. 初始化 Ruckig
            ruckig::Ruckig<1> otg {DELTA};
            ruckig::InputParameter<1> input;
            ruckig::OutputParameter<1> output;

            // 2. 設置運動參數
            input.current_position = {posVec.front()};
            input.current_velocity = {0.0};
            input.current_acceleration = {0.0};
            input.target_position = {posVec.back()};
            input.target_velocity = {0.0};
            input.target_acceleration = {0.0};

            // 恢復舊版極限
            input.max_velocity = {50000.0};
            input.max_acceleration = {200000.0};
            input.max_jerk = {1000000.0};

            qDebug() << "  [Ruckig] 軸" << axisNumber << "準備生成軌跡: 從" << posVec.front() << "到" << posVec.back();
            qDebug() << "  [Ruckig] 運動極限: V_max=" << input.max_velocity[0]
                     << ", A_max=" << input.max_acceleration[0]
                     << ", J_max=" << input.max_jerk[0];

            // 3. 生成軌跡
            ruckig::Trajectory<1> trajectory;
            ruckig::Result result = otg.calculate(input, trajectory);

            if (result == ruckig::Result::ErrorInvalidInput) {
                qDebug() << "  [Ruckig] 錯誤: 輸入參數無效!";
                continue;
            }

            qDebug() << "  [Ruckig] 軌跡生成成功，預計耗時: " << trajectory.get_duration() << "s";

            // 新增：如果 Ruckig duration > 用戶時間，提高極限重算
            double totalTime = (timeVec.back() - timeVec.front());  // 移除 /1000.0，直接當秒
            double specifiedDuration = totalTime;
            if (trajectory.get_duration() > specifiedDuration) {
                input.max_velocity[0] *= 1.5;  // 提高以適應用戶短時間
                input.max_acceleration[0] *= 1.5;
                result = otg.calculate(input, trajectory);
                qDebug() << "  調整極限適應用戶時間: 新 duration=" << trajectory.get_duration();
            }

            // 新增警告如果時間太短
            if (specifiedDuration < 1.0) {
                qDebug() << "  警告: 用戶時間 " << specifiedDuration << "s 太短，可能暴沖！建議增加時間。";
            }

            // 4. 以用戶時間生成固定步數點位
            long durationMs = static_cast<long>(specifiedDuration * 1000.0);  // 用戶時間轉 ms
            long currentOffsetMs = axisTimeOffsetMs.value(axisNumber, 0L);


            // 固通 PT 要求第一個點的時間不能為 0，因此從 DELTA_MS (2 ms) 開始取樣
        for (long ms = DELTA_MS; ms < durationMs; ms += DELTA_MS) {
                double normalizedTime = (static_cast<double>(ms) / durationMs) * trajectory.get_duration();  // 映射逐步分配
                double new_position, new_velocity, new_acceleration;
                trajectory.at_time(normalizedTime, new_position, new_velocity, new_acceleration);

                SPTPOINT pt;
                pt.axisNumber = axisNumber;
                pt.position = new_position;
                pt.timeMs = currentOffsetMs + ms;
                pt.segmentType = PT_SEGMENT_NORMAL;
                allPoints.push_back(pt);
            }

            // 最後補終點
            SPTPOINT final_pt;
            final_pt.axisNumber = axisNumber;
            final_pt.position = posVec.back();
            final_pt.timeMs = currentOffsetMs + durationMs;
            final_pt.segmentType = PT_SEGMENT_NORMAL;
            allPoints.push_back(final_pt);

            // 更新偏移
            axisTimeOffsetMs[axisNumber] = currentOffsetMs + durationMs + DELTA_MS;
        }

#endif

////////////////////////////////////
        if (timeVec.size() >= 2) {
            // -------- 逐段用 Ruckig 生成軌跡 --------
            ruckig::Ruckig<1> otg{DELTA};
            ruckig::InputParameter<1> input;

            double cumulativeTime = 0.0;          // 這一軸已累積的時間 (s)
            long   offsetMs       = axisTimeOffsetMs.value(axisNumber, 0L); // 不同軸的起始偏移

            

            // 2) 逐段生成
            for (size_t seg = 1; seg < posVec.size(); ++seg) {
                // ---- 2-1. 給 Ruckig 當前段的端點 ----
                input.current_position      = { posVec[seg-1] };
                input.current_velocity      = { 0.0 };
                input.current_acceleration  = { 0.0 };
                input.target_position       = { posVec[seg] };
                input.target_velocity       = { 0.0 };
                input.target_acceleration   = { 0.0 };

                // 根據運動距離動態調整運動參數
                double distance = std::abs(posVec[seg] - posVec[seg-1]);
                double segmentTime = timeVec[seg] - timeVec[seg-1];

                // 計算所需的最小速度
                double minRequiredVel = distance / segmentTime * 1.5; // 1.5倍安全係數
                double maxVel = std::max(50000.0, minRequiredVel);
                double maxAcc = std::max(200000.0, maxVel * 2.0);
                double maxJerk = std::max(1000000.0, maxAcc * 5.0);

                input.max_velocity          = { maxVel };
                input.max_acceleration      = { maxAcc };
                input.max_jerk              = { maxJerk };

                // 設定最小持續時間以確保軌跡可行
                input.minimum_duration = segmentTime * 0.8; // 80% 的用戶指定時間

                qDebug() << "  [Ruckig] 軸" << axisNumber << " 段 " << seg
                         << " 參數: 距離=" << distance << " 時間=" << segmentTime
                         << " V_max=" << maxVel << " A_max=" << maxAcc << " J_max=" << maxJerk;

                ruckig::Trajectory<1> segTraj;
                ruckig::Result result = otg.calculate(input, segTraj);

                if (result != ruckig::Result::Finished) {
                    qDebug() << "  [Ruckig] 軸" << axisNumber << " 段 " << seg
                            << " 計算失敗，錯誤代碼:" << static_cast<int>(result);

                    // 嘗試降低要求重新計算
                    input.minimum_duration = 0.0; // 移除最小時間限制
                    input.max_velocity[0] *= 2.0;
                    input.max_acceleration[0] *= 2.0;
                    input.max_jerk[0] *= 2.0;

                    result = otg.calculate(input, segTraj);
                    if (result != ruckig::Result::Finished) {
                        qDebug() << "  [Ruckig] 軸" << axisNumber << " 段 " << seg
                                << " 重試後仍失敗，跳過此段";
                        continue;
                    } else {
                        qDebug() << "  [Ruckig] 軸" << axisNumber << " 段 " << seg
                                << " 重試成功";
                    }
                }

                // ===== 新增：列印此段軌跡時間 =====
                qDebug() << QString("  [Ruckig] 軸 %1 段 %2 duration = %3 s")
                            .arg(axisNumber).arg(seg).arg(segTraj.get_duration());

                // ---- 2-2. 以 2 ms 取樣這段 ----
                long segDurMs = static_cast<long>(segTraj.get_duration() * 1000.0);
                for (long ms = DELTA_MS; ms <= segDurMs; ms += DELTA_MS) {
                    double  t = ms / 1000.0;   // 秒
                    double  p, v, a;
                    segTraj.at_time(t, p, v, a);

                    SPTPOINT pt;
                    pt.axisNumber  = axisNumber;
                    pt.position    = p;
                    pt.timeMs      = offsetMs + static_cast<long>(cumulativeTime*1000) + ms;
                    pt.segmentType = PT_SEGMENT_NORMAL;
                    allPoints.push_back(pt);
                }

                cumulativeTime += segTraj.get_duration();   // 秒
            }

            // 3) 將此軸累積時間回寫給下一軸偏移
            axisTimeOffsetMs[axisNumber] = offsetMs + static_cast<long>(cumulativeTime*1000) + DELTA_MS;
        }





///////////////////////////////////////////////////////////////////////////////////////////

        // ----------- Ruckig 軌跡生成結束 -----------
        // ----------- 保留終止點邏輯 -----------
        if (!allPoints.empty()) {
            auto lastPointIt = std::find_if(allPoints.rbegin(), allPoints.rend(),
                [axisNumber](const SPTPOINT& p) { return p.axisNumber == axisNumber; });
            if (lastPointIt != allPoints.rend()) {
                double lastPosition = lastPointIt->position;          // 相對脈衝
                double lastTime = lastPointIt->timeMs / 1000.0 + 0.1;

                // 僅修改顯示：終止點對應的「絕對位置」= 起點絕對座標 + 相對脈衝
                double absoluteStartPos = currentEncoderPos;          // 該軸運動前的絕對起點
                double absoluteStopPos  = absoluteStartPos + lastPosition;

                SPTPOINT stopPoint;
                stopPoint.axisNumber = axisNumber;
                stopPoint.position = lastPosition;  // 保持相對脈衝，避免影響 PT 曲線
                stopPoint.timeMs = static_cast<long>(lastTime * 1000);
                stopPoint.segmentType = PT_SEGMENT_STOP;
                allPoints.push_back(stopPoint);

                qDebug() << "  為軸" << axisNumber
                         << "添加終止點: 相對位置=" << lastPosition
                         << ", 絕對位置=" << absoluteStopPos
                         << ", 時間=" << lastTime * 1000 << "ms";
            }
        }
    }

    // 按時間排序所有點
    std::sort(allPoints.begin(), allPoints.end(), [](const SPTPOINT& a, const SPTPOINT& b) {
        return a.timeMs < b.timeMs;
    });


    qDebug() << "生成了" << allPoints.size() << "個PT點";

    // 確保有點位可處理
    if (allPoints.empty()) {
        qDebug() << "沒有可用的PT點，退出處理";
        return;
    }

    // 輸出點位詳細信息
    qDebug() << "========== 所有PT點詳細信息 ==========";
    std::map<int, std::vector<SPTPOINT>> pointsByAxis;
    for (const auto& point : allPoints) {
        pointsByAxis[point.axisNumber].push_back(point);
    }

    for (const auto& axisPair : pointsByAxis) {
        int axisNumber = axisPair.first;
        const std::vector<SPTPOINT>& axisPoints = axisPair.second;

        qDebug() << "--- 軸" << axisNumber << "的點 (共" << axisPoints.size() << "個) ---";

        for (size_t i = 0; i < axisPoints.size(); i++) {
            const SPTPOINT& point = axisPoints[i];
            QString pointType = (point.segmentType == PT_SEGMENT_STOP) ? "終止點" : "普通點";

            //qDebug() << "  點" << (i+1) << ": 位置=" << point.position
                  //   << ", 時間=" << point.timeMs << "ms"
                   //  << ", 類型=" << pointType;
        }
    }
    qDebug() << "======================================";



    // 設置各軸為PT模式並準備運動
    QMap<int, bool> axisReady;  // 記錄軸是否已準備好
    for (auto const& [axisNumber, axisPoints] : pointsByAxis) {

        // 檢查軸狀態
        long status;
        short sRtn = GTN_GetSts(cardCore, axisNumber, &status);
        if (sRtn != 0) {
            qDebug() << "  錯誤: 獲取軸" << axisNumber << "狀態失敗，錯誤碼:" << sRtn;
            continue;
        }

        // 清除軸錯誤
        sRtn = GTN_ClrSts(cardCore, axisNumber);
        if (sRtn != 0) {
            qDebug() << "  錯誤: 清除軸" << axisNumber << "狀態失敗，錯誤碼:" << sRtn;
            continue;
        }
        Sleep(100);

        // 確保軸已使能
        if (!(status & 0x200)) {
            sRtn = GTN_AxisOn(cardCore, axisNumber);
            if (sRtn != 0) {
                qDebug() << "  錯誤: 啟動軸" << axisNumber << "失敗，錯誤碼:" << sRtn;
                continue;
            }
            qDebug() << "  軸" << axisNumber << "已啟動";
            Sleep(100);
        }



           // --- 方案B：不進行FINAL SYNC，保持當前PrfPos不變 ---
   // 註釋掉FINAL SYNC，避免雙重偏移問題
   // double syncPosition = 0;
   // if (savedAxisPositions.contains(axisNumber)) {
   //     syncPosition = savedAxisPositions[axisNumber];
   //     GTN_SetPrfPos(cardCore, axisNumber, syncPosition);
   //     qDebug() << QString("  [FINAL SYNC] 軸 %1 使用檔案歸零位置 %2 進行最終同步")
   //                 .arg(axisNumber).arg(syncPosition);
   // } else {
   //     // 如果檔案中沒有數據，則讀取當前編碼器位置
   //     if (GTN_GetEncPos(cardCore, axisNumber, &syncPosition, 1, nullptr) == 0) {
   //         GTN_SetPrfPos(cardCore, axisNumber, syncPosition);
   //         qDebug() << QString("  [FINAL SYNC] 軸 %1 使用當前編碼器位置 %2 進行最終同步")
   //                     .arg(axisNumber).arg(syncPosition);
   //     } else {
   //         qDebug() << "  警告: 讀取軸" << axisNumber << " 編碼器位置失敗，仍嘗試進 PT";
   //     }
   // }
   qDebug() << QString("  [方案B] 軸 %1 保持當前PrfPos不變，PT使用絕對位置").arg(axisNumber);
   // --- END 方案B ---



        // 設置為PT模式
        sRtn = GTN_PrfPt(cardCore, axisNumber, PT_MODE_DYNAMIC);
        if (sRtn != 0) {
            qDebug() << "  錯誤: 設置軸" << axisNumber << "為PT模式失敗，錯誤碼:" << sRtn;
            continue;
        }

        qDebug() << "  軸" << axisNumber << "設置為PT模式成功";



        // 設置FIFO大小為1024段
        sRtn = GTN_SetPtMemory(cardCore, axisNumber, 1);  // 0=32段, 1=1024段
        if (sRtn != 0) {
            qDebug() << "  錯誤: 設置軸" << axisNumber << "的FIFO大小失敗，錯誤碼:" << sRtn;
            continue;
        }

        // 清空PT FIFO
        sRtn = GTN_PtClear(cardCore, axisNumber);
        if (sRtn != 0) {
            qDebug() << "  錯誤: 清空軸" << axisNumber << "的PT FIFO失敗，錯誤碼:" << sRtn;
            continue;
        }

        axisReady[axisNumber] = true;  // 標記軸已準備好
    }






    // 首批上傳點位數量
    const int FIRST_BATCH_SIZE = 200;  // 大幅增加首批點數，確保有充足的初始數據

    // 為每個軸單獨維護時間基準
    QMap<int, double> axisLastTimes;  // 記錄每個軸的最後時間點

    // 上傳首批點位
    qDebug() << "===== 第一階段：上傳首批點位 =====";

    int currentIndex = 0;
    // 遍歷每個軸，分別上傳首批點位
    for (auto it = pointsByAxis.begin(); it != pointsByAxis.end(); ++it) {
        int axisNumber = it->first;
        const std::vector<SPTPOINT>& axisPoints = it->second;

        if (!axisReady[axisNumber]) {
            qDebug() << "  軸" << axisNumber << "未準備好，跳過";
            continue;
        }

        int pointsToUpload = std::min(FIRST_BATCH_SIZE, (int)axisPoints.size());
        qDebug() << "  為軸" << axisNumber << "上傳首批" << pointsToUpload << "個點";

        for (int i = 0; i < pointsToUpload; i++) {
            const SPTPOINT& point = axisPoints[i];

            // 保持原始時間，不做修改
            short sRtn = GTN_PtData(cardCore, axisNumber, point.position, point.timeMs, point.segmentType);
            if (sRtn == 0) {
                //qDebug() << "    成功上傳: 位置=" << point.position
                       //  << ", 時間=" << point.timeMs
                       //  << ", 類型=" << (point.segmentType == PT_SEGMENT_STOP ? "終止點" : "普通點");

                // 更新該軸的最後時間
                axisLastTimes[axisNumber] = point.timeMs / 1000.0;
            } else {
                qDebug() << "    錯誤: 上傳點位失敗，錯誤碼:" << sRtn;
            }
        }

        currentIndex += pointsToUpload;  // 更新全局索引
    }

    // 同時啟動所有已準備好的軸
    short allAxisMask = 0;
    for (auto it = axisReady.begin(); it != axisReady.end(); ++it) {
        if (it.value()) {
            int axis = it.key();
            allAxisMask |= (1 << (axis - 1));
        }
    }

    qDebug() << "啟動所有軸，掩碼:" << allAxisMask;
    short startRtn = GTN_PtStart(cardCore, allAxisMask);
    if (startRtn == 0) {
        qDebug() << "成功: 所有軸同時啟動";
    } else {
        qDebug() << "錯誤: 啟動所有軸失敗，錯誤碼:" << startRtn;
        return;  // 啟動失敗，退出
    }

    // 上傳剩餘點位並監控運動狀態
    qDebug() << "===== 第二階段：上傳剩餘點位並監控運動 =====";

    // 記錄每個軸已完成上傳的點數
    QMap<int, int> axisUploadedCount;
    for (auto it = pointsByAxis.begin(); it != pointsByAxis.end(); ++it) {
        axisUploadedCount[it->first] = std::min(FIRST_BATCH_SIZE, (int)it->second.size());
    }

    // 記錄每個軸是否已上傳完所有點
    QMap<int, bool> axisUploadComplete;
    for (auto it = pointsByAxis.begin(); it != pointsByAxis.end(); ++it) {
        axisUploadComplete[it->first] = (axisUploadedCount[it->first] >= it->second.size());
    }

    // 記錄運動是否開始減速
    QMap<int, bool> axisDecelerating;

    // 創建超時計時器
    QElapsedTimer watchdog;
    watchdog.start();

    // 計算所有運動的最大時間
    double maxMotionTime = 0;
    for (const auto& point : allPoints) {
        maxMotionTime = std::max(maxMotionTime, point.timeMs / 1000.0);
    }

    // 設置超時時間為最大運動時間的2倍，再加上緩衝
    const int MAX_WAIT_TIME_MS = static_cast<int>((maxMotionTime * 2 + 30) * 1000);

    ////////////////////////

    // 監控循環
    while (true) {
        bool allComplete = true;
        // 新增錯誤計數器
        QMap<int, int> axisErrorCount;
    
        // 新增：檢查所有軸是否有剩餘點，等待同步
        bool hasRemaining = false;
        for (auto it = pointsByAxis.begin(); it != pointsByAxis.end(); ++it) {
            int axis = it->first;
            if (axisUploadedCount[axis] < it->second.size()) {
                hasRemaining = true;
                break;
            }
        }
        if (hasRemaining) {
            Sleep(0);  // 0ms 直接繼續，提高頻率
        } else {
            Sleep(1);  // 只在無剩餘時休眠
        }
    
        for (auto it = pointsByAxis.begin(); it != pointsByAxis.end(); ++it) {
            int axisNumber = it->first;
            const std::vector<SPTPOINT>& axisPoints = it->second;
    
            if (!axisReady[axisNumber]) {
                continue;
            }
    
            // 檢查軸狀態
            long status;
            if (GTN_GetSts(cardCore, axisNumber, &status) != 0) {
                qDebug() << "  無法獲取軸" << axisNumber << "狀態";
                continue;
            }
    
            // 檢查FIFO空間，及時補點
            short space;
            if (GTN_PtSpace(cardCore, axisNumber, &space) == 0) {
                int remainingPoints = axisPoints.size() - axisUploadedCount[axisNumber];
                int pointsToUpload = std::min(200, remainingPoints);
                while (space > 100 && pointsToUpload > 0) {  // 增大閾值
                    int pointIndex = axisUploadedCount[axisNumber];
                    const SPTPOINT& point = axisPoints[pointIndex];
                    short sRtn = GTN_PtData(cardCore, axisNumber, point.position, point.timeMs, point.segmentType);
                    if (sRtn == 0) {
                        axisUploadedCount[axisNumber]++;
                        space--;
                        pointsToUpload--;
                    } else {
                        qDebug() << "    錯誤: 上傳點位失敗，錯誤碼:" << sRtn;
                        axisErrorCount[axisNumber]++;
                        if (axisErrorCount[axisNumber] > 10) {
                            qDebug() << "過多失敗，強制停止軸" << axisNumber;
                            GTN_Stop(cardCore, 1 << (axisNumber-1), 0);  // 添加 option=0
                            break;
                        }
                    }
                }
                if (axisUploadedCount[axisNumber] >= axisPoints.size()) {
                    axisUploadComplete[axisNumber] = true;
                }
            }
    
            bool isStopped = !(status & 0x400);  // 正確停止檢查
            if (!(axisUploadComplete[axisNumber] && isStopped)) {
                allComplete = false;
            }
        }
    
        if (allComplete) {
            qDebug() << "所有軸的運動已完成";
            qDebug() << "===== 🎯 運動完成！各軸實際運動情況報告 =====";
            for (auto const& [axisNumber, axisPoints] : pointsByAxis) {
                if (axisReady.contains(axisNumber) && axisReady[axisNumber]) {
                    double finalPrfPos = 0;
                    double finalEncPos = 0;
                    GTN_GetPrfPos(cardCore, axisNumber, &finalPrfPos, 1, nullptr);
                    GTN_GetEncPos(cardCore, axisNumber, &finalEncPos, 1, nullptr);
    
                    double startPosition = 0;
                    if (savedAxisPositions.contains(axisNumber)) {
                        startPosition = savedAxisPositions[axisNumber];
                    }
    
                    double plannedEndPosition = 0;
                    if (!axisPoints.empty()) {
                        for (auto it = axisPoints.rbegin(); it != axisPoints.rend(); ++it) {
                            if (it->segmentType != PT_SEGMENT_STOP) {
                                plannedEndPosition = it->position;
                                break;
                            }
                        }
                    }
    
                    double plannedEndAbs = startPosition + plannedEndPosition;
    
                    long actualAbsPos = 0;
                    GTN_GetEcatEncPos(cardCore, axisNumber, &actualAbsPos);
    
                    double actualDistance = static_cast<double>(actualAbsPos) - startPosition;
                    double plannedDistance = plannedEndAbs - startPosition;
                    double positionError = static_cast<double>(actualAbsPos) - plannedEndAbs;
    
                    qDebug() << QString("✅ 軸 %1 運動報告：").arg(axisNumber);
                    qDebug() << QString("   📍 從位置：%1").arg(startPosition);
                    qDebug() << QString("   🎯 計劃到位置：%1").arg(plannedEndAbs);
                    qDebug() << QString("   📍 實際到位置：%1").arg(actualAbsPos);
                    qDebug() << QString("   📏 計劃運動距離：%1 個脈衝").arg(plannedDistance);
                    qDebug() << QString("   📏 實際運動距離：%1 個脈衝").arg(actualDistance);
                    qDebug() << QString("   ⚠️  位置誤差：%1 個脈衝").arg(positionError);
                    qDebug() << "   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━";
                }
            }
            qDebug() << "================================================";
    
            break;
        }
    
        // 檢查超時
        if (watchdog.elapsed() > MAX_WAIT_TIME_MS) {
            qDebug() << "警告: 運動監控超時，但運動可能尚未完成";
            // 記錄當前狀態
            for (auto it = pointsByAxis.begin(); it != pointsByAxis.end(); ++it) {
                int axisNumber = it->first;
                long status;
                if (GTN_GetSts(cardCore, axisNumber, &status) == 0) {
                    qDebug() << "  軸" << axisNumber << "狀態:" << status;
                    // 輸出進度信息
                    double totalPoints = it->second.size();
                    double uploadedPoints = axisUploadedCount[axisNumber];
                    double progress = (uploadedPoints / totalPoints) * 100;
                    //qDebug() << "  軸" << axisNumber << "進度:" << progress << "%";
                }
            }
            GTN_Stop(cardCore, allAxisMask, 0);  // 添加 option=0
            qDebug() << "強制停止所有軸";
            break;
        }
    
        Sleep(1);  // 與 DELTA_MS 相同，確保能及時補充數據
    }

    qDebug() << "===== [SUPERPT_4] 處理完成 =====";
}










///////////////////////////pushButton_go_0pushButton_go_0////////////////////////////

// ===== 軸目標位置設定區域 =====
// 在這裡設置各軸的目標位置，如果不設置則默認為0（歸零）
void ethercat::initializeAxisTargetPositions()
{
    // 設置各軸的目標位置，取消註釋並修改數值來自定義：

    setAxisTargetPosition(1, 38278);      // 軸1目標位置：0脈衝（歸零）
    setAxisTargetPosition(2, 161088);      // 軸2目標位置：0脈衝（歸零）
    setAxisTargetPosition(3, 224203);      // 軸3目標位置：0脈衝（歸零）
    setAxisTargetPosition(4, -511632);      // 軸4目標位置：0脈衝（歸零）
    setAxisTargetPosition(5, 107669);      // 軸5目標位置：0脈衝（歸零）
    setAxisTargetPosition(6, 58098);      // 軸6目標位置：0脈衝（歸零）
    setAxisTargetPosition(7, -50000);     // 軸7目標位置：50000脈衝

    qDebug() << "===== 軸目標位置初始化完成 =====";
}

// pushButton_go_0 按鈕槽函數 - 讓所有軸歸零到絕對位置
void ethercat::on_pushButton_go_0_clicked()
{
    qDebug() << "===== [軸運動] 開始執行所有軸運動到目標位置 =====";

    // 首先初始化目標位置設定
    initializeAxisTargetPositions();

    // 檢查控制卡是否已初始化
    if (!isCardInitialized && !isCardGloballyInitialized) {
        qDebug() << "錯誤: 控制卡未初始化，無法執行運動";
        return;
    }

    // ===== 新增：自動生成並寫入 0_pose.txt 檔案 =====
    QString poseFilePath = QCoreApplication::applicationDirPath() + "/0_pose.txt";
    QFile poseFile(poseFilePath);

    // 創建或覆蓋檔案，寫入當前絕對位置
    if (!poseFile.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qDebug() << "錯誤: 無法創建 0_pose.txt 檔案：" << poseFile.errorString();
        QMessageBox::warning(nullptr, "錯誤", "無法創建 0_pose.txt 檔案");
        return;
    }

    QTextStream outStream(&poseFile);
    outStream.setEncoding(QStringConverter::Utf8);

    // 寫入檔案標題
    outStream << "# 軸絕對位置記錄檔案\n";
    outStream << "# 格式: 軸號=絕對位置\n";
    outStream << "# 生成時間: " << QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss") << "\n\n";

    // 獲取並寫入所有軸的當前絕對位置
    QMap<short, long> currentAbsolutePositions;

    // 從控制卡讀取當前所有軸的絕對位置
    for (short axis = 1; axis <= 8; ++axis) {  // 假設最多8軸，你可以根據實際情況調整
        long currentPos;
        if (GTN_GetEcatEncPos(cardCore, axis, &currentPos) == 0) {
            currentAbsolutePositions[axis] = currentPos;
            outStream << QString("axis_%1=%2\n").arg(axis).arg(currentPos);
            qDebug() << QString("寫入軸 %1 當前絕對位置: %2").arg(axis).arg(currentPos);
        } else {
            qDebug() << QString("警告: 無法讀取軸 %1 的絕對位置").arg(axis);
        }
    }

    poseFile.close();
    qDebug() << "成功生成 0_pose.txt 檔案：" << poseFilePath;

    // ===== 從檔案讀取位置數據替代暫存記憶體 =====
    if (currentAbsolutePositions.isEmpty()) {
        qDebug() << "警告: 沒有讀取到任何軸的絕對位置數據";
        return;
    }

    // 遍歷所有有絕對位置數據的軸
    for (auto it = currentAbsolutePositions.begin(); it != currentAbsolutePositions.end(); ++it) {
        short axis = it.key();
        long absolutePos = it.value();

        qDebug() << QString("開始軸 %1 歸零運動，當前絕對位置: %2").arg(axis).arg(absolutePos);

        // 檢查軸狀態
        long status;
        if (GTN_GetSts(cardCore, axis, &status) != 0) {
            qDebug() << QString("警告: 無法獲取軸 %1 狀態").arg(axis);
            continue;
        }

        // 確保軸已使能且未在運動中
        if (!(status & 0x200)) {
            qDebug() << QString("警告: 軸 %1 未使能，跳過歸零").arg(axis);
            continue;
        }

        if (status & 0x400) {
            qDebug() << QString("警告: 軸 %1 正在運動中，跳過歸零").arg(axis);
            continue;
        }

        // 按照官方標準流程進行歸零運動
        short sRtn;

        // 1. 清除軸狀態
        sRtn = GTN_ClrSts(cardCore, axis, 8);
        if (sRtn != 0) {
            qDebug() << QString("警告: 軸 %1 清除狀態失敗，錯誤碼: %2").arg(axis).arg(sRtn);
            continue;
        }

        // 2. 伺服使能
        sRtn = GTN_AxisOn(cardCore, axis);
        if (sRtn != 0) {
            qDebug() << QString("警告: 軸 %1 伺服使能失敗，錯誤碼: %2").arg(axis).arg(sRtn);
            continue;
        }

        // ===== 【重要修正】運動起點計算邏輯 =====
        // 修正原因：
        // 1. 原邏輯強制設置 GTN_SetPrfPos(axis, absolutePos) 會製造假象，讓規劃位置=絕對位置
        // 2. 這違背了物理現實：規劃位置和絕對位置在真實機械系統中永遠不會完全相等
        // 3. 正確做法：保持真實的規劃位置不變，基於檔案中的絕對位置計算相對運動距離
        // 4. absolutePos 來自 0_pose.txt 檔案，代表馬達當前真實所在的絕對位置（自然相等，非強制）

        // 3. 先把規劃原點 PrfPos 與機械絕對位置對齊 -----------------
        sRtn = GTN_SetPrfPos(cardCore, axis, static_cast<double>(absolutePos));
        if (sRtn != 0) {
            qDebug() << QString("警告: 軸 %1 同步 PrfPos 失敗，錯誤碼: %2")
                        .arg(axis).arg(sRtn);
            continue;
        }

        // 4. 直接以目標絕對座標下達命令 -------------------------------
        long targetPos   = axisTargetPositions.value(axis, 0);
        long moveDistance = targetPos - absolutePos;   // 只是列印用
        qDebug() << QString("軸 %1 運動計算: 起點=%2 ⇒ 目標=%3，位移=%4")
                    .arg(axis).arg(absolutePos).arg(targetPos).arg(moveDistance);

        // 5. 設置為點位運動模式
        sRtn = GTN_PrfTrap(cardCore, axis);
        if (sRtn != 0) {
            qDebug() << QString("警告: 軸 %1 設置運動模式失敗，錯誤碼: %2").arg(axis).arg(sRtn);
            continue;
        }

        // 6. 設置運動參數（優化波形平滑度）
        TTrapPrm trapPrm;
        double velocity = 10.0;  // 歸零速度
        trapPrm.acc = velocity * 20;   // 降低加速度係數到20倍
        trapPrm.dec = velocity * 20;   // 降低減速度係數到20倍
        trapPrm.smoothTime = 30;       // 增加平滑時間到30
        sRtn = GTN_SetTrapPrm(cardCore, axis, &trapPrm);
        if (sRtn != 0) {
            qDebug() << QString("警告: 軸 %1 設置運動參數失敗，錯誤碼: %2").arg(axis).arg(sRtn);
            continue;
        }

        // 7. 設置規劃目標位置（使用絕對座標）
     sRtn = GTN_SetPos(cardCore, axis, targetPos);
        if (sRtn != 0) {
            qDebug() << QString("警告: 軸 %1 設置目標位置失敗，錯誤碼: %2").arg(axis).arg(sRtn);
            continue;
        }

        // 7. 設置運動速度
        sRtn = GTN_SetVel(cardCore, axis, velocity);
        if (sRtn != 0) {
            qDebug() << QString("警告: 軸 %1 設置運動速度失敗，錯誤碼: %2").arg(axis).arg(sRtn);
            continue;
        }

        // 8. 開始運動
        sRtn = GTN_Update(cardCore, 1 << (axis - 1));
        if (sRtn == 0) {
            qDebug() << QString("軸 %1 開始運動，絕對位置從 %2 → %3")
                        .arg(axis).arg(absolutePos).arg(targetPos);
        } else {
            qDebug() << QString("警告: 軸 %1 啟動運動失敗，錯誤碼: %2").arg(axis).arg(sRtn);
        }
    }

    qDebug() << "===== [軸運動] 所有軸運動指令已發送，開始監控歸位進度 =====";

    // ======= 新增：等待所有軸歸位完成 =======
    const int HOMING_TIMEOUT_MS = 120000;  // 120 秒超時
    QElapsedTimer homingTimer; homingTimer.start();
    QMap<short, bool> axisDone;  // 記錄軸是否已完成
    for (short ax : currentAbsolutePositions.keys()) axisDone[ax] = false;

    while (true) {
        bool allFinished = true;
        for (short ax : currentAbsolutePositions.keys()) {
            if (axisDone[ax]) continue;

            long sts = 0;
            if (GTN_GetSts(cardCore, ax, &sts) == 0) {
                bool isMoving = sts & 0x400;   // bit10 = 規劃軸運動標誌
                if (!isMoving) {
                    axisDone[ax] = true;
                    qDebug() << QString("[歸位完成] 軸 %1 已停止，歸位結束").arg(ax);
                } else {
                    allFinished = false;
                }
            }
        }

        if (allFinished) break;

        if (homingTimer.elapsed() > HOMING_TIMEOUT_MS) {
            qDebug() << "警告: 歸位監控超時，仍有軸未完成！";
            break;
        }

        QThread::msleep(20);
    }

    if (homingTimer.elapsed() <= HOMING_TIMEOUT_MS) {
        qDebug() << "===== [軸運動] 所有軸歸位完成，開始位置同步驗證 =====";

        // 【關鍵修正】歸位完成後，將當前位置同步為目標位置，而不是強制歸零
        // 首先，等待一小段時間讓馬達完全穩定
        QThread::msleep(2000); // 增加2000毫秒延遲，等待伺服整定

        bool allAxisSyncSuccess = true;  // 記錄是否所有軸同步成功

        for (short ax : currentAbsolutePositions.keys()) {
            long targetPos = axisTargetPositions.contains(ax) ? axisTargetPositions[ax] : 0;

            qDebug() << QString("  [CHECK] 檢查軸 %1 運動完成後的真實位置狀態，目標位置: %2").arg(ax).arg(targetPos);

            // 1. 先讀取當前絕對位置 (不受 PrfPos 原點影響)
            long actualAbsPos;
            short sRtnAbs = GTN_GetEcatEncPos(cardCore, ax, &actualAbsPos);

            // 2. 【同步原點】將 PrfPos 重設為絕對位置，以便規劃/編碼器位置與絕對座標對齊
            if (sRtnAbs == 0) {
                GTN_SetPrfPos(cardCore, ax, static_cast<double>(actualAbsPos));
            }

            // 3. 重新讀取規劃與編碼器位置，確保與絕對坐標一致後再顯示
            double actualPrfPos, actualEncPos;
            short sRtnPrf = GTN_GetPrfPos(cardCore, ax, &actualPrfPos, 1, nullptr);
            short sRtnEnc = GTN_GetEncPos(cardCore, ax, &actualEncPos, 1, nullptr);

            if (sRtnPrf != 0 || sRtnEnc != 0 || sRtnAbs != 0) {
                qDebug() << QString("  警告: 軸 %1 讀取位置失敗. GetPrfPos: %2, GetEncPos: %3, GetEcatEncPos: %4")
                            .arg(ax).arg(sRtnPrf).arg(sRtnEnc).arg(sRtnAbs);
                allAxisSyncSuccess = false;
                continue;
            }

            // 4. 顯示真實的位置狀態（不隱藏誤差）
            double prfError = actualPrfPos - targetPos;
            double encError = actualEncPos - targetPos;
            long absError = actualAbsPos - targetPos;

            qDebug() << QString("  [REAL-STATUS] 軸 %1 真實位置狀態:")
                        .arg(ax);
            qDebug() << QString("    規劃位置: %1 (誤差: %2)")
                        .arg(actualPrfPos).arg(prfError);
            qDebug() << QString("    編碼器位置: %1 (誤差: %2)")
                        .arg(actualEncPos).arg(encError);
            qDebug() << QString("    絕對位置: %1 (誤差: %2)")
                        .arg(actualAbsPos).arg(absError);

            // 5. 檢查定位精度（設定允許誤差範圍）
            const long POSITION_TOLERANCE = 50;  // 允許 ±50 脈衝誤差
            if (abs(absError) <= POSITION_TOLERANCE) {
                qDebug() << QString("  [POSITION-OK] 軸 %1 定位精度合格，絕對誤差: %2 (≤%3)")
                            .arg(ax).arg(absError).arg(POSITION_TOLERANCE);
            } else {
                qDebug() << QString("  [POSITION-FAIL] 軸 %1 定位精度不足，絕對誤差: %2 (>%3)")
                            .arg(ax).arg(absError).arg(POSITION_TOLERANCE);
                allAxisSyncSuccess = false;
            }
        }

        // 【重要】最終成功訊息 - 只有在所有程序都完成且精度合格時才顯示
        if (allAxisSyncSuccess) {
            qDebug() << "===== [軸運動] 所有軸歸位完成並定位精度合格，可以下達 PT 指令 =====";
        } else {
            qDebug() << "===== [軸運動] 歸位完成但部分軸定位精度不足，請檢查軸狀態 =====";
        }
    } else {
        qDebug() << "===== [軸運動] 歸位超時，無法確保軸狀態正確 =====";
    }
    // ======= 監控結束 =======
}

// 公有接口函數，用於外部調用歸零功能
void ethercat::executeAxisHoming()
{
    // 直接調用私有槽函數
    on_pushButton_go_0_clicked();
}

// 設置軸目標位置的實現函數
void ethercat::setAxisTargetPosition(short axis, long targetPosition)
{
    axisTargetPositions[axis] = targetPosition;
    qDebug() << QString("設置軸 %1 目標位置為: %2").arg(axis).arg(targetPosition);
}


