#include "dialog_plc.h"
#include "ui_dialog_plc.h"
#include <QMessageBox>
#include <QPair>
#include <QSerialPort>
#include <QSerialPortInfo>
#include <QDebug>

Dialog_Plc::Dialog_Plc(QWidget *parent)
    : QDialog(parent)
    , ui(new Ui::Dialog_Plc)
{
    ui->setupUi(this);

    connect(ui->pushButton_Plc_Connect, &QPushButton::clicked, this, &Dialog_Plc::on_pushButton_Plc_Connect_clicked);
    connect(ui->pushButton_Plc_AddToMainwinder_2, &QPushButton::clicked, this, &Dialog_Plc::on_pushButton_Plc_AddToMainwinder_clicked);

    serialPort = new QSerialPort(this);
    connect(serialPort, &QSerialPort::readyRead, this, &Dialog_Plc::onReadyRead);
}

Dialog_Plc::~Dialog_Plc()
{
    if (serialPort->isOpen()) {
        serialPort->close();
    }
    delete ui;
}

// 新增的方法实现
QString Dialog_Plc::getSerialPortName() const
{
    return ui->lineEdit_7ACOMPORT->text();
}

int Dialog_Plc::getBaudRate() const
{
    return ui->lineEdit_7ABaudRate->text().toInt();
}

int Dialog_Plc::getDataBits() const
{
    return ui->lineEdit_7ADATABITS->text().toInt();
}

QSerialPort::StopBits Dialog_Plc::getStopBits() const
{
    switch (ui->lineEdit_7ASTOPBITS->text().toInt()) {
    case 1: return QSerialPort::OneStop;
    case 2: return QSerialPort::TwoStop;
    default: return QSerialPort::OneStop;
    }
}

QSerialPort::Parity Dialog_Plc::getParity() const
{
    QString parity = ui->lineEdit_7APARITY->text().toLower();
    if (parity == "none") return QSerialPort::NoParity;
    else if (parity == "even") return QSerialPort::EvenParity;
    else if (parity == "odd") return QSerialPort::OddParity;
    else return QSerialPort::NoParity;
}

QSerialPort::FlowControl Dialog_Plc::getFlowControl() const
{
    QString flowControl = ui->lineEdit_7AFLOWCONTROL->text().toLower();
    if (flowControl == "none") return QSerialPort::NoFlowControl;
    else if (flowControl == "hardware") return QSerialPort::HardwareControl;
    else if (flowControl == "software") return QSerialPort::SoftwareControl;
    else return QSerialPort::NoFlowControl;
}

int Dialog_Plc::getSlaveAddress() const
{
    return ui->lineEdit_7ASLAVEADDRESS->text().toInt();
}

int Dialog_Plc::getTimeout() const
{
    return ui->lineEdit_7ATIMEOUT->text().toInt();
}

int Dialog_Plc::getRetryCount() const
{
    return ui->lineEdit_7ARETRAY->text().toInt();
}

void Dialog_Plc::on_pushButton_Plc_Connect_clicked()
{
    if (serialPort->isOpen()) {
        serialPort->close();
    }

    serialPort->setPortName(getSerialPortName());
    serialPort->setBaudRate(getBaudRate());
    serialPort->setDataBits(static_cast<QSerialPort::DataBits>(getDataBits()));
    serialPort->setStopBits(getStopBits());
    serialPort->setParity(getParity());
    serialPort->setFlowControl(getFlowControl());

    if (serialPort->open(QIODevice::ReadWrite)) {
        ui->label_connect->setStyleSheet("QLabel { color : green; }");
        ui->label_connect->setText("連接成功");

        slaveAddress = getSlaveAddress();
        timeout = getTimeout();
        retryCount = getRetryCount();
    } else {
        ui->label_connect->setStyleSheet("QLabel { color : red; }");
        ui->label_connect->setText("連接失敗：" + serialPort->errorString());
    }
}

void Dialog_Plc::onReadyRead()
{
    QByteArray data = serialPort->readAll();
    ui->lineEdit_TcpBackMassae->setText(QString::fromUtf8(data));
}

void Dialog_Plc::on_pushButton_Plc_AddToMainwinder_clicked()
{
    QString plcOrderText = ui->textEdit_PlcOrder_2->toPlainText();
    QPair<QString, QString> plcOrder = qMakePair(plcOrderText, "PLC");
    emit addPlcOrderToMainWindow(plcOrder);
    qDebug() << "PLC order signal sent. Content:" << plcOrderText;
}
