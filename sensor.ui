<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Sensor</class>
 <widget class="QDialog" name="Sensor">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>839</width>
    <height>529</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Sensor/TCP</string>
  </property>
  <property name="styleSheet">
   <string notr="true">background-color: rgb(223, 223, 223);</string>
  </property>
  <widget class="QWidget" name="horizontalLayoutWidget">
   <property name="geometry">
    <rect>
     <x>200</x>
     <y>10</y>
     <width>521</width>
     <height>31</height>
    </rect>
   </property>
   <layout class="QHBoxLayout" name="horizontalLayout">
    <item>
     <widget class="QLabel" name="label">
      <property name="text">
       <string>IP</string>
      </property>
     </widget>
    </item>
    <item>
     <spacer name="verticalSpacer_3">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <widget class="QLineEdit" name="lineEdit_Sensor_Ip">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
     </widget>
    </item>
    <item>
     <spacer name="verticalSpacer_2">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <widget class="QLabel" name="label_2">
      <property name="text">
       <string>PORT</string>
      </property>
     </widget>
    </item>
    <item>
     <spacer name="verticalSpacer">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <widget class="QLineEdit" name="lineEdit_Sensor_port">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QWidget" name="horizontalLayoutWidget_2">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>50</y>
     <width>521</width>
     <height>31</height>
    </rect>
   </property>
   <layout class="QHBoxLayout" name="horizontalLayout_2">
    <item>
     <widget class="QPushButton" name="pushButton_Sensor_Connect">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(199, 199, 199);</string>
      </property>
      <property name="text">
       <string>Connect</string>
      </property>
     </widget>
    </item>
    <item>
     <spacer name="verticalSpacer_5">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <widget class="QLabel" name="label_Sensor_Display_Connect">
      <property name="text">
       <string>Connect_Check</string>
      </property>
     </widget>
    </item>
    <item>
     <spacer name="verticalSpacer_4">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
   </layout>
  </widget>
  <widget class="QTextEdit" name="textEdit_Sensor_SendMassae">
   <property name="geometry">
    <rect>
     <x>100</x>
     <y>90</y>
     <width>611</width>
     <height>31</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(255, 255, 255);</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_4">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>90</y>
     <width>81</width>
     <height>19</height>
    </rect>
   </property>
   <property name="text">
    <string>SendCode</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_5">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>130</y>
     <width>81</width>
     <height>19</height>
    </rect>
   </property>
   <property name="text">
    <string>BackCode</string>
   </property>
  </widget>
  <widget class="QTextEdit" name="textEdit_Sensor_BackMassae">
   <property name="geometry">
    <rect>
     <x>100</x>
     <y>130</y>
     <width>611</width>
     <height>91</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(255, 255, 255);</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_6">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>20</y>
     <width>69</width>
     <height>19</height>
    </rect>
   </property>
   <property name="text">
    <string>Name:</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEdit_3_sensorName">
   <property name="geometry">
    <rect>
     <x>70</x>
     <y>20</y>
     <width>113</width>
     <height>21</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(255, 255, 255);</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pushButton_Sensor_SendMassage">
   <property name="geometry">
    <rect>
     <x>720</x>
     <y>90</y>
     <width>93</width>
     <height>28</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(229, 229, 229);</string>
   </property>
   <property name="text">
    <string>Send</string>
   </property>
  </widget>
  <widget class="QWidget" name="horizontalLayoutWidget_3">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>230</y>
     <width>681</width>
     <height>89</height>
    </rect>
   </property>
   <layout class="QHBoxLayout" name="horizontalLayout_3">
    <item>
     <widget class="QLabel" name="label_7">
      <property name="text">
       <string>BackRAGE</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QTextEdit" name="textEdit">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLabel" name="label_8">
      <property name="text">
       <string>to</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QTextEdit" name="textEdit_2">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QLabel" name="label_9">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>340</y>
     <width>221</width>
     <height>29</height>
    </rect>
   </property>
   <property name="text">
    <string>IF UP_OUT_SENT_CODE</string>
   </property>
  </widget>
  <widget class="QTextEdit" name="textEdit_3">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>370</y>
     <width>681</width>
     <height>29</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(255, 255, 255);</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_10">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>410</y>
     <width>221</width>
     <height>29</height>
    </rect>
   </property>
   <property name="text">
    <string>IF LOW_OUT_SENT_CODE</string>
   </property>
  </widget>
  <widget class="QTextEdit" name="textEdit_4">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>440</y>
     <width>681</width>
     <height>29</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(255, 255, 255);</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_3">
   <property name="geometry">
    <rect>
     <x>40</x>
     <y>490</y>
     <width>601</width>
     <height>19</height>
    </rect>
   </property>
   <property name="text">
    <string>請以     SENSOR[Sensor_599  本頁面傳感器名稱 ]:   後面加要發的   ASCII命令</string>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
