#ifndef ETHERCATMASTER_H
#define ETHERCATMASTER_H

#include <vector>
#include <cstdint>

class EtherCATMaster {
public:
    struct SlaveInfo {
        int position;
        uint32_t vendorId;
        uint32_t productCode;
    };

    bool initialize(const char* ifname);
    bool scanSlaves();
    bool setState(uint16_t state);
    bool writeSDO(uint16_t slave, uint16_t index, uint8_t subIndex, int32_t value);
    bool readSDO(uint16_t slave, uint16_t index, uint8_t subIndex, int32_t& value);
    bool processPDO();
    std::vector<SlaveInfo> getSlaves() const;

private:
    std::vector<SlaveInfo> slaves;
};

#endif // ETHERCATMASTER_H 