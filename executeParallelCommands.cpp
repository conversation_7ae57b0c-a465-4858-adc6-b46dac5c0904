// executeParallelCommands.cpp - 多軸並行計算函數實現 (executeCommandSequence的多線程版本)
#include "ethercat.h"
#include "AxisWorker.h"
#include "AxisSynchronizer.h"
#include <QMetaObject>
#include <QDebug>
#include <QFuture>
#include <QtConcurrent>

// 執行並行命令處理
void ethercat::executeParallelCommands(const QStringList& commands) {
    GTN_ClearTime(cardCore, TIME_ELAPSE_PROFILE);
    qDebug() << "清除中斷執行時間統計";

    qDebug() << "\n===== [PVT斷點3-命令序列執行開始(並行版)] =====";
    qDebug() << "- 命令數量：" << commands.size();
    qDebug() << "- 當前時間：" << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    qDebug() << "- 執行狀態：" << isExecutingCommand;
    qDebug() << "- 待處理序列數量：" << pendingSequences.size();
    
    // 【重要】確保表單ID已初始化
    initializeTableIds();
    
    // 清除PVT緩存，但不重置命令隊列（防止丟失命令）
    axisPVTCache.clear();
    
    // 解析命令到隊列
    parseCommandsToQueues(commands);
    
    qDebug() << "[DEBUG] 嘗試啟動Watch監控";
    // 啟動Watch監控以檢測同步問題
    //startWatchMonitoring();
    
    // 處理每個軸的第一個命令
    QSet<short> activeAxes;
    for (auto it = axisCommandQueues.begin(); it != axisCommandQueues.end(); ++it) {
        short axis = it.key();
        
        if (!it.value().isEmpty()) {
            QString command = it.value().dequeue();
            parseCommandToCache(command);
            activeAxes.insert(axis);
            
            // 標記第一個命令已執行
            executedAxisCommands[axis]++;
            qDebug() << "軸" << axis << "第一個命令標記為已執行";
        }
    }
    
    qDebug() << "\n===== [PVT斷點3.3-處理第一個命令完成] =====";
    qDebug() << "- 已激活的轴数量：" << activeAxes.size();
    
    // 第一階段：一次性先收集所有需要的資訊
    QMap<short, short> axisTableIds;
    QList<short> axisArray;

    for (short axis : activeAxes) {
        axisArray.append(axis);
        axisTableIds[axis] = getCurrentTableId(axis);
    }

    // 第二階段：批量處理軸設置 (並行執行)
    QList<QFuture<void>> axisFutures;
    for (short axis : axisArray) {
        axisFutures.append(QtConcurrent::run([=]() {
            // 1. 先清除軸錯誤
            GTN_ClrSts(cardCore, axis);
            QThread::msleep(50);
            
            // 2. 檢查軸是否已經使能，如果沒有才使能
            long status;
            if (GTN_GetSts(cardCore, axis, &status) == 0) {
                // 檢查使能狀態位 (0x200)
                if (!(status & 0x200)) {
                    // 軸未使能，需要使能
                    GTN_AxisOn(cardCore, axis);
                    qDebug() << "軸" << axis << "未使能，正在使能";
                    Sleep(1000);  // 等待使能完成
                }
            }
            
            // 3. 設置為PVT模式
            if (GTN_PrfPvt(cardCore, axis) != 0) {
                qDebug() << "錯誤: 設置軸" << axis << "為PVT模式失敗";
                return;
            }
            qDebug() << "設置軸" << axis << "為PVT模式成功";
            
            // 4. 選擇表單ID
            if (GTN_PvtTableSelect(cardCore, axis, axisTableIds[axis]) != 0) {
                qDebug() << "錯誤: 軸" << axis << "選擇PVT表格失敗";
                return;
            }
        }));
    }

    // 等待所有軸初始化完成
    for (auto &future : axisFutures) {
        future.waitForFinished();
    }
    
    // 創建同步器 - 用於多線程同步
    AxisSynchronizer *calcSynchronizer = new AxisSynchronizer(this);
    AxisSynchronizer *uploadSynchronizer = new AxisSynchronizer(this);
    
    // 存儲所有工作線程和實際線程
    QMap<short, AxisWorker*> workers;
    QMap<short, QThread*> threads;
    
    // 按照廠商流程：先清除錯誤，再使能軸，然後設置PVT模式
    short mask = 0;
    for (short axis : activeAxes) {
        // 添加到同步器
        calcSynchronizer->addAxis(axis);
        uploadSynchronizer->addAxis(axis);
        
        // 4. 上傳PVT數據到當前表單
        short currentTableId = getCurrentTableId(axis);
        axisTableIds[axis] = currentTableId;
        
        qDebug() << "\n===== [PVT斷點3.5-獲取當前表單ID] =====";
        qDebug() << "- 軸號：" << axis;
        qDebug() << "- 當前表單ID：" << currentTableId;
        
        // 創建工作線程
        AxisWorker *worker = new AxisWorker(axis, cardCore, this);
        QThread *thread = new QThread();
        
        // 設置工作線程到實際線程
        worker->moveToThread(thread);
        
        // 連接信號
        connect(worker, &AxisWorker::calculationDone, 
                calcSynchronizer, &AxisSynchronizer::axisReady);
        connect(worker, &AxisWorker::uploadDone, 
                uploadSynchronizer, &AxisSynchronizer::axisReady);
        connect(worker, &AxisWorker::error, 
                this, &ethercat::handleAxisError);
        
        // 保存工作線程和實際線程
        workers[axis] = worker;
        threads[axis] = thread;
        
        // 啟動線程
        thread->start();
        
        // 6. 添加到啟動掩碼
        mask |= (1 << (axis - 1));
    }
    
    // 如果沒有可用的軸，則退出
    if (mask == 0) {
        qDebug() << "錯誤: 沒有可用的軸可以啟動";
        calcSynchronizer->deleteLater();
        uploadSynchronizer->deleteLater();
        return;
    }
    
    // 計算完成後上傳數據
    connect(calcSynchronizer, &AxisSynchronizer::allAxesReady, 
            this, [=]() {
                qDebug() << "\n===== [並行處理] 所有軸計算完成，開始上傳數據 =====";
                
                // 使用QtConcurrent實現真正的並行上傳
                QList<QFuture<void>> uploadFutures;
                for (auto it = workers.begin(); it != workers.end(); ++it) {
                    short axis = it.key();
                    uploadFutures.append(QtConcurrent::run([=]() {
                        // 直接在新線程中上傳數據，而不是通過信號槽
                        uploadPVTData(axis, axisTableIds[axis]);
                        // 上傳完成後發出信號
                        emit it.value()->uploadDone(axis);
                    }));
                }
            });
    
    // 處理所有軸上傳完成後的同步啟動
    connect(uploadSynchronizer, &AxisSynchronizer::allAxesReady, 
            this, [=]() {
                qDebug() << "\n===== [PVT斷點3.8-準備啟動PVT運動] =====";
                qDebug() << "- 啟動掩碼：" << QString("0x%1").arg(mask, 0, 16);
                
                // 7. 啟動PVT運動
                int result = GTN_PvtStart(cardCore, mask);
                
                qDebug() << "\n===== [PVT斷點3.9-PVT運動啟動結果] =====";
                qDebug() << "- 啟動結果：" << result;
                
                // 8. 監控運動
                if (result == 0) {
                    qDebug() << "\n===== [PVT斷點3.10-開始監控PVT運動] =====";
                    qDebug() << "成功啟動所有軸的PVT運動";
                    
                    // 設置命令執行標誌
                    isExecutingCommand = true;
                    
                    // 監控運動
                    monitorAllPVTMotions();
                } else {
                    qDebug() << "錯誤: 啟動PVT運動失敗，錯誤碼:" << result;
                    isExecutingCommand = false;
                }
                
                // 清理資源
                for (auto thread : threads) {
                    thread->quit();
                    thread->wait();
                    thread->deleteLater();
                }
                for (auto worker : workers) {
                    worker->deleteLater();
                }
                
                calcSynchronizer->deleteLater();
                uploadSynchronizer->deleteLater();
            });
    
    // 啟動每個軸的數據處理
    for (auto it = workers.begin(); it != workers.end(); ++it) {
        short axis = it.key();
        if (axisPVTCache.contains(axis)) {
            // 數據已經通過parseCommandToCache解析，直接發出信號即可
            emit it.value()->calculationDone(axis);
        } else {
            qDebug() << "警告: 軸" << axis << "沒有PVT數據緩存";
            emit it.value()->calculationDone(axis);
        }
    }
}