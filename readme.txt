从你提供的描述来看，你希望实现的是一种工作流程的累加，每次用户在add_stepmotor_action界面设置参数并点击pushButton_addaction按钮时，就会将一个新的电机控制序列添加到一个列表中。然后，用户可以点击mainwindow.ui中的pushButton_run按钮来执行这些累积的命令。

要实现这个功能，你需要在add_stepmotor_action类中存储这些命令，然后在mainwindow类中执行这些命令。你可以在add_stepmotor_action类中使用一个容器（比如QList或QVector）来存储这些命令，每个命令可以是一个结构体或类，包含速度、持续时间和启动的信息。

这里是一个大概的实现方案：

在add_stepmotor_action类中，定义一个命令结构体和一个存储命令的列表。
cpp
Copy code
struct MotorCommand {
    int speed;
    int duration;
    bool start;
};

// add_stepmotor_action.h 中的类定义
class add_stepMotor_action : public QDialog {
    // ...
    QList<MotorCommand> commands;
    void addCommand(int speed, int duration, bool start);
    // ...
};
当用户点击pushButton_addaction时，创建一个新的MotorCommand并添加到列表中。
cpp
Copy code
void add_stepMotor_action::addCommand(int speed, int duration, bool start) {
    MotorCommand cmd { speed, duration, start };
    commands.append(cmd);
}
在mainwindow类中，实现一个槽函数来执行add_stepmotor_action中累积的命令。
cpp
Copy code
// mainwindow.cpp
void MainWindow::executeAllCommands() {
    for (const MotorCommand &cmd : myStepMotorAction->commands) {
        myStepMotorAction->sendCommand(cmd.speed, cmd.duration, cmd.start);
        QThread::sleep(cmd.duration);
    }
}
在mainwindow.ui中，连接pushButton_run按钮的点击信号到上面的executeAllCommands槽函数。
cpp
Copy code
connect(ui->pushButton_run, &QPushButton::clicked, this, &MainWindow::executeAllCommands);
这种方式允许用户动态地构建一个电机控制命令列表，并在最后一次性执行所有的命令。这只是一个基础的框架，根据你的需求可能需要进一步细化和调试。