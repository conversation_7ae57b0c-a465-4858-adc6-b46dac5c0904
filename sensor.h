#ifndef SENSOR_H
#define SENSOR_H

#include <QDialog>
#include <QTcpSocket>

namespace Ui {
class Sensor;
}

class Sensor : public QDialog
{
    Q_OBJECT

public:
    explicit Sensor(QWidget *parent = nullptr, int port = 8080);  // 修改構造函數
    ~Sensor();

    void sendCommand(const QString& command);  // 添加這行
    QString getName() const;
    void sendHexCommand(const QByteArray& hexData);

signals:
    void sensorCreated(const QString& name, Sensor* sensor);


public slots:
    void on_pushButton_Sensor_Connect_clicked(); // 槽函数的声明
    void onReadyRead();//槽函数
    void sendMassageToServer();//槽函数



private:
    Ui::Sensor *ui;
    QTcpSocket *tcpSocket;
    QString sensorName;
    int port;  // 添加 port 成員變量
};

#endif // SENSOR_H
