
我們的版本
graph TD
    A[用戶在textEdit_Muti中輸入多軸PVT命令] --> B[用戶點擊pushButton_sendMain_3按鈕]
    B --> C[MainWindow::on_pushButton_sendMain_3_clicked]
    C --> D[獲取textEdit_Muti中的文本]
    D --> E[切分為多行命令]
    E --> F[將每行命令加入sensorCommandQueue]
    F --> G[調用processSensorCommandQueue處理命令]
    
    G --> H[從隊列中取出一條多軸命令]
    H --> I[解析命令，按電機窗口名稱分組]
    I --> J{是PVT命令?}
    J -->|是| K[按窗口分組命令]
    J -->|否| L[其他命令處理]
    
    K --> M[對每個窗口調用window->sendPVTCommands]
    M --> N[創建CommandSequence對象並添加到pendingSequences]
    N --> O[調用executeNextSequence開始執行序列]
    
    O --> P[從pendingSequences獲取下一個序列]
    P --> Q[將序列設為當前序列currentSequence]
    Q --> R[調用executeNextCommandInSequence執行序列中的命令]
    
    R --> S[獲取當前命令並增加索引]
    S --> T[清空之前的PVT數據]
    T --> U{命令包含逗號?}
    U -->|是| V[切分為多個軸命令並分別解析]
    U -->|否| W[解析單軸命令]
    
    V --> X[對每個軸調用parsePVTCommand]
    W --> X
    
    X --> Y[解析PVT命令參數]
    Y --> Z[創建PVTMotion對象並添加到multiSegmentPVTs]
    Z --> AA[調用executePVTMotions執行PVT運動]
    
    AA --> AB[檢查所有軸的運動狀態]
    AB --> AC{有軸在運動?}
    AC -->|是| AD[延遲200ms後重試]
    AC -->|否| AE[為每個軸設置PVT模式]
    
    AE --> AF[生成每個軸的PVT點數據]
    AF --> AG[將PVT數據下載到控制卡]
    AG --> AH[啟動PVT運動]
    AH --> AI[設置運動完成回調]
    
    AI --> AJ[運動完成後自動調用monitorPVTMotions]
    AJ --> AK[檢查運動是否完成]
    AK --> AL{運動完成?}
    AL -->|是| AM[調用executeNextCommandInSequence執行下一條命令]
    AL -->|否| AN[延遲後重新檢查]
    
    AM --> AO{序列中還有命令?}
    AO -->|是| R
    AO -->|否| AP[調用executeNextSequence處理下一個序列]
    AP --> AQ{還有待處理序列?}
    AQ -->|是| O
    AQ -->|否| AR[命令處理完成]





廠商的版本
graph TD
    A[初始化階段] --> B[打開運動控制器 GTN_Open]
    B --> C[初始化EtherCAT通訊 GTN_InitEcatComm]
    C --> D[等待EtherCAT就緒 GTN_IsEcatReady]
    D --> E[開始EtherCAT通訊 GTN_StartEcatComm]
    E --> F[系統復位 GTN_Reset]
    F --> G[配置階段]
    
    G --> H[加載配置文件 GTN_LoadConfig]
    H --> I[清除軸狀態 GTN_ClrSts]
    I --> J[啟動伺服 GTN_AxisOn]
    J --> K[等待伺服使能完成 Sleep]
    K --> L[PVT設置階段]
    
    L --> M[設置為PVT模式 GTN_PrfPvt]
    M --> N[建立PVT數據表 GTN_PvtTable]
    N --> O[選擇數據表 GTN_PvtTableSelect]
    O --> P[啟動PVT運動 GTN_PvtStart]
    P --> Q[監控階段]
    
    Q --> R[讀取軸狀態 GTN_GetSts]
    R --> S[讀取運動時間 GTN_PvtStatus]
    S --> T[讀取規劃速度 GTN_GetPrfVel]
    T --> U[讀取規劃位置 GTN_GetPrfPos]
    U --> V[顯示數據]
    V --> W{運動完成?}
    W -->|否| R
    W -->|是| X[結束階段]
    
    X --> Y[關閉伺服 GTN_AxisOff]
    Y --> Z[終止EtherCAT通訊 GTN_TerminateEcatComm]
    Z --> AA[關閉運動控制器 GTN_Close]