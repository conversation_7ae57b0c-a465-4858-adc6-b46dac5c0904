﻿// 5-3.cpp : 定义控制台应用程序的入口点。
//

#include "stdafx.h"
#include "conio.h"
#include "gts.h"
#include "windows.h"

#define AXIS 1		// 定义轴号
#define CORE 1		// 控制卡内核序号默认为1

// 该函数检测某条GTN指令的执行结果，command为指令名称，error为指令执行返回值
void commandhandler(char *command, short error)
{
	// 如果指令执行返回值为非0，说明指令执行错误，向屏幕输出错误结果
	if (error)
	{
		printf("%s = %d\n", command, error);
	}
}

int _tmain(int argc, _TCHAR* argv[])
{
	short sRtn; // 指令返回值变量
	short sEcatSts;	// ECAT通讯状态变量
	short sProbePrm; // 探针参数第 5 章 EtherCAT 指令说明
	//37© 2015 固高科技版权所有
	unsigned short usProbeSts; // 探针捕获状态
	long lRiseValue1, lRiseValue2, lFallValue1, lFallValue2; // 探针捕获值
	long lLastValue;
	unsigned short bPreSts = 0;
	bool bStopProbe = false;
	sRtn = GTN_Open();
	commandhandler("GTN_Open", sRtn);
	if(sRtn)
	{
		printf("Failure to access cord!\n");
		return -1;
	}
	sRtn = GTN_InitEcatComm(CORE);
	commandhandler("GTN_InitEcatComm", sRtn);
	if(sRtn)
	{
		printf("EtherCAT communication error!\n");
		return -1;
	}
	do {// 查询EtherCAT通讯是否准备好了
		sRtn = GTN_IsEcatReady(CORE, &sEcatSts);
	} while (sEcatSts != 1 || sRtn != 0);
	sRtn = GTN_StartEcatComm(CORE);
	commandhandler("GTN_StartEcatComm", sRtn);
	sRtn = GTN_Reset(CORE);
	commandhandler("GTN_Reset", sRtn);
	// 延时5s
	Sleep(5000);
	// 配置轴1报警输出无效
	sRtn = GTN_AlarmOff(CORE, AXIS);
	commandhandler("GTN_AlarmOff", sRtn);
	// 配置轴1正负限位无效
	sRtn = GTN_LmtsOn(CORE, AXIS);
	commandhandler("GTN_LmtsOn", sRtn);
	// 配置完成后要更新状态
	sRtn = GTN_ClrSts(CORE, AXIS,1);
	commandhandler("GTN_ClrSts", sRtn);
	// 轴1伺服使能
	sRtn = GTN_AxisOn(CORE, AXIS);
	commandhandler("GTN_AxisOn", sRtn);
	sProbePrm = 0x0013; // 探针1上升沿连续捕获，探针2无效
	sRtn = GTN_SetTouchProbeFunction(CORE, AXIS, sProbePrm);
	commandhandler("GTN_SetTouchProbeFunction", sRtn);
	do {
		sRtn = GTN_GetTouchProbeStatus(CORE, AXIS, &usProbeSts, 
			&lRiseValue1,&lFallValue1, &lRiseValue2, &lFallValue2);
		commandhandler("GTN_GetTouchProbeStatus", sRtn);
		if (usProbeSts & 0x1) // 探针 1 有效
		{
			if (usProbeSts & 0x2) // 探针 1 上升沿捕获触发
			{
				if (usProbeSts & bPreSts) // 再次捕获触发，实际判断条件请查阅
					//驱动器相关手册。 GTHD 将第 6 位作为上升沿捕获状态刷新标识
				{
					lLastValue = lRiseValue1;
					bPreSts = usProbeSts&0x40;
				}
			}
			if (usProbeSts & 0x4)  // 探针 1 下降沿捕获触发
			{ }
		}
		if (usProbeSts & 0x100)  // 探针 2 有效
		{ }
		if (bStopProbe)
		{
			sProbePrm = 0x00; // 终止探针捕获
			sRtn = GTN_SetTouchProbeFunction(CORE, AXIS, sProbePrm);
			commandhandler("GTN_SetTouchProbeFunction", sRtn);
			break;
		}
	} while (true);
	printf("Please press anykey to continue!\n");
	_getch();
	// 轴1伺服关使能
	sRtn = GTN_AxisOff(CORE, AXIS);
	commandhandler("GTN_AxisOff", sRtn);
	// 关闭EtherCAT通讯
	sRtn = GTN_TerminateEcatComm(CORE);
	commandhandler("GTN_TerminateEcatComm", sRtn);
	// 关闭运动控制器
	sRtn = GTN_Close();
	commandhandler("GTN_Close", sRtn);
	return 0;
}

