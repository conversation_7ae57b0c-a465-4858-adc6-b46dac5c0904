#include "EtherCATMaster.h"
#include "ethercattype.h"
#include "nicdrv.h"
#include "ethercatbase.h"
#include "ethercatmain.h"
#include "ethercatdc.h"
#include "ethercatcoe.h"
#include "ethercatfoe.h"
#include "ethercatconfig.h"

bool EtherCATMaster::initialize(const char* ifname) {
    // 獲取網絡適配器列表
    ec_adaptert* adapter = ec_find_adapters();
    if (!adapter) {
        return false;
    }

    // 查找指定的網絡適配器
    while (adapter) {
        if (strcmp(adapter->desc, ifname) == 0) {
            // 使用找到的適配器初始化
            if (ec_init(adapter->name) <= 0) {
                ec_free_adapters(adapter);
                return false;
            }
            break;
        }
        adapter = adapter->next;
    }

    // 釋放適配器列表
    ec_free_adapters(adapter);

    if (ec_config_init(FALSE) <= 0) {
        ec_close();
        return false;
    }
    
    // 配置DC選項
    ec_configdc();
    
    // 請求PRE-OP狀態
    if (ec_statecheck(0, EC_STATE_PRE_OP, EC_TIMEOUTSTATE) != EC_STATE_PRE_OP) {
        ec_close();
        return false;
    }
    
    return true;
}

bool EtherCATMaster::scanSlaves() {
    int slaveCount = ec_slavecount;
    if (slaveCount <= 0) {
        return false;
    }
    
    std::vector<SlaveInfo> detectedSlaves;
    for (int i = 1; i <= slaveCount; i++) {
        SlaveInfo slave;
        slave.position = i;
        slave.vendorId = ec_slave[i].eep_man;
        slave.productCode = ec_slave[i].eep_id;
        detectedSlaves.push_back(slave);
    }
    
    slaves = detectedSlaves;
    return true;
}

bool EtherCATMaster::setState(uint16_t state) {
    ec_slave[0].state = state;
    ec_writestate(0);
    return (ec_statecheck(0, state, EC_TIMEOUTSTATE) == state);
}

bool EtherCATMaster::writeSDO(uint16_t slave, uint16_t index, uint8_t subIndex, int32_t value) {
    int retval = ec_SDOwrite(slave, index, subIndex, FALSE, sizeof(value), &value, EC_TIMEOUTRXM);
    return (retval > 0);
}

bool EtherCATMaster::readSDO(uint16_t slave, uint16_t index, uint8_t subIndex, int32_t& value) {
    int retval = ec_SDOread(slave, index, subIndex, FALSE, NULL, &value, EC_TIMEOUTRXM);
    return (retval > 0);
}

bool EtherCATMaster::processPDO() {
    ec_send_processdata();
    return (ec_receive_processdata(EC_TIMEOUTRET) > 0);
}

std::vector<EtherCATMaster::SlaveInfo> EtherCATMaster::getSlaves() const {
    return slaves;
} 