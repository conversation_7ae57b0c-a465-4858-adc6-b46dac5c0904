#include "AxisWorker.h"
#include <QRegularExpression>
#include <QRegularExpressionMatch>
#include <QDebug>
#include <stdexcept>
#include "ethercat.h"

// 導入gts.h頭文件 - 根據您的項目結構可能需要調整路徑
extern "C" {
    #include "gts.h"
}

AxisWorker::AxisWorker(short axis, short cardCore, QObject *parent)
    : QObject(parent), m_axis(axis), m_cardCore(cardCore) {
}

AxisWorker::~AxisWorker() {
}

void AxisWorker::processCommand(const QString &command) {
    try {
        // 解析PVT命令參數
        calculatePVTData(command);
        
        // 發出計算完成信號
        emit calculationDone(m_axis);
    } catch (const std::exception &e) {
        emit error(m_axis, QString("計算錯誤: %1").arg(e.what()));
    }
}

void AxisWorker::uploadData(short tableId) {
    qDebug() << "軸" << m_axis << "開始上傳PVT數據到表單ID:" << tableId;
    
    // 獲取ethercat實例
    ethercat* ethercatInstance = qobject_cast<ethercat*>(parent());
    if (ethercatInstance) {
        // 調用uploadPVTData函數上傳數據
        ethercatInstance->uploadPVTData(m_axis, tableId);
    } else {
        emit error(m_axis, "無法獲取ethercat實例進行數據上傳");
        return;
    }
    
    // 數據上傳完成，發出信號
    emit uploadDone(m_axis);
}

void AxisWorker::calculatePVTData(const QString &cmd) {
    qDebug() << "軸" << m_axis << "開始解析命令: " << cmd;
    
    // 解析命令參數
    QRegularExpression regex("\"?\\S+:PA(-?\\d+)-(-?\\d+)\\s+TA([\\d.]+)-([\\d.]+)\\s+SPA([\\d.]+)(?:/([\\d.]+))?(?:/([\\d.]+))?\"?");
    QRegularExpressionMatch match = regex.match(cmd);
    
    if (!match.hasMatch()) {
        throw std::runtime_error("PVT命令格式不匹配: " + cmd.toStdString());
    }
    
    // 提取參數
    double startPos = match.captured(1).toDouble();
    double endPos = match.captured(2).toDouble();
    
    // 檢查是否有雙負號情況 (PA0--200000)
    if (cmd.contains("PA") && cmd.contains("--")) {
        // 如果命令中包含雙負號，確保endPos是負數
        endPos = -std::abs(endPos);
        qDebug() << "檢測到雙負號，將終點位置調整為:" << endPos;
    }
    
    // 檢查 PA-200000-0 這種情況，確保起點是負值
    if (cmd.contains("PA-") && !cmd.contains("--")) {
        // 確保startPos是負數
        startPos = -std::abs(startPos);
        qDebug() << "檢測到起點負值，確保起點位置為:" << startPos;
    }
    
    double startTime = match.captured(3).toDouble();
    double endTime = match.captured(4).toDouble();
    double speed = match.captured(5).toDouble();
    
    // 解析可選的起始和終端速度
    double startVelocity = (match.captured(6).length() > 0) ? 
                          match.captured(6).toDouble() : 0.0;
    double endVelocity = (match.captured(7).length() > 0) ? 
                        match.captured(7).toDouble() : 0.0;
    
    // 清除舊數據
    m_cache.times.clear();
    m_cache.positions.clear();
    m_cache.velocities.clear();
    
    // 添加起點和終點
    m_cache.times.append(startTime);
    m_cache.times.append(endTime);
    m_cache.positions.append(startPos);
    m_cache.positions.append(endPos);
    m_cache.velocities.append(startVelocity);
    m_cache.velocities.append(endVelocity);
    m_cache.startVelocity = startVelocity;
    m_cache.endVelocity = endVelocity;
    m_cache.isDirty = true;
    m_cache.isUploaded = false;
    
    qDebug() << "軸" << m_axis << "解析結果: " 
             << "起點位置=" << startPos
             << "終點位置=" << endPos
             << "起始時間=" << startTime
             << "終止時間=" << endTime
             << "速度=" << speed
             << "起始速度=" << startVelocity
             << "終端速度=" << endVelocity;
} 