<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>RobotArm</class>
 <widget class="QDialog" name="RobotArm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>916</width>
    <height>572</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>1510A主命令</string>
  </property>
  <property name="styleSheet">
   <string notr="true">background-color: rgb(217, 217, 217);</string>
  </property>
  <widget class="QWidget" name="horizontalLayoutWidget">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>20</y>
     <width>841</width>
     <height>31</height>
    </rect>
   </property>
   <layout class="QHBoxLayout" name="horizontalLayout">
    <item>
     <widget class="QLabel" name="label">
      <property name="text">
       <string>TCP_IP:</string>
      </property>
     </widget>
    </item>
    <item>
     <spacer name="verticalSpacer_2">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <widget class="QLineEdit" name="lineEdit_RIP">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
     </widget>
    </item>
    <item>
     <spacer name="verticalSpacer_3">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <widget class="QLabel" name="label_2">
      <property name="text">
       <string>Port:</string>
      </property>
     </widget>
    </item>
    <item>
     <spacer name="verticalSpacer_4">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <widget class="QLineEdit" name="lineEdit_RPORT">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
     </widget>
    </item>
    <item>
     <spacer name="verticalSpacer">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <widget class="QPushButton" name="pushButton_RConnect">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(85, 170, 255);</string>
      </property>
      <property name="text">
       <string>Connect</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QWidget" name="verticalLayoutWidget">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>110</y>
     <width>391</width>
     <height>401</height>
    </rect>
   </property>
   <layout class="QVBoxLayout" name="verticalLayout">
    <item>
     <widget class="QLabel" name="label_3">
      <property name="text">
       <string>Common Test:</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QTextEdit" name="textEdit_Rbsend">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
     </widget>
    </item>
    <item>
     <spacer name="horizontalSpacer_2">
      <property name="orientation">
       <enum>Qt::Horizontal</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>40</width>
        <height>20</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <widget class="QPushButton" name="pushButton_RSendTest_Common">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(85, 170, 255);</string>
      </property>
      <property name="text">
       <string>Send Test</string>
      </property>
     </widget>
    </item>
    <item>
     <spacer name="horizontalSpacer_4">
      <property name="orientation">
       <enum>Qt::Horizontal</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>40</width>
        <height>20</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <widget class="QLabel" name="label_5">
      <property name="text">
       <string>Common</string>
      </property>
     </widget>
    </item>
    <item>
     <spacer name="horizontalSpacer">
      <property name="orientation">
       <enum>Qt::Horizontal</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>40</width>
        <height>20</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <widget class="QTextEdit" name="textEdit_RealCommonRb">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 127);</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QWidget" name="verticalLayoutWidget_2">
   <property name="geometry">
    <rect>
     <x>440</x>
     <y>110</y>
     <width>421</width>
     <height>401</height>
    </rect>
   </property>
   <layout class="QVBoxLayout" name="verticalLayout_2">
    <item>
     <widget class="QLabel" name="label_4">
      <property name="text">
       <string>Back_Massage:</string>
      </property>
     </widget>
    </item>
    <item>
     <spacer name="horizontalSpacer_3">
      <property name="orientation">
       <enum>Qt::Horizontal</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>40</width>
        <height>20</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <widget class="QTextEdit" name="textEdit_R_Back_Massage">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QWidget" name="horizontalLayoutWidget_2">
   <property name="geometry">
    <rect>
     <x>19</x>
     <y>60</y>
     <width>451</width>
     <height>41</height>
    </rect>
   </property>
   <layout class="QHBoxLayout" name="horizontalLayout_2">
    <item>
     <widget class="QLabel" name="label_RChec_Connect">
      <property name="text">
       <string>Check_Connect</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QPushButton" name="pushButton_sendMain">
   <property name="geometry">
    <rect>
     <x>160</x>
     <y>520</y>
     <width>93</width>
     <height>28</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(255, 85, 255);</string>
   </property>
   <property name="text">
    <string>sendMain</string>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
