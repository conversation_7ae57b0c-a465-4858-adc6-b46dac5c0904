#include "add_stepmotor_action.h"
#include "ui_add_stepmotor_action.h"

#include <QThread>
#include <QDebug>
#include <QTimer>
#include <QTextEdit>



// 构造函数
add_stepMotor_action::add_stepMotor_action(QWidget *parent)
    : QDialog(parent)
    , ui(new Ui::add_stepMotor_action)
{
    ui->setupUi(this);
    //setupSerialPort(); // 配置串行端口

    //状态下拉框
     ui->startComboBox->addItem("Y");
     ui->startComboBox->addItem("N");
     //设置了按钮的 clicked 信号连接到了 on_pushButton_Addaction_clicked 槽函数
     connect(ui->pushButton_Addaction, SIGNAL(clicked()), this, SLOT(on_pushButton_Addaction_clicked()), Qt::UniqueConnection);
     connect(ui->pushButton_add_to_mainwindow, &QPushButton::clicked, this, &add_stepMotor_action::on_pushButton_add_to_mainwindow_clicked);



}




// 析构函数
add_stepMotor_action::~add_stepMotor_action()
{
    serial.close(); // 确保在析构函数中关闭串行端口
    delete ui;
}
void add_stepMotor_action::setDeviceName(const QString &name) {
    ui->device_name->setText(name);

}
void add_stepMotor_action::setDeviceType(const QString &type) {
    ui->device_name_2->setText(type);
}



//接收從dialog.cpp傳送過來的。串行通訊的資料。
void add_stepMotor_action::setSerialPortSettings(const QString &portName, qint32 baudRate, QSerialPort::DataBits dataBits, QSerialPort::Parity parity, QSerialPort::StopBits stopBits, QSerialPort::FlowControl flowControl) {
    serial.setPortName(portName);
    serial.setBaudRate(baudRate);
    serial.setDataBits(dataBits);
    serial.setParity(parity);
    serial.setStopBits(stopBits);
    serial.setFlowControl(flowControl);
}

//電機控制協議建造
//void add_stepMotor_action::sendCommand(int speed, int duration, bool start) {
    //QString command = QString("%1:%2:%3").arg(speed).arg(duration).arg(start ? "Y" : "N");

    // 组织基础信息
    //QString baseMessage = QString("Device Name: \"%1\", Device Type: \"%2\"")
                              //.arg(ui->device_name->text())
                              //.arg(ui->device_name_2->text());

    //QString portStatusMessage;
    //if (!serial.isOpen()) {
        //if (!serial.open(QIODevice::ReadWrite)) {
            //portStatusMessage = " - Serial Port Status: Failed to open.";
        //} else {
            //portStatusMessage = " - Serial Port Status: Opened successfully.";
       // }
    //} else {
       // portStatusMessage = " - Serial Port Status: Already open.";
    //}

    // 尝试发送命令
    //QString commandStatusMessage = serial.isOpen() && serial.write(command.toUtf8()) != -1 ?
                                       //QString(" - Added command: %1% for %2 seconds. Start: %3").arg(speed).arg(duration).arg(start ? "Yes" : "No") :
                                      // " - Error: Failed to send command.";

    // 组合消息并显示
    //qDebug() << baseMessage << portStatusMessage << commandStatusMessage;
//}




//点击pushButton_addaction时，创建一个新的MotorCommand并添加到列表中。
void add_stepMotor_action::addCommand(int speed, int duration, bool start) {
    QString portStatusMessage;
    // 检查串口状态并获取串口名称
    QString portName = serial.portName(); // 获取当前串口名称

    if (!serial.isOpen()) {
        portStatusMessage = "Serial Port Status: Not open.";
    } else {
        portStatusMessage = "Serial Port Status: Open.";
    }

    // 生成和保存命令
    QString commandString = QString("%1:%2:%3").arg(speed).arg(duration).arg(start ? "Y" : "N");
    MotorCommand cmd { speed, duration, start, commandString };
    commands.append(cmd);

    // 获取设备名称和类型
    QString deviceName = ui->device_name->text();
    QString deviceType = ui->device_name_2->text();

    // 在调试输出中显示命令和设备信息，串口状态及串口名称
    qDebug() << "Device Name:" << deviceName << ", Device Type:" << deviceType
             << ", Serial Port:" << portName // 显示串口名称
             << " - Added command:" << cmd.speed << "% for" << cmd.duration << "seconds. Start:" << (cmd.start ? "Yes" : "No")
             << ", Command String:" << cmd.commandString << ", " << portStatusMessage;
}

void add_stepMotor_action::on_pushButton_Addaction_clicked() {
    qDebug() << "on_pushButton_Addaction_clicked called";
    int speed = ui->speedLineEdit->text().toInt(); // 获取速度值
    int duration = ui->durationLineEdit->text().toInt(); // 获取持续时间
    bool start = (ui->startComboBox->currentText() == "Y"); // 获取是否启动

    // addCommand 函数的调用
    addCommand(speed, duration, start);


}

//點擊pushButton_add_to_mainwindow事件
void add_stepMotor_action::on_pushButton_add_to_mainwindow_clicked() {

    // 获取设备名称和类型
    QString deviceName = ui->device_name->text();
    QString deviceType = ui->device_name_2->text();
    // 获取串口状态
    QString portStatus = serial.isOpen() ? "Open" : "Not open";
    QString portName = serial.portName();

    QString summary;
    // 每个命令前都添加设备信息，确保格式一致
    foreach (const MotorCommand &cmd, commands) {    //foreach 循环遍历 commands 列表
        summary += QString("Device Name: %1, Device Type: %2, Serial Port: %3 - %4\n")
                       .arg(deviceName)
                       .arg(deviceType)
                       .arg(portName)
                       .arg(portStatus);
        summary += QString("Speed: %1, Duration: %2, Start: %3\n")
                       .arg(cmd.speed)
                       .arg(cmd.duration)
                       .arg(cmd.start ? "Yes" : "No");
    }
    qDebug() << "Emitting signal with summary:" << summary; // 在发射信号之前添加调试输出
    emit requestUpdateTextSummary(summary); // 发射信号使用 emit 关键字发射 requestUpdateTextSummary 信号，并将 summary 作为参数传递
}
