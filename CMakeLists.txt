cmake_minimum_required(VERSION 3.16)

# 重置並設置基本變量
unset(CMAKE_PROJECT_INCLUDE_BEFORE CACHE)
unset(CMAKE_PROJECT_INCLUDE CACHE)

# 設置 Qt 路徑
set(CMAKE_PREFIX_PATH "E:/Qt/6.6.1/mingw_64")
set(Qt6_DIR "E:/Qt/6.6.1/mingw_64/lib/cmake/Qt6")

# 設置項目
project(sewingtop VERSION 0.1 LANGUAGES C CXX)

# 設置構建類型
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Debug)
endif()

# 設置編譯器標誌
set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g")
set(CMAKE_C_FLAGS_DEBUG "${CMAKE_C_FLAGS_DEBUG} -g")

# 設置 C++ 標準
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_C_STANDARD 99)
set(CMAKE_C_STANDARD_REQUIRED ON)

# Qt 自動化設置
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# 添加 SOEM 库 (移到 Qt 之前)
add_subdirectory(external/SOEM)

# 添加 Ruckig 庫
add_subdirectory(ruckig-main)

# Npcap SDK 路径设置
if(WIN32)
    if(CMAKE_SIZEOF_VOID_P EQUAL 8)
        set(NPCAP_SDK_DIR "C:/Program Files/Npcap/SDK" CACHE PATH "Path to Npcap SDK")
        set(NPCAP_LIB_DIR "${NPCAP_SDK_DIR}/Lib/x64")
    else()
        set(NPCAP_SDK_DIR "C:/Program Files (x86)/Npcap/SDK" CACHE PATH "Path to Npcap SDK")
        set(NPCAP_LIB_DIR "${NPCAP_SDK_DIR}/Lib")
    endif()

    include_directories(BEFORE
        "${CMAKE_CURRENT_SOURCE_DIR}/external/SOEM/soem"
        "${CMAKE_CURRENT_SOURCE_DIR}/external/SOEM/osal"
        "${CMAKE_CURRENT_SOURCE_DIR}/external/SOEM/oshw"
        "${CMAKE_CURRENT_SOURCE_DIR}/external/SOEM/osal/win32"
        "${CMAKE_CURRENT_SOURCE_DIR}/external/SOEM/oshw/win32"
        "${CMAKE_CURRENT_SOURCE_DIR}/external/SOEM"
        "${NPCAP_SDK_DIR}/Include"
    )

    add_definitions(
        -D_SAL_VERSION=20
        -DSAL_NO_ATTRIBUTE_DECLARATIONS
        -DSIMPLE_PACKET32
        -DHAVE_REMOTE
        -DWPCAP
        -DPCAP_DONT_INCLUDE_PCAP_BPF_H
        -DWIN32
        -DWIN32_LEAN_AND_MEAN
        -D_WIN32_WINNT=0x0601
        -D_CRT_SECURE_NO_WARNINGS
        -D_WINSOCK_DEPRECATED_NO_WARNINGS
    )
endif()

# 添加 GoogolTech 庫路徑
include_directories(BEFORE
    "${CMAKE_CURRENT_SOURCE_DIR}/LibraryX64"
    "${CMAKE_CURRENT_SOURCE_DIR}/ruckig-main/include"
    # ... 其他現有的包含目錄
)

# 如果是 64 位系統，添加庫目錄
if(CMAKE_SIZEOF_VOID_P EQUAL 8)
    link_directories(
        "${CMAKE_CURRENT_SOURCE_DIR}/LibraryX64"
    )
endif()

# 查找 Qt6 包 (只保留一次)
find_package(Qt6 COMPONENTS Core Gui Widgets Network SerialPort Concurrent REQUIRED)

# 設置源文件
set(PROJECT_SOURCES
    main.cpp
    mainwindow.cpp
    mainwindow.h
    mainwindow.ui
    dialog.h
    dialog.cpp
    dialog.ui
    ethercat.cpp
    ethercat.h
    ethercat.ui
    EtherCATMaster.cpp
    EtherCATMaster.h
    dialog_plc.h
    dialog_plc.cpp
    dialog_plc.ui
    loic.h
    loic.cpp
    loic.ui
    sensor.h
    sensor.cpp
    sensor.ui
    robotarm.h
    robotarm.cpp
    robotarm.ui
    sensor_485.h
    sensor_485.cpp
    sensor_485.ui
    d_size.h
    d_size.cpp
    d_size.ui
    commandscheduler.h
    commandscheduler.cpp
    commandscheduler.ui
    add_stepmotor_action.h
    add_stepmotor_action.cpp
    add_stepmotor_action.ui
    # 添加新的多軸並行計算相關文件
    AxisWorker.h
    AxisWorker.cpp
    AxisSynchronizer.h
    AxisSynchronizer.cpp
    executeParallelCommands.cpp
    handleAxisError.cpp
    modifyExecuteCommandSequence.cpp  # 如果需要的話
)

# 創建可執行檔
if(QT_VERSION_MAJOR EQUAL 6)
    qt_add_executable(${PROJECT_NAME}
        MANUAL_FINALIZATION
        ${PROJECT_SOURCES}
    )
else()
    add_executable(${PROJECT_NAME}
        ${PROJECT_SOURCES}
    )
endif()

# 設置 Windows 特定屬性
if(WIN32)
    set_target_properties(${PROJECT_NAME} PROPERTIES
        WIN32_EXECUTABLE TRUE
        VS_MANIFEST_UAC_EXECUTION_LEVEL "requireAdministrator"
        VS_MANIFEST_UAC_BYPASS_UIACCESS "true"
        LINK_FLAGS "-Wl,--subsystem,windows"
    )
endif()

# 設置庫文件的完整路徑
set(GTS_LIB_PATH "${CMAKE_CURRENT_SOURCE_DIR}/LibraryX64")

# 檢查庫文件是否存在（同時檢查兩種可能的文件名）
if(EXISTS "${GTS_LIB_PATH}/gts.lib")
    set(GTS_LIB_NAME "gts.lib")
elseif(EXISTS "${GTS_LIB_PATH}/gts64.lib")
    set(GTS_LIB_NAME "gts64.lib")
else()
    message(FATAL_ERROR "找不到 gts.lib 或 gts64.lib，請確認文件位置：${GTS_LIB_PATH}")
endif()

# 鏈接庫
target_link_libraries(${PROJECT_NAME} PRIVATE
    Qt6::Core
    Qt6::Gui
    Qt6::Widgets
    Qt6::Network
    Qt6::SerialPort
    Qt6::Concurrent
    soem
    ruckig
    "${NPCAP_LIB_DIR}/wpcap.lib"
    "${NPCAP_LIB_DIR}/Packet.lib"
    ws2_32
    iphlpapi
    setupapi
    winmm
    "${GTS_LIB_PATH}/${GTS_LIB_NAME}"
)

# 添加日誌目錄配置
if(CMAKE_BUILD_TYPE STREQUAL "Release")
    # Release 模式下的日誌目錄配置
    add_compile_definitions(LOG_DIR="${CMAKE_INSTALL_PREFIX}/logs")
    
    # 確保安裝時創建日誌目錄
    install(DIRECTORY DESTINATION "${CMAKE_INSTALL_PREFIX}/logs"
            DIRECTORY_PERMISSIONS OWNER_READ OWNER_WRITE OWNER_EXECUTE
                                GROUP_READ GROUP_WRITE GROUP_EXECUTE
                                WORLD_READ WORLD_WRITE WORLD_EXECUTE)
else()
    # Debug 模式下的日誌目錄配置
    add_compile_definitions(LOG_DIR="${CMAKE_BINARY_DIR}/logs")
endif()

# 添加版本信息
add_compile_definitions(
    APP_VERSION="${PROJECT_VERSION}"
    QT_VERSION="${Qt6_VERSION}"
)

# 複製 DLL 到輸出目錄（同樣檢查兩種可能的文件名）
if(EXISTS "${GTS_LIB_PATH}/gts.dll")
    set(GTS_DLL_NAME "gts.dll")
elseif(EXISTS "${GTS_LIB_PATH}/gts64.dll")
    set(GTS_DLL_NAME "gts64.dll")
endif()

if(DEFINED GTS_DLL_NAME)
    add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
            "${GTS_LIB_PATH}/${GTS_DLL_NAME}"
            $<TARGET_FILE_DIR:${PROJECT_NAME}>
    )
endif()

# 安裝配置
include(GNUInstallDirs)
install(TARGETS ${PROJECT_NAME}
    BUNDLE DESTINATION .
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
)

if(QT_VERSION_MAJOR EQUAL 6)
    qt_finalize_executable(${PROJECT_NAME})
endif()

include_directories(${CMAKE_SOURCE_DIR}/libs/tk-spline)
