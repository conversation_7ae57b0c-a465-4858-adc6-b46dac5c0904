/**
 * @file ethercat.h
 * @brief EtherCAT通信和驅動器控制類定義
 * 
 * 該類提供：
 * - EtherCAT主站功能
 * - 驅動器狀態管理
 * - 運動控制接口
 * - UI交互功能
 */

#ifndef ETHERCAT_H
#define ETHERCAT_H

// Windows headers first
#include <windows.h>
#include <setupapi.h>

#ifdef NULL
#undef NULL
#endif

// Now include gts.h
#include "LibraryX64/gts.h"

// Qt headers
#include <QWidget>
#include <QInputDialog>
#include <QMessageBox>
#include <QProcess>
#include <QTimer>
#include <QTcpSocket>
#include <QThread>
#include <QJsonDocument>
#include <QJsonObject>
#include <QFile>
#include <QQueue>

// Project headers
#include "EtherCATMaster.h"
#include "AxisWorker.h"
#include "AxisSynchronizer.h"


#include <QRegularExpression>
#include <QStandardPaths>

// 定義核心索引
#define CORE                1

/**
 * @enum DriveStatus
 * @brief 驅動器狀態枚舉
 * 
 * 定義了CiA402狀態機的各個狀態：
 * - NOT_READY: 未就緒
 * - SWITCH_ON_DISABLED: 禁止開啟
 * - READY_TO_SWITCH_ON: 準備開啟
 * - SWITCHED_ON: 已開啟
 * - OPERATION_ENABLED: 運行使能
 */
enum DriveStatus {
    DS_NOT_READY,
    DS_SWITCH_ON_DISABLED,
    DS_READY_TO_SWITCH_ON,
    DS_SWITCHED_ON,
    DS_OPERATION_ENABLED,
    DS_QUICK_STOP_ACTIVE,
    DS_FAULT_REACTION_ACTIVE,
    DS_FAULT
};

/**
 * @struct DriveCommand
 * @brief 驅動器命令結構
 * 
 * @var type 命令類型
 * @var targetValue 目標值
 * @var slavePosition 從站位置
 * @var data 命令數據
 */
struct DriveCommand {
    enum Type {
        ENABLE_OPERATION,
        SET_POSITION,
        SET_VELOCITY,
        QUICK_STOP,
        FAULT_RESET,
        DISABLE_OPERATION,
        SWITCH_ON,
        SWITCH_OFF
    };
    Type type;
    int targetValue;
    int slavePosition;
    QByteArray data;
};

// 在 DriveCommand 結構體後添加
struct PointToPointMotion {
    int startPosition;
    int endPosition;
    int speed;
};

namespace Ui {
class ethercat;
}

struct MultiAxisMotion {
    short axis;
    double endPosition;
    double speed;
};

// 定義軸參考位置結構
struct AxisReferencePosition {
    short axis;              // 軸號
    double homePosition;     // 原點位置
    double currentPosition;  // 當前位置
    double softLimitPos;     // 正向軟限位
    double softLimitNeg;     // 負向軟限位
    bool isReferenced;       // 是否已回原點
};

class ethercat : public QWidget
{
    Q_OBJECT

public:

    static QMap<int, double> s_axisLastPos;
    static QMap<int, double> s_axisLastTime;
    // PVT相關結構體
    struct PVTPoint {
        double time;
        double pos;
        double vel;
    };

    struct PVTMotion {
        QString axisName;
        double startPos;
        double endPos;
        double startTime;
        double endTime;
        double speed;
        short axis;
        double startVelocity = 0;  // 新增
        double endVelocity = 0;    // 新增
    };

    struct PVTProfile {
        QVector<PVTPoint> points;
        int currentIndex;
        bool isActive;
    };

    // PVT相關函數
    void processSensorCommand(const QString& command);

     // PVT數據上傳函數
    void uploadPVTData(short axis, short tableId = -1);

       // 添加新函數的聲明
    void executeCommandSequenceNew(const QStringList& commands);
    
    void parsePVTCommand(const QString& command);
    void executePVTMotions();
    void monitorPVTMotions();
    //QVector<PVTPoint> calculatePVTPoints(const PVTMotion& motion);
    bool validatePVTMotions();
    void setupPVTProfile(const PVTMotion& motion);

    void restoreAxisPositions();
    explicit ethercat(QWidget *parent = nullptr);
    ~ethercat();

    // 檢查運動狀態
    void checkMotionStatus(short axis);
    
    // 獲取當前位置和速度
    double getCurrentPosition(short axis);
    double getCurrentVelocity(short axis);

    
    // 添加closeEvent聲明
    void closeEvent(QCloseEvent *event) override;

    //void initializeCard();
    QString getName() const;  // 只保留聲明
    void sendCommand(const QString& command);

    // 在 class ethercat 的 public 部分添加
    void executePointToPointMotion(const QString& command);
    void parsePointToPointCommand(const QString& command, PointToPointMotion& motion);

    //void setCommandQueue(const QStringList& commands);
    //void startNextCommand();

    void executeMultiAxisMotion(const QList<MultiAxisMotion>& motions);
    static void waitForAllAxesComplete(const QList<short>& axes);
    static bool enableMultiAxis(const QList<short>& axes);

    // 修改為純聲明
    short getCurrentAxis() const;
    
    // 添加靜態輔助函數聲明
    static bool enableAxisStatic(short axis);
    
    // 保留一個 globalCardCore 聲明
    static short globalCardCore;

    // 添加靜態回調函數指針
    static std::function<void()> motionCompletedCallback;

    // 修改為靜態函數
    static bool initializeAxisReference(short axis);
    static bool updateAxisReference(short axis);
    static AxisReferencePosition getAxisReference(short axis);
    bool moveToHomePosition(short axis);
    bool checkAxisInRange(short axis, double position);

    bool isInitialized() const { return isCardInitialized; }

    void sendPVTCommands(const QStringList& commands);
    

    // 添加動態修改PVT點的函數
    void modifyPVTPoint(short axis, int pointIndex, double newPosition, double newVelocity);

    // 批量執行命令序列
    void executeCommandSequence(const QStringList& commands);

    // 根據軸名稱獲取對應的軸號
    short getAxisNumber(const QString& axisName) const;

    // 添加新的監控函數聲明
    void monitorPVTMotion();
    void logAxisRealTimeData(short axis);
    //void waitForPVTCompletion(long mask);

    // 添加並行處理相關函數
    void executeParallelCommands(const QStringList& commands);
    void handleAxisError(short axis, const QString& errorMessage);
    
    // 添加配置選項
    void setParallelCalculation(bool enable) { useParallelCalculation = true; }
    bool isParallelCalculationEnabled() const { return useParallelCalculation; }

    // superPT在 public: 部分添加以下聲明
    void SUPERPT_1(const QString& commandString);
    void SUPERPT_2();
    void SUPERPT_3();
    void SUPERPT_4();

    // 添加一個方法用於訪問 Super2_finaldata
    static QMap<QString, QPair<int, QStringList>> getSuperPT2Data() { return Super2_finaldata; }

    void startFifoMonitor(); // FIFO監控函數
    void processNextSPTCommand(int axisNumber, const QString& axisCode); // 處理下一個命令

    // 軟限位設置（支援減速比）
    void setAxisSoftLimit(short axis, double gearRatio = 1.0, double pulsePerRevolution = 131072.0, double limitDegree = 140.0, double centerPulse = 0.0);

    // 軟限位初始化（系統啟動時調用一次）
    void initializeAllAxisSoftLimits();

    // 軟限位檢查函數
    bool checkAxisSoftLimit(short axis, double position);

    // 公有接口函數，用於外部調用歸零功能
    void executeAxisHoming();

    // 設置軸目標位置的公有函數
    void setAxisTargetPosition(short axis, long targetPosition);

    // 初始化軸目標位置的函數
    void initializeAxisTargetPositions();

signals:
    void connectionStatusChanged(bool connected);
    void windowClosed();
    void motionCompleted();
    void commandSequenceCompleted();  // 命令序列完成信號
    void softLimitCorrected(int axis, double originalPosition, double correctedPosition); // 軟限位觸發通知

private slots:

//    void on_pushButton_pp_Position_clicked();
    void on_pushButton_move_clicked();  // 添加這個槽函數
    void onNameChanged(const QString& name);
    void setUniqueName();
    void onConnected();
    void onDisconnected();
    void onError(QAbstractSocket::SocketError socketError);
    void onReadyRead();
    void on_pushButton_ethercat_connect_clicked();
    void on_pushButton_ethercat_send_m_clicked();
    void on_pushButton_refresh_drives_clicked();
    void onDriveSelected(int index);
   
    
    void onMoveButtonClicked();
    void processNextMotion();

    void on_pushButton_pp_Position_clicked();  // 設置絕對位置按鈕槽函數
    void on_pushButton_backhome_clicked();
    void on_pushButton_go_0_clicked();  // 歸零按鈕槽函數

private:
    bool isWaitingForNext = false; 
    // 保存絕對位置相關
    QMap<int, long> absolutePositions;  // 軸號 -> 絕對位置
    QString getPositionsFilePath();
    void savePositionsToFile();
    void loadPositionsFromFile();
    
    
    static bool isCardGloballyInitialized;  // 新增靜態成員
    bool initializeCompensation(short axis);  // 新增的補償初始化函數
    //bool compensatePosition(short axis, double targetPosition, double actualPosition);
    QTimer* motionTimer = nullptr;
    QTimer *initTimer;  // 添加用於初始化的計時器
    void setupLogging();  // 添加這一行
    bool isClosing = false;
    bool isCardInitialized;  // 追蹤卡的初始化狀態
    QString lastKnownStatus; // 保存最後已知的狀態
    QString getCurrentStatus();
    QString getActiveConnections();
    void cleanupOldLogs(const QString& logPath, int daysToKeep);
    void logCloseEvent(const QString& stage, const QString& message);
    void updateDriveStatus(bool readFromCard = true);  // 統一的狀態更新函數
    //void updateDriveStatus();  // 無參數版本用於定時更新
    //void updateDriveStatus(int status);  // 帶參數版本用於響應處理
    bool enableAxis(short axis);
    void logError(const QString& message, short errorCode, short axis = -1, long status = 0);

    // 增強的軸狀態檢查函數
    bool isAxisReadyForHoming(short axis);
    QString getAxisStatusDescription(long status);
    static int instanceCount;  // 添加靜態成員聲明
    Ui::ethercat *ui;
    QTimer *driveDetectionTimer;
    QTimer *cyclicTimer;
    QTcpSocket *socket;
    EtherCATMaster ecMaster;
    
    void setupDriveUI();
    
    
    
    
    void detectDrives();
    QByteArray createEtherCATFrame(uint8_t command, uint16_t index, uint8_t subIndex, const QByteArray &data);
    void sendDriveCommand(const DriveCommand &cmd);
    void transitionToDriveState(DriveStatus targetState);
    void handleDriveResponse(const QByteArray &data);
    bool checkAndRequestAdminRights();
    void handleAdminRightsRequest();
    
   
    
    int32_t currentPosition;
    long currentStatus;  // 添加當前狀態成員變量
    
    void moveToPosition(int position, int velocity);
    void updatePositionDisplay(int32_t position);
    QByteArray createDriveCommand(uint16_t index, uint8_t subIndex, const QByteArray &data, int slavePosition);
    void sendCommandToDrive(const QByteArray &command, int slavePosition);
    void detectMotionCards();
    bool isRunningAsAdmin();
    QString getAdminPassword();
    bool isValidDeviceId(const QString& hardwareId);
    void logDeviceError(const QString& errorMessage);
    
    short cardCore = 1;
    void closeCard();
    bool initializeAxis(short axis);
    
    QString getErrorMessage(short errorCode);
    
    bool checkDriveInitialization(int driveIndex);
    QTimer *driveStatusTimer;
    
   
    // 添加新的函數聲明
    //void initializeDrive(short axis);
    bool checkDriveStatus(short axis);

    // 運動參數
    double acceleration;    // 加速度
    double deceleration;   // 減速度
    double targetPosition; // 目標位置
    double targetVelocity; // 目標速度
    int smoothTime;        // 平滑時間

    struct MotionSegment {
        double startTime;    // 開始時間（秒）
        double endTime;      // 結束時間（秒）
        double velocity;     // 目標速度（rpm）
        int direction;       // 方向：1正向，-1反向
        bool continuePrevious = false;  // 是否繼承前一個命令的速度，預設為 false
    };
    
    void executeMotionSegment(const MotionSegment &segment);
    void setupMotionProfile();
    QVector<MotionSegment> motionSegments;
    //QTimer *motionTimer;

    struct TimeControlledMotion {
        QString windowName;
        double startTime;    // 開始時間（秒）
        double endTime;      // 結束時間（秒）
        double velocity;     // 速度（pulse/s）
        int direction;       // 方向：1正向，-1反向
        bool continuePrevious = false;  // 是否繼承前一個命令的速度，預設為 false
        double targetPosition; // 新增：目標位置
    };
    
    void executeTimeControlledMotion(const TimeControlledMotion& motion);
    void parseMotionCommand(const QString& command);
    
    QVector<TimeControlledMotion> motionQueue;
    int currentMotionIndex;
    QElapsedTimer elapsedTimer;

    QString getAxisStatusText(long status);
    void updateStatusDisplay(short axis, long status, const QString& statusText, const QString& positionInfo);
    void updateStatusColor(long status);

    QStringList pendingCommands;
    void executeNextCommand();
    bool isExecutingCommand = false;

    static QMap<short, bool> axisMotionComplete;

    // 改為靜態成員
    static QMap<short, AxisReferencePosition> axisReferences;

    // PVT相關成員
    QMap<QString, PVTProfile> pvtProfiles;
    QList<PVTMotion> pendingPVTMotions;

    QQueue<QString> commandQueue;  // 添加命令隊列

    struct MultiSegmentPVT {
        QList<PVTMotion> segments;  // 存儲多個PVT段
        short axis;                 // 軸號
        QString axisName;           // 軸名稱
    };

    QMap<short, MultiSegmentPVT> multiSegmentPVTs;  // 用於存儲多段PVT數據

    void executeNextPVTCommand();

    // 在 MultiSegmentPVT 結構體後添加新結構體
    struct CommandSequence {
        QList<QString> rawCommands;     // 原始命令字符串
        int currentIndex = 0;           // 當前執行到的命令索引
    };

    // 在 ethercat 類的私有成員變數部分添加
    CommandSequence currentSequence;    // 當前的命令序列
    bool isExecutingSequence = false;   // 是否正在執行序列
    QQueue<CommandSequence> pendingSequences;  // 等待執行的序列隊列

    void executeNextSequence();
    
    short getAxisNumber() const { 
        // 從 PVTMotion 結構中獲取軸號
        // 或者返回當前軸號，這裡我們返回 cardCore 作為替代
        return cardCore; 
    }

    // 在 ethercat 類的私有成員變數部分添加
    QMap<short, PVTDataCache> axisPVTCache;  // 軸緩存映射

   
    // 批量解析命令到緩存
    void parseCommandToCache(const QString& command);

    // 監控所有PVT運動
    void monitorAllPVTMotions();
    // 計算PVT點函數重載版本
    //QVector<PVTPoint> calculatePVTPoints(double startPos, double endPos, 
                                       //double startTime, double endTime, double speed);

    //bool initAxis(short axis);  // 新增軸初始化函數

    // 計算終端速度的函數
    //double calculateEndVelocity(double startPos, double endPos, double speed);

    // 支持速度繼承的PVT點計算函數
    //QVector<PVTPoint> calculatePVTPoints(double startPos, double endPos, 
                                       //double startTime, double endTime, double speed,
                                       //double startVelocity = 0, double endVelocity = 0);

    // 表單ID對結構，用於管理每個軸的兩個表單ID
    struct TableIdPair {
        short primaryId;    // 主表單ID
        short secondaryId;  // 次表單ID
        bool usingPrimary;  // 當前是否使用主表單
    };

    // 軸與表單ID的映射
    QMap<short, TableIdPair> axisTableIds;

    // 當前命令隊列
    QMap<short, QQueue<QString>> axisCommandQueues;

    // 軸命令準備狀態
    QMap<short, bool> axisCommandReady;

    // 表單ID管理函數
    void initializeTableIds();
    short getCurrentTableId(short axis);
    short getNextTableId(short axis);
    void switchToNextTable(short axis);

    // 命令隊列管理函數
    void addCommandToQueue(short axis, const QString& command);
    void parseCommandsToQueues(const QStringList& commands);
    bool prepareNextCommand(short axis);

    // 修改uploadPVTData函數聲明，添加可選的tableId參數
    //void uploadPVTData(short axis, short tableId = -1);

    // 並行計算選項
    bool useParallelCalculation = true;  // 默認啟用並行計算

    // 在ethercat.h中添加
    QMap<short, int> totalAxisCommands;  // 記錄每個軸的命令總數
    QMap<short, int> executedAxisCommands;  // 記錄每個軸已執行的命令數

    void checkCardPerformance();  // 添加性能診斷函數聲明

    // 在private部分的函數聲明中添加
    void setupWatchMonitoring();
    void startWatchMonitoring();
    void stopWatchMonitoring();
    //void analyzeWatchData(const QString& filename);
    //QString getWatchDataFilePath();
    
    // 其他成員變數
    bool isWatchMonitoringActive = false;
    QTimer* watchDataTimer = nullptr;

    // 用於存儲每個軸的命令隊列和線程
    //QMap<short, QStringList> axisCommandQueues;  // 軸號 -> 命令隊列
    QMap<short, QThread*> axisThreads;           // 軸號 -> 線程
    QMap<short, QObject*> axisWorkers;           // 軸號 -> 工作對象
    QMutex commandQueueMutex;                    // 保護命令隊列的互斥鎖
    bool isPTInitialized = false;                // 是否已初始化PT系統
    
    // 添加新的私有方法
    void processPTCommandQueue(short axis);

    // 在 private: 部分添加以下靜態成員變數
    static QMap<short, QThread*> s_ptThreads;        // 軸 -> 線程映射
    static QMap<short, QObject*> s_ptObjects;        // 軸 -> 物件映射
    static bool s_ptThreadsInitialized;              // 線程是否已初始化標記
    static QMutex s_ptMutex;                         // 互斥鎖，保護命令添加

    // 添加以下靜態成員變數聲明
    static QMap<QString, QStringList> Super1_finaldata; // 存儲所有累積的命令
    static QMutex s_commandMutex; // 用於保護多線程訪問
    static QTimer* s_autoAnalysisTimer; // 計時器用於自動調用 SUPERPT_2
    
    // 添加 Super2_finaldata 靜態變量聲明
    static QMap<QString, QPair<int, QStringList>> Super2_finaldata; // 存儲分析結果

    // 添加這些靜態成員變量的聲明
    static QMap<QString, QThread*> s_axisThreads; // 存儲每個軸的線程
    static QMap<QString, QQueue<QString>> s_axisCommandQueues; // 存儲每個軸的命令隊列

    // 在ethercat.h類定義中添加
    static QMap<int, bool> axisProcessingNext;
    
    // 存儲各軸絕對位置差異的變量
    QMap<short, long> axisAbsolutePositions;
    QMap<short, long> axisTargetPositions;  // 軸目標位置映射，如果沒有設定則默認為0
};

#endif // ETHERCAT_H
