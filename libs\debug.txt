[2025-06-28 16:11:41.665] Debug: MainWindow: pushButton_go_0 按鈕被點擊
[2025-06-28 16:11:41.665] Debug: ===== [軸運動] 開始執行所有軸運動到目標位置 =====
[2025-06-28 16:11:41.665] Debug: "設置軸 1 目標位置為: 40997"
[2025-06-28 16:11:41.665] Debug: "設置軸 2 目標位置為: 159256"
[2025-06-28 16:11:41.665] Debug: "設置軸 3 目標位置為: 226819"
[2025-06-28 16:11:41.665] Debug: "設置軸 4 目標位置為: -513340"
[2025-06-28 16:11:41.665] Debug: "設置軸 5 目標位置為: 110795"
[2025-06-28 16:11:41.665] Debug: "設置軸 6 目標位置為: 55068"
[2025-06-28 16:11:41.665] Debug: "設置軸 7 目標位置為: -50000"
[2025-06-28 16:11:41.665] Debug: ===== 軸目標位置初始化完成 =====
[2025-06-28 16:11:41.665] Debug: "開始軸 1 歸零運動，當前絕對位置: 40997"
[2025-06-28 16:11:41.717] Debug: "軸 1 開始運動，從位置 40997 運動到 40997"
[2025-06-28 16:11:41.717] Debug: "開始軸 2 歸零運動，當前絕對位置: 159255"
[2025-06-28 16:11:41.769] Debug: "軸 2 開始運動，從位置 159255 運動到 159256"
[2025-06-28 16:11:41.769] Debug: "開始軸 3 歸零運動，當前絕對位置: 226819"
[2025-06-28 16:11:41.821] Debug: "軸 3 開始運動，從位置 226819 運動到 226819"
[2025-06-28 16:11:41.821] Debug: "開始軸 4 歸零運動，當前絕對位置: -513340"
[2025-06-28 16:11:41.873] Debug: "軸 4 開始運動，從位置 -513340 運動到 -513340"
[2025-06-28 16:11:41.873] Debug: "開始軸 5 歸零運動，當前絕對位置: 110876"
[2025-06-28 16:11:41.925] Debug: "軸 5 開始運動，從位置 110876 運動到 110795"
[2025-06-28 16:11:41.925] Debug: "開始軸 6 歸零運動，當前絕對位置: 55063"
[2025-06-28 16:11:41.977] Debug: "軸 6 開始運動，從位置 55063 運動到 55068"
[2025-06-28 16:11:41.977] Debug: "開始軸 7 歸零運動，當前絕對位置: -49999"
[2025-06-28 16:11:42.029] Debug: "軸 7 開始運動，從位置 -49999 運動到 -50000"
[2025-06-28 16:11:42.029] Debug: ===== [軸運動] 所有軸運動指令已發送，開始監控歸位進度 =====
[2025-06-28 16:11:42.029] Debug: "[歸位完成] 軸 1 已停止，歸位結束"
[2025-06-28 16:11:42.029] Debug: "[歸位完成] 軸 2 已停止，歸位結束"
[2025-06-28 16:11:42.029] Debug: "[歸位完成] 軸 3 已停止，歸位結束"
[2025-06-28 16:11:42.030] Debug: "[歸位完成] 軸 4 已停止，歸位結束"
[2025-06-28 16:11:42.030] Debug: "[歸位完成] 軸 5 已停止，歸位結束"
[2025-06-28 16:11:42.030] Debug: "[歸位完成] 軸 6 已停止，歸位結束"
[2025-06-28 16:11:42.051] Debug: "[歸位完成] 軸 7 已停止，歸位結束"
[2025-06-28 16:11:42.051] Debug: ===== [軸運動] 所有軸歸位完成，可以下達 PT 指令 =====
[2025-06-28 16:11:42.252] Debug: "  [SYNC] 開始同步軸 1 到目標位置: 40997"
[2025-06-28 16:11:42.303] Debug: "  [SYNC-VERIFY] 軸 1 驗證結果: EncPos=40997, PrfPos=40997"
[2025-06-28 16:11:42.303] Debug: "  [SYNC-SUCCESS] 軸 1 位置同步成功並已驗證。"
[2025-06-28 16:11:42.303] Debug: "  [SYNC] 開始同步軸 2 到目標位置: 159256"
[2025-06-28 16:11:42.355] Debug: "  [SYNC-VERIFY] 軸 2 驗證結果: EncPos=159256, PrfPos=159256"
[2025-06-28 16:11:42.355] Debug: "  [SYNC-SUCCESS] 軸 2 位置同步成功並已驗證。"
[2025-06-28 16:11:42.355] Debug: "  [SYNC] 開始同步軸 3 到目標位置: 226819"
[2025-06-28 16:11:42.407] Debug: "  [SYNC-VERIFY] 軸 3 驗證結果: EncPos=226819, PrfPos=226819"
[2025-06-28 16:11:42.407] Debug: "  [SYNC-SUCCESS] 軸 3 位置同步成功並已驗證。"
[2025-06-28 16:11:42.407] Debug: "  [SYNC] 開始同步軸 4 到目標位置: -513340"
[2025-06-28 16:11:42.459] Debug: "  [SYNC-VERIFY] 軸 4 驗證結果: EncPos=-513340, PrfPos=-513340"
[2025-06-28 16:11:42.459] Debug: "  [SYNC-SUCCESS] 軸 4 位置同步成功並已驗證。"
[2025-06-28 16:11:42.459] Debug: "  [SYNC] 開始同步軸 5 到目標位置: 110795"
[2025-06-28 16:11:42.510] Debug: "  [SYNC-VERIFY] 軸 5 驗證結果: EncPos=110790, PrfPos=110795"
[2025-06-28 16:11:42.510] Debug: "  [SYNC-FAIL] 軸 %1 位置同步後驗證失敗！Enc/Prf 未能設定到目標值。"
[2025-06-28 16:11:42.510] Debug: "  [SYNC] 開始同步軸 6 到目標位置: 55068"
[2025-06-28 16:11:42.562] Debug: "  [SYNC-VERIFY] 軸 6 驗證結果: EncPos=55069, PrfPos=55068"
[2025-06-28 16:11:42.562] Debug: "  [SYNC-FAIL] 軸 %1 位置同步後驗證失敗！Enc/Prf 未能設定到目標值。"
[2025-06-28 16:11:42.562] Debug: "  [SYNC] 開始同步軸 7 到目標位置: -50000"
[2025-06-28 16:11:42.613] Debug: "  [SYNC-VERIFY] 軸 7 驗證結果: EncPos=-50000, PrfPos=-50000"
[2025-06-28 16:11:42.613] Debug: "  [SYNC-SUCCESS] 軸 7 位置同步成功並已驗證。"