#ifndef DIALOG_H
#define DIALOG_H

#include <QDialog>
#include <QSerialPort> // 包含 QSerialPort 头文件
#include <QSerialPortInfo>
#include "add_stepmotor_action.h"



namespace Ui {
class Dialog;
}

class Dialog : public QDialog
{
    Q_OBJECT

public:
    explicit Dialog(QWidget *parent = nullptr);
    ~Dialog();
signals:
    void requestUpdateTextSummary(const QString &summary);// 新信号
    void forwardUpdateTextSummary(const QString &summary); // 用于从add_stepMotor_action接收信号并转发

private slots:
    void handleSetBaudRateButtonClick();// 槽函数声明
    void handleReadyRead(); // 处理串行数据
    void handleConfigureSerialPort(); //
    void showStepMotorAction();  // 用于显示步进电机窗口的槽函数
    void relayUpdateTextSummary(const QString &summary);

    // 槽函数声明
    //void relayUpdateTextSummary(const QString &summary); // 用于从add_stepMotor_action接收信号并转发


private:
    void configureSerialPort(); // 用于配置串行端口的函数

    Ui::Dialog *ui;
    QSerialPort serialPort; // 创建 QSerialPort 对象
    void populateSerialPortNames(); // 在这里添加声明

    add_stepMotor_action *myStepMotorAction;// 声明第三层的实例

};

#endif // DIALOG_H
