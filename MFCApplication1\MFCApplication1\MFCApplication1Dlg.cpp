﻿
// MFCApplication1Dlg.cpp: 实现文件
//

#include "pch.h"
#include "framework.h"
#include "MFCApplication1.h"
#include "MFCApplication1Dlg.h"
#include "afxdialogex.h"
#include "gts.h"
#include <conio.h>

#pragma comment(lib, "gts.lib")

#ifdef _DEBUG
#define new DEBUG_NEW
#endif


// 用于应用程序“关于”菜单项的 CAboutDlg 对话框

class CAboutDlg : public CDialogEx
{
public:
	CAboutDlg();

	// 对话框数据
#ifdef AFX_DESIGN_TIME
	enum { IDD = IDD_ABOUTBOX };
#endif

protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持

	// 实现
protected:
	DECLARE_MESSAGE_MAP()
};

CAboutDlg::CAboutDlg() : CDialogEx(IDD_ABOUTBOX)
{
}

void CAboutDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
}

BEGIN_MESSAGE_MAP(CAboutDlg, CDialogEx)
END_MESSAGE_MAP()


// CMFCApplication1Dlg 对话框



CMFCApplication1Dlg::CMFCApplication1Dlg(CWnd* pParent /*=nullptr*/)
	: CDialogEx(IDD_MFCAPPLICATION1_DIALOG, pParent)
	, core(1)
	, axis(1)
	, prfpos(0)
	, encpos(0)
{
	m_hIcon = AfxGetApp()->LoadIcon(IDR_MAINFRAME);
}

void CMFCApplication1Dlg::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
	DDX_Text(pDX, IDC_EDIT1, axis);//设置text控件
	//DDX_Text(pDX, IDC_EDIT2,jogvel);
	DDX_Text(pDX, IDC_EDIT3, prfpos);
	DDX_Text(pDX, IDC_EDIT4, encpos);
	//DDX_Text(pDX, IDC_EDIT5,HomeMode);
	DDX_Text(pDX, IDC_EDIT9, sts);
	//DDX_Text(pDX, IDC_EDIT6,Home_Vel);
	//DDX_Text(pDX, IDC_EDIT7,Home_indexVel);
	//DDX_Text(pDX, IDC_EDIT8,Home_Acc);


}


BEGIN_MESSAGE_MAP(CMFCApplication1Dlg, CDialogEx)
	ON_WM_SYSCOMMAND()
	ON_WM_PAINT()
	ON_WM_QUERYDRAGICON()
	ON_EN_CHANGE(IDC_EDIT1, &CMFCApplication1Dlg::OnEnChangeEdit1)
	ON_BN_CLICKED(IDC_BUTTON1, &CMFCApplication1Dlg::OnBtnClick1)//设置按钮
	ON_BN_CLICKED(IDC_BUTTON2, &CMFCApplication1Dlg::OnBtnClick2)
	ON_BN_CLICKED(IDC_BUTTON3, &CMFCApplication1Dlg::OnbtnClick3)
	ON_BN_CLICKED(IDC_BUTTON4, &CMFCApplication1Dlg::OnbtnClick4)
	ON_BN_CLICKED(IDC_BUTTON5, &CMFCApplication1Dlg::OnbtnClick5)
	ON_BN_CLICKED(IDC_BUTTON6, &CMFCApplication1Dlg::OnbtnClick6)
	ON_BN_CLICKED(IDC_BUTTON7, &CMFCApplication1Dlg::OnbtnClick7)
	ON_BN_CLICKED(IDC_BUTTON8, &CMFCApplication1Dlg::OnbtnClick8)
	ON_EN_CHANGE(IDC_EDIT5, &CMFCApplication1Dlg::OnEnChangeEdit5)
	ON_WM_TIMER()
	//ON_WM_LBUTTONDOWN(IDC_BUTTON9, &CMFCApplication1Dlg::OnBnButton9Down)
	//ON_WM_LBUTTONUP(IDC_BUTTON9, &CMFCApplication1Dlg::OnBnButton9Up)
	//ON_WM_LBUTTONDOWN(IDC_BUTTON10, &CMFCApplication1Dlg::OnBnButton10Down)
	//ON_WM_LBUTTONUP(IDC_BUTTON10, &CMFCApplication1Dlg::OnBnButton10Up)
	ON_BN_CLICKED(IDC_BUTTON9, &CMFCApplication1Dlg::OnBnClickedButton9)
	ON_BN_CLICKED(IDC_BUTTON10, &CMFCApplication1Dlg::OnBnClickedButton10)
	ON_BN_CLICKED(IDC_BUTTON11, &CMFCApplication1Dlg::OnBnClickedButton11)
	ON_BN_CLICKED(IDC_BUTTON12, &CMFCApplication1Dlg::OnBnClickedButton12)
	ON_BN_CLICKED(IDC_BUTTON13, &CMFCApplication1Dlg::OnBnClickedButton13)
	ON_BN_CLICKED(IDC_BUTTON14, &CMFCApplication1Dlg::OnBnClickedButton14)
	ON_BN_CLICKED(IDC_BUTTON15, &CMFCApplication1Dlg::OnBnClickedButton15)
END_MESSAGE_MAP()


// CMFCApplication1Dlg 消息处理程序

BOOL CMFCApplication1Dlg::OnInitDialog()
{
	CDialogEx::OnInitDialog();

	// 将“关于...”菜单项添加到系统菜单中。

	// IDM_ABOUTBOX 必须在系统命令范围内。
	ASSERT((IDM_ABOUTBOX & 0xFFF0) == IDM_ABOUTBOX);
	ASSERT(IDM_ABOUTBOX < 0xF000);

	CMenu* pSysMenu = GetSystemMenu(FALSE);
	if (pSysMenu != nullptr)
	{
		BOOL bNameValid;
		CString strAboutMenu;
		bNameValid = strAboutMenu.LoadString(IDS_ABOUTBOX);
		ASSERT(bNameValid);
		if (!strAboutMenu.IsEmpty())
		{
			pSysMenu->AppendMenu(MF_SEPARATOR);
			pSysMenu->AppendMenu(MF_STRING, IDM_ABOUTBOX, strAboutMenu);
		}


	}

	// 设置此对话框的图标。  当应用程序主窗口不是对话框时，框架将自动
	//  执行此操作
	SetIcon(m_hIcon, TRUE);			// 设置大图标
	SetIcon(m_hIcon, FALSE);		// 设置小图标

	// TODO: 在此添加额外的初始化代码

	return TRUE;  // 除非将焦点设置到控件，否则返回 TRUE
}

void CMFCApplication1Dlg::OnSysCommand(UINT nID, LPARAM lParam)
{
	if ((nID & 0xFFF0) == IDM_ABOUTBOX)
	{
		CAboutDlg dlgAbout;
		dlgAbout.DoModal();
	}
	else
	{
		CDialogEx::OnSysCommand(nID, lParam);
	}
}

// 如果向对话框添加最小化按钮，则需要下面的代码
//  来绘制该图标。  对于使用文档/视图模型的 MFC 应用程序，
//  这将由框架自动完成。

void CMFCApplication1Dlg::OnPaint()
{
	if (IsIconic())
	{
		CPaintDC dc(this); // 用于绘制的设备上下文

		SendMessage(WM_ICONERASEBKGND, reinterpret_cast<WPARAM>(dc.GetSafeHdc()), 0);

		// 使图标在工作区矩形中居中
		int cxIcon = GetSystemMetrics(SM_CXICON);
		int cyIcon = GetSystemMetrics(SM_CYICON);
		CRect rect;
		GetClientRect(&rect);
		int x = (rect.Width() - cxIcon + 1) / 2;
		int y = (rect.Height() - cyIcon + 1) / 2;

		// 绘制图标
		dc.DrawIcon(x, y, m_hIcon);
	}
	else
	{
		CDialogEx::OnPaint();
	}
}

//当用户拖动最小化窗口时系统调用此函数取得光标
//显示。
HCURSOR CMFCApplication1Dlg::OnQueryDragIcon()
{
	return static_cast<HCURSOR>(m_hIcon);
}



void CMFCApplication1Dlg::OnEnChangeEdit1()
{
	// TODO:  如果该控件是 RICHEDIT 控件，它将不
	// 发送此通知，除非重写 CDialogEx::OnInitDialog()
	// 函数并调用 CRichEditCtrl().SetEventMask()，
	// 同时将 ENM_CHANGE 标志“或”运算到掩码中。

	// TODO:  在此添加控件通知处理程序代码
}

void CMFCApplication1Dlg::commandhandler(CString name, int rtn)
{
	if (rtn)
	{
		CString str, str1;
		str.Format(_T("%d"), rtn);
		str1 = name + "=" + str;
		AfxMessageBox(str1);
	}
}

void CMFCApplication1Dlg::OnBtnClick1()
{
	// TODO: 在此添加控件通知处理程序代码
	rtn = GTN_Open(5, core);
	commandhandler(_T("GTN_Open"), rtn);
}


void CMFCApplication1Dlg::OnBtnClick2()
{
	// TODO: 在此添加控件通知处理程序代码
	rtn = GTN_Reset(core);
	commandhandler(_T("GTN_Reset"), rtn);
}

//////////////////////////////////////////////////////////////////////
void CMFCApplication1Dlg::OnbtnClick3()
{
	// TODO: 在此添加控件通知处理程序代码
	rtn = GTN_TerminateEcatComm(core);
	commandhandler(_T("GTN_TerminateEcatComm"), rtn);
	rtn = GTN_InitEcatComm(core);
	commandhandler(_T("GTN_InitEcatComm"), rtn);
	/*rtn = GTN_IsEcatReady(core, &status);
	commandhandler(_T("GTN_IsEcatReady"), rtn);*/
	do {
		rtn = GTN_IsEcatReady(core, &status);
	} while (status != 1 || rtn != 0);
	rtn = GTN_StartEcatComm(core);
	commandhandler(_T("GTN_InitEcatComm"), rtn);
	rtn = GTN_AlarmOff(core, axis);
	commandhandler(_T("GTN_AlarmOff"), rtn);
	rtn = GTN_LmtsOff(core, axis, -1);
	commandhandler(_T("GTN_LmtsOff"), rtn);


	//设置软限位模式
	rtn = GTN_SetSoftLimitMode(core, axis, pMode);
	commandhandler(_T("GTN_SetSoftLimitMode"), rtn);
	//读取软限位模式
	rtn = GTN_GetSoftLimitMode(core, axis,&pMode);
	commandhandler(_T("GTN_GetSoftLimitMode"), rtn);
	//设置软限位值
	rtn = GTN_SetSoftLimit(core, axis, 1000000000, -1000000000);
	commandhandler(_T("GTN_SetSoftLimit"), rtn);
	while (_kbhit())
	{
		// 读取第一轴轴状态
		rtn = GTN_GetSts(core, axis, &IAxisStatus);
		// 读取第一轴规划位置
		rtn = GTN_GetPrfPos(core, axis, &prfpos);
		printf("sts=0x%-8lx prfPos=%-10.2lf\r", sts, prfpos);
	}
	return ;
}

////////////////////////////////////////////////////////////////////////
void CMFCApplication1Dlg::OnbtnClick4()
{
	// TODO: 在此添加控件通知处理程序代码
	rtn = GTN_ZeroPos(core, axis, 1);
	commandhandler(_T("GTN_ZeroPos"), rtn);
}


void CMFCApplication1Dlg::OnbtnClick5()
{
	// TODO: 在此添加控件通知处理程序代码
	rtn = GTN_ClrSts(core, axis, 1);
	commandhandler(_T("GTN_ClrSts"), rtn);
}


void CMFCApplication1Dlg::OnbtnClick6()
{
	// TODO: 在此添加控件通知处理程序代码
	rtn = GTN_AxisOn(core, axis);
	commandhandler(_T("GTN_AxisOn"), rtn);
	if (rtn == 0)
	{
		AfxMessageBox(_T("上使能成功"));
	}
	else
	{
		AfxMessageBox(_T("上使能失败"));
	}
}


void CMFCApplication1Dlg::OnbtnClick7()
{
	// TODO: 在此添加控件通知处理程序代码
	rtn = GTN_AxisOff(core, axis);
	commandhandler(_T("GTN_AxisOff"), rtn);
	if (rtn == 0)
	{
		AfxMessageBox(_T("使能关闭成功"));
	}
	else
	{
		AfxMessageBox(_T("使能关闭失败"));
	}
}


//回零
void CMFCApplication1Dlg::OnbtnClick8()
{
	/*THomeStatus homeStatus;*/
	// TODO: 在此添加控件通知处理程序代码
	// 必须处于伺服使能状态，切换到回零模式
	rtn = GTN_SetHomingMode(core, axis, 6);
	commandhandler(_T("GTN_SetHomingMode"), rtn);
	// 设置回零参数
	rtn = GTN_SetEcatHomingPrm(core, axis, HomeMode, Home_Vel, Home_indexVel, Home_Acc, 0, 0);
	commandhandler(_T("GTN_SetEcatHomingPrm"), rtn);
	// 启动回零
	rtn = GTN_StartEcatHoming(core, axis);
	commandhandler(_T("GTN_StartEcatHoming"), rtn);
	do {
		rtn = GTN_GetEcatHomingStatus(core, axis, &homeStatus);
		commandhandler(_T("GTN_GetEcatHomingStatus"), rtn);
		if (bStop) //中断、停止回零
		{
			rtn = GTN_StopEcatHoming(core, axis);
			commandhandler(_T("GTN_StopEcatHoming"), rtn);
			break;
		}
	} while (3 != homeStatus); // 等待搜索原点完成
	rtn = GTN_SetHomingMode(core, axis, 8); // 切换到位置控制模式
	commandhandler(_T("GTN_SetHomingMode"), rtn);
}




void CMFCApplication1Dlg::OnTimer(UINT_PTR nIDEvent)
{
	//读取规划轴1的规划位置
	UpdateData(TRUE);
	rtn = GTN_GetPrfPos(core, axis, &prfpos);
	commandhandler(_T("GTN_GetprfPos"), rtn);
	rtn = GTN_GetEncPos(core, axis, &encpos);
	commandhandler(_T("GTN_GetEncPos"), rtn);
	// 读取轴状态
	rtn = GTN_GetSts(core, axis, &IAxisStatus);
	commandhandler(_T("GTN_GetSts"), rtn);
	// 伺服报警标志
	if (IAxisStatus & 0x2)
	{
		bFlagAlarm = TRUE;
		printf("伺服报警\n");
	}
	else
	{
		bFlagAlarm = FALSE;
		printf("伺服正常\n");
	}
	// 跟随误差越限标志
	if (IAxisStatus & 0x10)
	{
		bFlagMError = TRUE;
		printf("运动出错\n");
	}
	else
	{
		bFlagMError = FALSE;
		printf("运动正常\n");
	}
	// 正向限位
	if (IAxisStatus & 0x20)
	{
		bFlagPosLimit = TRUE;
		printf("正限位触发\n");
	}
	else
	{
		bFlagPosLimit = FALSE;
		printf("正限位未触发\n");
	}
	// 负向限位
	if (IAxisStatus & 0x40)
	{
		bFlagNegLimit = TRUE;
		printf("负限位触发\n");
	}
	else
	{
		bFlagNegLimit = FALSE;
		printf("负限位未触发\n");
	}
	// 平滑停止
	if (IAxisStatus & 0x80)
	{
		bFlagSmoothStoop = TRUE;
		printf("平滑停止触发\n");
	}
	else
	{
		bFlagSmoothStoop = FALSE;
		printf("平滑停止未触发\n");
	}
	// 急停标志
	if (IAxisStatus & 0x100)
	{
		bFlagAbruptStop = TRUE;
		printf("急停触发\n");
	}
	else
	{
		bFlagAbruptStop = FALSE;
		printf("急停未触发\n");
	}
	// 伺服使能标志
	if (IAxisStatus & 0x200)
	{
		bFlagServoOn = TRUE;
		printf("伺服使能\n");
	}
	else
	{
		bFlagServoOn = FALSE;
		printf("伺服关闭\n");
	}
	// 规划器正在运动标志
	if (IAxisStatus & 0x400)
	{
		bFlagMotion = TRUE;
		printf("规划器正在运动\n");
	}
	else
	{
		bFlagMotion = FALSE;
		printf("规划器已停止\n");
	}
	// 读取运动模式
	rtn = GTN_GetPrfMode(core, axis, &IPrfMode);
	commandhandler(_T("GTN_GetPrfMode"), rtn);
	//CMFCApplication1Dlg::UpdatePrfMode();
	printf("运动模式 %s\n", chPrfMode);
	memset(chPrfMode, '\0', sizeof(chPrfMode));
	// 清空字符串
	memset(chPrfMode, '\0', sizeof(chPrfMode));
	switch (IPrfMode)
	{
	case 0:
		sprintf_s(chPrfMode, sizeof(chPrfMode), "Trap");
		break;
	case 1:
		sprintf_s(chPrfMode, sizeof(chPrfMode), "Jog");
		break;
	case 2:
		sprintf_s(chPrfMode, sizeof(chPrfMode), "PT");
		break;
	case 3:
		sprintf_s(chPrfMode, sizeof(chPrfMode), "Gear");
		break;
	case 4:
		sprintf_s(chPrfMode, sizeof(chPrfMode), "Follow");
		break;
	case 5:
		sprintf_s(chPrfMode, sizeof(chPrfMode), "Interpolation");
		break;
	case 6:
		sprintf_s(chPrfMode, sizeof(chPrfMode), "PVT");
		break;
	default:
		break;
	}

	UpdateData(FALSE);
	CDialogEx::OnTimer(nIDEvent);
}

void CMFCApplication1Dlg::UpdateFlags()
{
	
}
void CMFCApplication1Dlg::UpdatePrfMode()
{
	
}
//	printf("运动模式 %s\n", chPrfMode);
//	CDialogEx::OnTimer(nIDEvent);
//	// 在这里添加定时器触发后的处理代码
//	CString str;
//	str.Format(_T("prfpos = %f"), prfpos);
//	AfxMessageBox(str);
//	str.Format(_T("encpos = %d"), encpos);
//	AfxMessageBox(str);
//	return;
//}


//void CMFCApplication1Dlg::OnTimer(UINT_PTR nIDEvent)
//{
//	
//	CDialog::OnTimer(nIDEvent);
//}

void CMFCApplication1Dlg::OnEnChangeEdit5()
{
	// TODO:  如果该控件是 RICHEDIT 控件，它将不
	// 发送此通知，除非重写 CDialogEx::OnInitDialog()
	// 函数并调用 CRichEditCtrl().SetEventMask()，
	// 同时将 ENM_CHANGE 标志“或”运算到掩码中。

	// TODO:  在此添加控件通知处理程序代码
}

void CMFCApplication1Dlg::OnBnClickedButton9()
{
	// TODO: 在此添加控件通知处理程序代码
	TJogPrm jog;
	// TODO: 在此添加控件通知处理程序代码
	// 将 AXIS 轴设为 Jog 模式
	rtn = GTN_PrfJog(core, axis);
	commandhandler(_T("GTN_PrfJog"), rtn);
	// 设置Jog 运动参数(需要读取全部运动参数到上位机变量)
	rtn = GTN_GetJogPrm(core, axis, &jog);
	commandhandler(_T("GTN_SetJogPrm"), rtn);
	//设置需要修改的运动参数
	jog.acc = 1;
	jog.dec = 1;
	jog.smooth = 0.2;
	// 读取Jog 运动参数
	rtn = GTN_SetJogPrm(core, axis, &jog);
	commandhandler(_T("GTN_GetJogPrm"), rtn);
	// 设置 AXIS 轴的目标速度
	rtn = GTN_SetVel(core, axis, 7);
	commandhandler(_T("GTN_SetVel"), rtn);
	// 启动 AXIS 轴的运动
	rtn = GTN_Update(core, 1 << (axis - 1));
	commandhandler(_T("GTN_Update"), rtn);

	// 设置定时器，700毫秒后触发
	SetTimer(1, 100, NULL);
}


void CMFCApplication1Dlg::OnBnClickedButton10()
{
	// TODO: 在此添加控件通知处理程序代码
	TJogPrm jog;
	// TODO: 在此添加控件通知处理程序代码
	// 将 AXIS 轴设为 Jog 模式
	rtn = GTN_PrfJog(core, axis);
	commandhandler(_T("GTN_PrfJog"), rtn);
	// 设置Jog 运动参数(需要读取全部运动参数到上位机变量)
	rtn = GTN_GetJogPrm(core, axis, &jog);
	commandhandler(_T("GTN_GetJogPrm"), rtn);
	//设置需要修改的运动参数
	jog.acc = 0.0626;
	jog.dec = 0.0626;
	jog.smooth = 0.2;
	// 读取Jog 运动参数
	rtn = GTN_SetJogPrm(core, axis, &jog);
	commandhandler(_T("GTN_SetJogPrm"), rtn);
	// 设置 AXIS 轴的目标速度
	rtn = GTN_SetVel(core, axis, -7);
	commandhandler(_T("GTN_SetVel"), rtn);
	// 启动 AXIS 轴的运动
	rtn = GTN_Update(core, 1 << (axis - 1));
	commandhandler(_T("GTN_Update"), rtn);


	// 设置定时器，700毫秒后触发
	SetTimer(1, 100, NULL);
}


void CMFCApplication1Dlg::OnBnClickedButton11()
{
	// TODO: 在此添加控件通知处理程序代码
	rtn = GTN_Stop(core, 1<< (axis - 1), 0);
	commandhandler(_T("GTN_Stop"), rtn);
	if (rtn != 0) {
		AfxMessageBox(_T("停止运动失败！"));
	}

	// 设置定时器，700毫秒后触发
	SetTimer(1, 100, NULL);
}

//齿轮运动
void CMFCApplication1Dlg::OnBnClickedButton12()
{
	// TODO: 在此添加控件通知处理程序代码
	TJogPrm jog;
	// 将主轴设为 Jog 模式
	rtn = GTN_PrfJog(core, axis);
	commandhandler(_T("GTN_PrfJog"), rtn);
	// 设置主轴运动参数
	rtn = GTN_GetJogPrm(core, axis, &jog);
	commandhandler(_T("GTN_PrfJog"), rtn);
	jog.acc = 1;
	rtn = GTN_SetJogPrm(core, axis, &jog);
	commandhandler(_T("GTN_PrfJog"), rtn);
	rtn = GTN_SetVel(core, axis, 100);
	commandhandler(_T("GTN_PrfJog"), rtn);
	// 启动主轴
	rtn = GTN_Update(core, 1 << (axis - 1));
	commandhandler(_T("GTN_PrfJog"), rtn);
	// 将从轴设为 Gear 模式
	rtn = GTN_PrfGear(core, axis, 0);
	commandhandler(_T("GTN_PrfJog"), rtn);
	// 设置主轴，默认跟随主轴规划位置
	rtn = GTN_SetGearMaster(core, axis, 0, 2, 0);
	commandhandler(_T("GTN_PrfJog"), rtn);
	// 设置从轴的传动比和离合区
	rtn = GTN_SetGearRatio(core, axis, 2, 1, 100000);
	commandhandler(_T("GTN_PrfJog"), rtn);
	// 启动从轴
	rtn = GTN_GearStart(core, axis << (axis - 1));
	commandhandler(_T("GTN_PrfJog"), rtn);
	
	// 设置定时器，700毫秒后触发
	SetTimer(1, 100, NULL);
}

////JOG+按下
//void CMFCApplication1Dlg::OnBnButton9Down(UINT nFlags, CPoint point)
//{
//	//CRect rect;
//	//CButton* pButton = (CButton*)GetDlgItem(IDC_BUTTON9);
//	//pButton->GetWindowRect(&rect);
//	//GetDlgItem(IDC_BUTTON9)->GetWindowRect(&rect);
//	//ScreenToClient(&rect); // 转换为对话框坐标
//	//if (rect.PtInRect(point))  // 检查点是否在按钮内
//	//{
//		
//}

//JOG+松开
//void CMFCApplication1Dlg::OnBnButton9Up(UINT nFlags, CPoint point)
//{
//	//CRect rect;
//	//CButton* pButton = (CButton*)GetDlgItem(IDC_BUTTON9);
//	//pButton->GetWindowRect(&rect);
//	//GetDlgItem(IDC_BUTTON9)->GetWindowRect(&rect);
//	//ScreenToClient(&rect); // 转换为对话框坐标
//	//if (rect.PtInRect(point))  // 检查点是否在按钮内
//	//{// TODO: 在此添加控件通知处理程序代码
//	// 停止 AXIS 轴的运动
//		AfxMessageBox(_T("按钮按下"));
//		
//	/*}*/
//	CDialogEx::OnLButtonDown(nFlags, point);
//}

//JOG-按下
//void CMFCApplication1Dlg::OnBnButton10Down(UINT nFlags, CPoint point)
//{
//	
//}

//JOG-松开
//void CMFCApplication1Dlg::OnBnButton10Up(UINT nFlags, CPoint point)
//{
//	// TODO: 在此添加控件通知处理程序代码
//	// 停止 AXIS 轴的运动
//	rtn = GTN_Stop(core, 0, 0);
//	commandhandler(_T("GTN_Stop"), rtn);
//	if (rtn != 0) {
//		AfxMessageBox(_T("停止运动失败！"));
//	}
//	CDialogEx::OnLButtonDown(nFlags, point);
//}





//建立二维插补坐标系
void CMFCApplication1Dlg::OnBnClickedButton13()
{
	// TODO: 在此添加控件通知处理程序代码
	// TCrdPrm结构体变量，该结构体定义了坐标系
	TCrdPrm crdPrm;
	// 将结构体变量初始化为0
	memset(&crdPrm, 0, sizeof(crdPrm));
	// 为结构体赋值
	crdPrm.dimension = 2; // 坐标系为二维坐标系
	crdPrm.synVelMax = 500; // 最大合成速度：500pulse/ms
	crdPrm.synAccMax = 1; // 最大加速度：1pulse/ms^2
	crdPrm.evenTime = 50; // 最小匀速时间：50ms
	crdPrm.profile[0] = 1; // 规划器1对应到X轴
	crdPrm.profile[1] = 2; // 规划器2对应到Y轴
	crdPrm.setOriginFlag = 1; // 表示需要指定坐标系的原点坐标的规划位置
	crdPrm.originPos[0] = 100; // 坐标系的原点坐标的规划位置为（100, 100）
	crdPrm.originPos[1] = 100;
	// 建立1号坐标系，设置坐标系参数
	rtn = GTN_SetCrdPrm(core, 1, &crdPrm);
	commandhandler(_T("GTN_SetCrdPrm"), rtn);

	// 设置定时器，700毫秒后触发
	SetTimer(1, 100, NULL);
}

//直线插补
void CMFCApplication1Dlg::OnBnClickedButton14()
{
	// TODO: 在此添加控件通知处理程序代码
	// 即将把数据存入坐标系1的FIFO0中，所以要首先清除此缓存区中的数据
	rtn = GTN_CrdClear(core, 1, 0);
	commandhandler(_T("GTN_CrdClear"), rtn);
	// 向缓存区写入第一段插补数据
	rtn = GTN_LnXY(
		core,
		1, // 该插补段的坐标系是坐标系1
		200000, 0, // 该插补段的终点坐标(200000, 0)
		100, // 该插补段的目标速度：100pulse/ms
		0.1, // 插补段的加速度：0.1pulse/ms^2
		0, // 终点速度为0
		0); // 向坐标系1的FIFO0缓存区传递该直线插补数据
	commandhandler(_T("GTN_LnXY"), rtn);
	// 向缓存区写入第二段插补数据
	rtn = GTN_LnXY(core, 1, 100000, 173205, 100, 0.1, 0, 0);
	commandhandler(_T("GTN_LnXY"), rtn);
	// 缓存区数字量输出
	rtn = GTN_BufIO(
		core,
		1, // 坐标系是坐标系1
		MC_GPO, // 数字量输出类型：通用输出
		0xffff, // bit0~bit15全部都输出
		0x55, // 输出的数值为0x55
		0); // 向坐标系1的FIFO0缓存区传递该数字量输出
	commandhandler(_T("GTN_BufIO"), rtn);
	// 第三段插补数据
	rtn = GTN_LnXY(core, 1, -100000, 173205, 100, 0.1, 0, 0);
	commandhandler(_T("GTN_LnXY"), rtn);
	// 缓存区数字量输出
	rtn = GTN_BufIO(core, 1, MC_GPO, 0xffff, 0xaa, 0);
	commandhandler(_T("GTN_BufIO"), rtn);
	// 第四段插补数据
	rtn = GTN_LnXY(core, 1, -200000, 0, 100, 0.1, 0, 0);
	commandhandler(_T("GTN_LnXY"), rtn);
	// 缓存区延时指令
	rtn = GTN_BufDelay(
		core,
		1, // 坐标系是坐标系1
		400, // 延时400ms
		0); // 向坐标系1的FIFO0缓存区传递该延时
	commandhandler(_T("GTN_BufDelay"), rtn);
	// 第五段插补数据
	rtn = GTN_LnXY(core, 1, -100000, -173205, 100, 0.1, 0, 0);
	commandhandler(_T("GTN_LnXY"), rtn);
	// 缓存区数字量输出
	rtn = GTN_BufIO(core, 1, MC_GPO, 0xffff, 0x55, 0);
	commandhandler(_T("GTN_BufIO"), rtn);
	// 缓存区延时指令
	rtn = GTN_BufDelay(core, 1, 100, 0);
	commandhandler(_T("GTN_BufDelay"), rtn);
	// 第六段插补数据
	rtn = GTN_LnXY(core, 1, 100000, -173205, 100, 0.1, 0, 0);
	commandhandler(_T("GTN_LnXY"), rtn);
	// 第七段插补数据
	rtn = GTN_LnXY(core, 1, 200000, 0, 100, 0.1, 0, 0);
	commandhandler(_T("GTN_LnXY"), rtn);
	// 查询坐标系1的FIFO0所剩余的空间
	rtn = GTN_CrdSpace(core, 1, &space, 0);
	commandhandler(_T("GTN_CrdSpace"), rtn);
	// 启动坐标系1的FIFO0的插补运动
	rtn = GTN_CrdStart(core, 1, 0);
	commandhandler(_T("GTN_CrdStart"), rtn);
	// 等待运动完成
	rtn = GTN_CrdStatus(core, 1, &run, &segment, 0);
	commandhandler(_T("GTN_CrdStatus"), rtn);
	do
	{
		// 查询坐标系1的FIFO的插补运动状态
		rtn = GTN_CrdStatus(
			core,
			1, // 坐标系是坐标系1
			&run, // 读取插补运动状态
			&segment, // 读取当前已经完成的插补段数
			0); // 查询坐标系1的FIFO0缓存区
		commandhandler(_T("GTN_CrdStatus"), rtn);
		// 坐标系在运动, 查询到的run的值为1
	} while (run == 1);

	// 设置定时器，700毫秒后触发
	SetTimer(1, 100, NULL);
}

//圆弧插补
void CMFCApplication1Dlg::OnBnClickedButton15()
{
	// TODO: 在此添加控件通知处理程序代码
	// 即将把数据存入坐标系1的FIFO0中，所以要首先清除此缓存区中的数据
	rtn = GTN_CrdClear(core, 1, 0);
	commandhandler(_T("GTN_CrdClear"), rtn);
	// 向缓存区写入第一段插补数据
	rtn = GTN_LnXY(
		core,
		1, // 该插补段的坐标系是坐标系1
		200000, 0, // 该插补段的终点坐标(200000, 0)
		100, // 该插补段的目标速度：100pulse/ms
		0.1, // 插补段的加速度：0.1pulse/ms^2
		0, // 终点速度为0
		0); // 向坐标系1的FIFO0缓存区传递该直线插补数据
	// 向缓存区写入第二段插补数据，该段数据是以圆心描述方法描述了一个整圆
	rtn = GTN_ArcXYC(core,
		1, // 坐标系是坐标系1
		200000, 0, // 该圆弧的终点坐标(200000, 0)
		-100000, 0, // 圆弧插补的圆心相对于起点位置的偏移量(-100000, 0)
		0, // 该圆弧是顺时针圆弧
		100, // 该插补段的目标速度：100pulse/ms
		0.1, // 该插补段的加速度：0.1pulse/ms^2
		0, // 终点速度为0
		0); // 向坐标系1的FIFO0缓存区传递该直线插补数据
	// 向缓存区写入第三段插补数据，该段数据是以半径描述方法描述了一个1/4圆弧
	rtn = GTN_ArcXYR(core,
		1, // 坐标系是坐标系1
		0, 200000, // 该圆弧的终点坐标(0, 200000)
		200000, // 半径：200000pulse
		1, // 该圆弧是逆时针圆弧
		100, // 该插补段的目标速度：100pulse/ms
		0.1, // 该插补段的加速度：0.1pulse/ms^2
		0, // 终点速度为0
		0); // 向坐标系1的FIFO0缓存区传递该直线插补数据
	commandhandler(_T("GTN_LnXY"), rtn);
	// 向缓存区写入第四段插补数据，回到原点位置
	rtn = GTN_LnXY(core, 1, 0, 0, 100, 0.1, 0, 0);
	commandhandler(_T("GTN_LnXY"), rtn);
	// 查询坐标系1的FIFO0所剩余的空间
	rtn = GTN_CrdSpace(1, 1, &space, 0);
	commandhandler(_T("GTN_CrdSpace"), rtn);
	// 启动坐标系1的FIFO0的插补运动
	rtn = GTN_CrdStart(1, 1, 0);
	commandhandler(_T("GTN_CrdStart"), rtn);

	// 设置定时器，700毫秒后触发
	SetTimer(1, 100, NULL);
}
