<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Dialog_Plc</class>
 <widget class="QDialog" name="Dialog_Plc">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>920</width>
    <height>465</height>
   </rect>
  </property>
  <property name="font">
   <font>
    <bold>true</bold>
   </font>
  </property>
  <property name="windowTitle">
   <string>命1510A第七轴运动控制</string>
  </property>
  <property name="styleSheet">
   <string notr="true">background-color: rgb(223, 223, 223);</string>
  </property>
  <widget class="QWidget" name="horizontalLayoutWidget">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>30</y>
     <width>821</width>
     <height>31</height>
    </rect>
   </property>
   <layout class="QHBoxLayout" name="horizontalLayout">
    <item>
     <spacer name="verticalSpacer_4">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <widget class="QLabel" name="label">
      <property name="font">
       <font>
        <bold>true</bold>
       </font>
      </property>
      <property name="text">
       <string>串行端口</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLineEdit" name="lineEdit_7ACOMPORT">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
     </widget>
    </item>
    <item>
     <spacer name="verticalSpacer_3">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <widget class="QLabel" name="label_2">
      <property name="font">
       <font>
        <bold>true</bold>
       </font>
      </property>
      <property name="text">
       <string>波特率</string>
      </property>
     </widget>
    </item>
    <item>
     <spacer name="verticalSpacer_2">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <widget class="QLineEdit" name="lineEdit_7ABaudRate">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
     </widget>
    </item>
    <item>
     <spacer name="verticalSpacer">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <widget class="QLabel" name="label_7">
      <property name="font">
       <font>
        <bold>true</bold>
       </font>
      </property>
      <property name="text">
       <string>数据位</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLineEdit" name="lineEdit_7ADATABITS">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLabel" name="label_8">
      <property name="font">
       <font>
        <bold>true</bold>
       </font>
      </property>
      <property name="text">
       <string>停止位</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLineEdit" name="lineEdit_7ASTOPBITS">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QWidget" name="horizontalLayoutWidget_3">
   <property name="geometry">
    <rect>
     <x>90</x>
     <y>180</y>
     <width>751</width>
     <height>41</height>
    </rect>
   </property>
   <layout class="QHBoxLayout" name="horizontalLayout_3">
    <item>
     <widget class="QPushButton" name="pushButton_Plc_Connect">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(214, 214, 214);</string>
      </property>
      <property name="text">
       <string>Connect</string>
      </property>
     </widget>
    </item>
    <item>
     <spacer name="verticalSpacer_7">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <widget class="QLabel" name="label_connect">
      <property name="text">
       <string>OK</string>
      </property>
     </widget>
    </item>
    <item>
     <spacer name="verticalSpacer_8">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <spacer name="verticalSpacer_9">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <widget class="QLabel" name="label_6">
      <property name="text">
       <string>Server Send Back</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLineEdit" name="lineEdit_TcpBackMassae">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QLabel" name="label_5">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>250</y>
     <width>69</width>
     <height>31</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <bold>true</bold>
    </font>
   </property>
   <property name="text">
    <string>输入指令</string>
   </property>
  </widget>
  <widget class="QTextEdit" name="textEdit_PlcOrder_2">
   <property name="geometry">
    <rect>
     <x>40</x>
     <y>300</y>
     <width>821</width>
     <height>101</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(255, 255, 255);</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pushButton_Plc_AddToMainwinder_2">
   <property name="geometry">
    <rect>
     <x>310</x>
     <y>420</y>
     <width>231</width>
     <height>28</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(255, 255, 127);</string>
   </property>
   <property name="text">
    <string>发送测试</string>
   </property>
  </widget>
  <widget class="QWidget" name="horizontalLayoutWidget_4">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>80</y>
     <width>821</width>
     <height>31</height>
    </rect>
   </property>
   <layout class="QHBoxLayout" name="horizontalLayout_4">
    <item>
     <spacer name="verticalSpacer_10">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <widget class="QLabel" name="label_9">
      <property name="font">
       <font>
        <bold>true</bold>
       </font>
      </property>
      <property name="text">
       <string>奇偶校验</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLineEdit" name="lineEdit_7APARITY">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
     </widget>
    </item>
    <item>
     <spacer name="verticalSpacer_11">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <widget class="QLabel" name="label_10">
      <property name="font">
       <font>
        <bold>true</bold>
       </font>
      </property>
      <property name="text">
       <string>从站地址</string>
      </property>
     </widget>
    </item>
    <item>
     <spacer name="verticalSpacer_12">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <widget class="QLineEdit" name="lineEdit_7ASLAVEADDRESS">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
     </widget>
    </item>
    <item>
     <spacer name="verticalSpacer_13">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <widget class="QLabel" name="label_11">
      <property name="font">
       <font>
        <bold>true</bold>
       </font>
      </property>
      <property name="text">
       <string>超时设置</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLineEdit" name="lineEdit_7ATIMEOUT">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLabel" name="label_12">
      <property name="font">
       <font>
        <bold>true</bold>
       </font>
      </property>
      <property name="text">
       <string>重试次数</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLineEdit" name="lineEdit_7ARETRAY">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QWidget" name="horizontalLayoutWidget_5">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>130</y>
     <width>191</width>
     <height>31</height>
    </rect>
   </property>
   <layout class="QHBoxLayout" name="horizontalLayout_5">
    <item>
     <spacer name="verticalSpacer_14">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <widget class="QLabel" name="label_13">
      <property name="font">
       <font>
        <bold>true</bold>
       </font>
      </property>
      <property name="text">
       <string>流控制</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLineEdit" name="lineEdit_7AFLOWCONTROL">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
