//////////////commandscheduler.cpp

#include "commandscheduler.h"
#include "ui_commandscheduler.h"

CommandScheduler::CommandScheduler(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::CommandScheduler)
{
    try {
        ui->setupUi(this);  // 加载 UI
        qDebug() << "CommandScheduler UI setup completed.";
    } catch (const std::exception& e) {
        qDebug() << "Exception during setupUi:" << e.what();
    }
}

CommandScheduler::~CommandScheduler()
{
    delete ui;
}

void CommandScheduler::setTotalTimes(const QString &time)
{
    qDebug() << "CommandScheduler::setTotalTimes called with time:" << time;
    qDebug() << "ui pointer:" << ui;
    qDebug() << "lineEdit_total_times pointer:" << ui->lineEdit_total_times;
    if (ui && ui->lineEdit_total_times) {
        ui->lineEdit_total_times->setText(time);
    } else {
        qDebug() << "ui or lineEdit_total_times is nullptr!";
    }
}
