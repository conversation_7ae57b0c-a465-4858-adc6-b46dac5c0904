//mainwindow.cpp

#include "mainwindow.h"
#include "./ui_mainwindow.h"
#include "dialog.h" //
#include "dialog_plc.h"
#include <QDebug>
#include "loic.h"
#include <QTimer>
#include <QDateTime>
#include "sensor.h"
//#include "robotArm.h"
#include <QSettings>
#include <QDateTime>
#include <QMessageBox>
#include <QThread>
#include <QRegularExpression>
#include "d_size.h"
#include "ethercat.h"
#include <QDateTime>
#include <QScrollBar>
#include <QApplication>
#include <QTextEdit>

#include <windows.h>
#include <shellapi.h>
#include <windows.h>
#include <QDir>
//部署，發布   E:\Qt\6.6.1\mingw_64\bin\windeployqt6.exe E:\swingtopM\build-sewingtop-Desktop_Qt_6_6_1_MinGW_64_bit-Release\sewingtop.exe









////////////////////////////////////////////////构造函数//////////////////////////////////////////////////////
/**
 * MainWindow 类的构造函数
 * 接收的变量:
 * - parent: 父窗口指针，用于初始化 QMainWindow
 *
 * 发出的变量:
 * - ui: 用户界面实例，用于界面元素的交互和显示
 * - dSizeDialog: 用于显示尺寸设置对话框的指针，初始化为 nullptr
 * - ethercatWindow: 用于显示 EtherCAT 窗口的指针，初始化为 nullptr
 * - nextSensor485Id: 传感器编号，初始化为 0，用于管理485传感器的ID
 * - dialogInstance: Dialog 类的实例，用于处理文本汇总更新
 * - dialogPlc: Dialog_Plc 类的实例，用于添加 PLC 命令
 * - serialPort: 串口对象，用于管理串口通信
 * - robotArmInstance: RobotArm 类的实例，用于处理机器人手臂的命令
 * - sensorCommandTimer: 定时器对象，用于周期性处理传感器命令队列
 *
 * 功能描述:
 * - 初始化界面和变量
 * - 检查软件的试用期或激活状态
 * - 连接界面按钮和相应的槽函数，处理用户交互
 * - 实例化并管理各类对话框和通信设备
 */


MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
    , dSizeDialog(nullptr)
    , ethercatWindow(nullptr)
    , isEthercatInitialized(false)
    , isEthercatConnected(false)
{
    qDebug() << "MainWindow constructor started...";
    
    try {
        ui->setupUi(this);
        setupDebugOutput();  // 添加這行
        connect(ui->pushButton_ClearDebug, &QPushButton::clicked, 
                this, &MainWindow::clearDebugOutput);
        
        // 初始化其他组件
        qDebug() << "Initializing components...";
        
        // 设置窗口标题
        setWindowTitle("SewingTop");
        
        // 隐藏不需要的控件
        ui->textEdit_total_action->setVisible(false);
        ui->scrollArea->setVisible(false);
        ui->scrollArea_2->setVisible(false); // 确保这一行被添加
        ui->pushButton_sendorder->setVisible(false);
        ui->textEdit_Hex->setVisible(false);
        ui->pushButton_sendMain_2->setVisible(false);


        // 初始化 nextSensor485Id
        nextSensor485Id = 0;

        // 在程序启动时检查试用期或激活状态
        if (!checkTrial()) {
            QMessageBox::critical(this, "试用期结束", "您的试用期已经结束或激活失败，请输入有效的激活码。");
            setUiEnabled(false);
        }

        // 连接激活按钮的槽
        connect(ui->pushButton_6Active, &QPushButton::clicked, this, &MainWindow::onActivationAttempt);

        // 实例化 Dialog 类
        dialogInstance = new Dialog(this);
        connect(dialogInstance, &Dialog::forwardUpdateTextSummary, this, &MainWindow::handleUpdateTextSummary);

        //connect(ui->pushButton_2PLC, &QPushButton::clicked, this, &MainWindow::on_pushButton_PLC_clicked, Qt::UniqueConnection);

        // 实例化 Dialog_Plc 类
        dialogPlc = new Dialog_Plc(this);
        connect(dialogPlc, &Dialog_Plc::addPlcOrderToMainWindow, this, &MainWindow::onPlcOrderAdded);

        // 初始化 QSerialPort 对象
        serialPort = new QSerialPort(this);

        // 连接 pushButton_Logic 的 clicked 信号到槽函数 on_pushButton_Logic_clicked
        connect(ui->pushButton_2Logic, &QPushButton::clicked, this, &MainWindow::on_pushButton_Logic_clicked);

        //connect(ui->pushButton_sendorder, SIGNAL(clicked()), this, SLOT(on_pushButton_sendorder_clicked()), Qt::UniqueConnection);

        // Sensor
        connect(ui->pushButton_Open_Sensor_2, SIGNAL(clicked()), this, SLOT(on_pushButton_Open_Sensor_2_clicked));

        //connect(ui->Show_RB, SIGNAL(clicked()), this, SLOT(onShowRBClicked()));

        // 实例化 RobotArm
        robotArmInstance = new RobotArm(this);
        bool isConnected = connect(robotArmInstance, &RobotArm::commandIssued, this, &MainWindow::handleCommandIssued);

        if (isConnected) {
            qDebug() << "[MainWindow] 成功连接 RobotArm::commandIssued 信号到 MainWindow::handleCommandIssued 槽。";
        } else {
            qDebug() << "[MainWindow] 连接 RobotArm::commandIssued 信号到 MainWindow::handleCommandIssued 槽失败。";
        }



        sensorCommandTimer = new QTimer(this);
        connect(sensorCommandTimer, &QTimer::timeout, this, &MainWindow::processSensorCommandQueue);

        /// 在 MainWindow 的構造函數中
        ui->pushButton_Open_Sensor_485->disconnect();
        connect(ui->pushButton_Open_Sensor_485, &QPushButton::clicked, this, &MainWindow::on_pushButton_Open_Sensor_485_clicked, Qt::UniqueConnection);


        connect(ui->pushButton_Opendsize, &QPushButton::clicked, this, &MainWindow::on_pushButton_Opendsize_clicked);

        // 在 MainWindow 构造函数中
        ui->pushButton_EtherCAT->disconnect(); // 断开所有现有连接
        connect(ui->pushButton_EtherCAT, &QPushButton::clicked, this, &MainWindow::on_pushButton_EtherCAT_clicked);

        commandScheduler = new CommandScheduler(this); // 实例化新界面
        connect(ui->openCommandSchedulerButton, &QPushButton::clicked, this, &MainWindow::openCommandScheduler);

        ethercatConnectionTimer = new QTimer(this);
        ethercatConnectionTimer->setInterval(1000); // 1秒检查一次
        connect(ethercatConnectionTimer, &QTimer::timeout, this, &MainWindow::checkEthercatConnection);

        connect(ui->pushButton_ClearDebug, &QPushButton::clicked, 
                this, &MainWindow::clearDebugOutput);

        qDebug() << "MainWindow constructor completed successfully";
        
        m_ethercat = nullptr;  // 初始化為 nullptr
        
    } catch (const std::exception& e) {
        qDebug() << "Error in MainWindow constructor:" << e.what();
        throw;
    } catch (...) {
        qDebug() << "Unknown error in MainWindow constructor";
        throw;
    }
}

/////////////////////////////////////////// 析构函数/////////////////////////////////////////////////////////////


////////////////////////////////////////////////////////////////////////////////////////////////////////////////
MainWindow::~MainWindow()

/**
 * MainWindow 类的析构函数
 * 用途:
 * - 清理和释放所有资源，避免内存泄漏。
 *
 * 功能描述:
 * - 删除所有 Sensor_485 和 EtherCAT 窗口实例。
 * - 释放用户界面相关资源。
 * - 检查并删除 dSizeDialog 和 ethercatWindow 实例（如果它们被实例化）。
 */


{
    if (ethercatConnectionTimer) {
        ethercatConnectionTimer->stop();
        delete ethercatConnectionTimer;
    }
    
    if (ethercatWindow) {
        ethercatWindow->disconnect();
        delete ethercatWindow;
    }
    
    // 刪除所有 Sensor_485 實例
    qDeleteAll(sensor485Instances);
    sensor485Instances.clear();

    // 删除所有 EtherCAT 窗口实例，并清空容器
    qDeleteAll(ethercatWindows);
    ethercatWindows.clear();

    // 删除其他窗口实例
    if (dSizeDialog) {
        delete dSizeDialog;
    }
    
    if (ethercatWindow) {
        delete ethercatWindow;
    }

    // 最后删除 UI（只删除一次）
    delete ui;
}
////////////////////////////////////////////////////////////////////////////////////////////////////////////
// Slot implementation

/**
 *这两个槽函数处理的是具体的用户界面交互，一个用于更新文本摘要，另一个用于显示PLC对话框。
 * Slot: handleUpdateTextSummary
 * 用途:
 * - 接收并处理从 Dialog 发送过来的文本摘要。
 *
 * 参数:
 * - summary: 从 Dialog 传递过来的 QString 类型的文本摘要。
 *
 * 功能描述:
 * - 将接收到的文本摘要更新到主窗口的 textEdit_total_action 文本框中。
 */
//////////////////////////////////////////////////////////////////////////////////////////////////////
void MainWindow::on_pushButton_3_clicked()

/**
 * Slot: on_pushButton_3_clicked
 * 用途:
 * - 处理界面上 "pushButton_3" 按钮的点击事件。
 *
 * 功能描述:
 * - 显示一个模态对话框，阻止用户访问主窗口的其他部分直到对话框关闭。
 *
 * 变量说明:
 * - dialogInstance: 已经在其他地方（如构造函数中）创建的 Dialog 类的实例，用于展示对话框。
 */

{
    //Dialog *dialog = new Dialog(this); // 创建Dialog类的实例，并通过指针操作
    dialogInstance->setModal(true); // 如果您希望对话框是模态的，即阻止用户访问其他窗口
    //dialog.exec(); // 显示对话框

    //dialog->setModal(true);
    // 显示对话框
    dialogInstance->exec();
}
////////////////////////////////////////////////////////////////////////////////////////////////////////
void MainWindow::handleUpdateTextSummary(const QString &summary) {

    /**
 *这两个槽函数处理的是具体的用户界面交互，一个用于更新文本摘要，另一个用于显示PLC对话框。
 * Slot: handleUpdateTextSummary
 * 用途:
 * - 接收并处理从 Dialog 发送过来的文本摘要。
 *
 * 参数:
 * - summary: 从 Dialog 传递过来的 QString 类型的文本摘要。
 *
 * 功能描述:
 * - 将接收到的文本摘要更新到主窗口的 textEdit_total_action 文本框中。
 */



    // 在这里处理从 Dialog 接收到的 summary
    // 比如更新 MainWindow 中的某个文本框或标签
    qDebug() << "3step_handleUpdateTextSummary triggered with summary:" << summary;
    ui->textEdit_total_action->setText(summary); // 假设你有一个 QLabel 名为 textEdit_total_action
}
void MainWindow::on_pushButton_PLC_clicked()
{
    // 实现PLC按钮的点击处理逻辑
}
///////////////////////////////////////////////////////////////////////////////////////////////////
// 接收來自   1510A第七轴运动控制   頁面發出的命令，並且加入textEdit_total_action,顯示他。
void MainWindow::onPlcOrderAdded(QPair<QString, QString> order) {

    /**
 * Slot: onPlcOrderAdded
 * 用途:
 * - 接收从 PLC 对话框发送过来的命令，并在主窗口中显示。
 *
 * 参数:
 * - order: 包含命令和来源的 QPair 对象，其中 first 是命令文本，second 是来源标识。
 *
 * 功能描述:
 * - 如果命令来自 "PLC"，则将其以特定格式添加到主窗口的 textEdit_total_action 文本框中。
 * - 命令文本之后，添加一个 "#end" 标记以指示命令结束。
 */

    if (order.second == "PLC") {
        // 将命令以特定格式添加到textEdit_total_action
        ui->textEdit_total_action->append("PLC: " + order.first); // 添加来源标签
        // 命令添加完毕后，在末尾添加#end标记
        ui->textEdit_total_action->append("#end");
    }
}
//在主窗口的運動控制欄加入機械臂的運動控制訊號。

// MainWindow.cpp
void MainWindow::handleCommandIssued(QPair<QString, QString> order) {
    if (order.second == "ROBO") {
        // 将命令以特定格式添加到textEdit_Hex
        ui->textEdit_Hex->append("ROBO: " + order.first); // 添加来源标签
    }
}






void MainWindow::on_pushButton_Logic_clicked()
{
    // 创建Loic窗口实例并显示
    Loic *loicDialog = new Loic(this); // 使用this作为父窗口以便于管理内存
    loicDialog->setModal(true); // 如果你希望Loic窗口为模态，可以设置为true
    loicDialog->show();
}

//顯示傳感器界面
void MainWindow::on_pushButton_Open_Sensor_clicked()
{
    // 实现Open Sensor按钮的点击处理逻辑
}

void MainWindow::onShowRBClicked() {
    if (robotArmInstance) {
        robotArmInstance->show();
    } else {
        qDebug() << "RobotArm instance is not initialized.";
    }
}
//預發射窗口區/////////////////////////////////////////////////////////////////////////////
QString MainWindow::processPreSend1(const QString& command)
{
    // 在这里添加 textEdit_presend1 的检核逻辑
    QString processedCommand = command;
    // 例如，可以在这里添加一些修改或验证逻辑
    ui->textEdit_presend1->append("Processing: " + command);
    return processedCommand;
}
////////////////////////////////////////////////內涵時間顯示羅輯
///
///
/// 內涵時間顯示羅輯
///
QString MainWindow::processPreSend2(const QString& command)
{
    static QElapsedTimer timer;  // 使用静态变量以保持计时器状态
    static int lastProcessTime = 0;  // 上次处理命令的时间戳
    static int accumulatedTime = 0;  // 累积时间

    if (!timer.isValid())  // 检查计时器是否已启动
        timer.start(); // 开始计时

    // 在这里添加 textEdit_presend2 的检核逻辑
    QString processedCommand = command;
    ui->textEdit_presend2->append("Processing: " + command);

    int currentTime = timer.elapsed();
    int elapsedTime = currentTime - lastProcessTime;  // 计算与上一个命令之间的间隔
    accumulatedTime += elapsedTime;  // 累积时间
    lastProcessTime = currentTime;  // 更新上次处理命令的时间戳

    // 更新界面中的时间显示
    QString elapsedTimeText = QString::number(accumulatedTime) + " ms";
    ui->lineEdit_mainwindow_totaltimes->setText(elapsedTimeText);
    qDebug() << "Current Command Processing Interval:" << elapsedTimeText;
    qDebug() << "Accumulated Time:" << accumulatedTime << "ms";

    // 间隔超过三秒时重置时间
    if (elapsedTime >= 3000) {
        accumulatedTime = 0;  // 重置累积时间
        qDebug() << "Interval exceeding 3000 ms, resetting accumulated time.";
    }

    return processedCommand;
}




//多線程發射命令區///////////////////////////////////////////////////////////////////////////////////////////////

//sensorTCP相關  檔案名為sensor.cop sensor.h

void MainWindow::on_pushButton_sendMain_3_clicked()
{
    QString commands = ui->textEdit_Muti->toPlainText();
    QStringList commandList = commands.split('\n', Qt::SkipEmptyParts);

    sensorCommandQueue.clear();

    for (const QString& command : commandList) {
        if (command.startsWith("延迟:")) {
            bool ok;
            int delayMs = command.mid(3).trimmed().remove("ms").toInt(&ok);
            if (ok) {
                sensorCommandQueue.enqueue(qMakePair(QString(), delayMs));
                qDebug() << "延遲添加:" << delayMs << "ms";
            }
        }


///////////////////////////////////////////////////////////////////////////////////


        // 處理特殊 PT 命令格式 (100W:Point0-10000；Time0.00-1.00 格式)
        else if (command.contains("W:Point") && command.contains("；Time")) {
            // 檢查是否包含多軸命令 (使用 \\ 分隔)
            if (command.contains("\\\\")) {
                // 這是多軸特殊 PT 命令
                sensorCommandQueue.enqueue(qMakePair(command, -2)); // -2 表示特殊多軸 PT 命令
                qDebug() << "特殊多軸 PT 命令添加:" << command;
            } else {
                // 這是單軸特殊 PT 命令
                sensorCommandQueue.enqueue(qMakePair(command, -3)); // -3 表示特殊單軸 PT 命令
                qDebug() << "特殊單軸 PT 命令添加:" << command;
            }
        }


//////////////////////////////////////////////////////////////////////////////////////

        // 處理多軸同步命令
        else if (command.contains(",")) {
            sensorCommandQueue.enqueue(qMakePair(command, -1)); // -1 表示多軸命令
            qDebug() << "多軸命令添加:" << command;
        }
        // 處理單軸命令
        else if (command.contains(":")) {
            QStringList parts = command.split(":", Qt::SkipEmptyParts);
            if (parts.size() == 2) {
                QString motorName = parts[0].trimmed();
                QString motorCommand = parts[1].trimmed();
                
                // 檢查是否為 PVT 命令
                if (motorCommand.contains("PA") && motorCommand.contains("TA")) {
                    sensorCommandQueue.enqueue(qMakePair(command, 0));
                    qDebug() << "PVT 單軸命令添加:" << command;
                }
                // 檢查其他類型的命令
                else if (motorCommand.startsWith("T") || 
                        motorCommand.startsWith("0x") || 
                        motorCommand.startsWith("P")) {
                    sensorCommandQueue.enqueue(qMakePair(command, 0));
                    qDebug() << "單軸命令添加:" << command;
                }
            }
        }
        else if (command.startsWith("SENSOR485[") || command.startsWith("SENSOR[")) {
            sensorCommandQueue.enqueue(qMakePair(command, 0));
            qDebug() << "感測器命令添加:" << command;
        }
    }

    processSensorCommandQueue();
}

void MainWindow::processSensorCommandQueue() {
    qDebug() << "[PQ_D0] processSensorCommandQueue_Entry. Queue size:" << sensorCommandQueue.size(); // DEBUG 0
    if (!sensorCommandQueue.isEmpty()) {
        qDebug() << "[PQ_D0_Detail] First command:" << sensorCommandQueue.first().first << "Type:" << sensorCommandQueue.first().second;
    }
    

    // 檢查命令佇列是否為空
    if (sensorCommandQueue.isEmpty()) {
        qDebug() << "[PQ_D1] Queue is empty, returning."; // DEBUG 1
        return;
    }

    qDebug() << "[PQ_D2] Before static isProcessing. Queue size:" << sensorCommandQueue.size(); // DEBUG 2
    // 使用靜態變數來追蹤命令處理狀態
    static bool isProcessing = false;
    qDebug() << "[PQ_D3] After static isProcessing. isProcessing_Value_Before_Check:" << isProcessing; // DEBUG 3
    if (isProcessing) {
        qDebug() << "[PQ_D4] isProcessing is true, returning."; // DEBUG 4
        return;
    }
    isProcessing = true;
    qDebug() << "[PQ_D5] Set isProcessing to true."; // DEBUG 5
////////////////////////////////////////////////////////////////////////////////////////////
    try {
        qDebug() << "[PQ_D6] Entering try block."; // DEBUG 6
        // 檢查隊列中是否有特殊PT命令
        bool hasSpecialPTCommands = false;
        for (int i = 0; i < sensorCommandQueue.size(); i++) {
            int cmdType = sensorCommandQueue.at(i).second;
            if (cmdType == -2 || cmdType == -3) {
                hasSpecialPTCommands = true;
                break;
            }
        }
        qDebug() << "[PQ_D7] After checking for SpecialPTCommands. hasSpecialPTCommands:" << hasSpecialPTCommands << "Queue size:" << sensorCommandQueue.size(); // DEBUG 7

        if (hasSpecialPTCommands) {
            qDebug() << "[PQ_D8] Entered hasSpecialPTCommands block."; // DEBUG 8
            // 收集所有連續的特殊PT命令
            QList<QPair<QString, int>> ptCommandsToProcess;
            
            for (int i = 0; i < sensorCommandQueue.size(); i++) {
                QPair<QString, int> pair = sensorCommandQueue.at(i);
                int cmdType = pair.second;
                if (cmdType == -2 || cmdType == -3) {
                    ptCommandsToProcess.append(pair);
                } else {
                    break;
                }
            }
            qDebug() << "[PQ_D8a] ptCommandsToProcess count:" << ptCommandsToProcess.size(); // DEBUG 8a
            
            if (!ptCommandsToProcess.isEmpty()) {
                qDebug() << "[PQ_D9] Entered !ptCommandsToProcess.isEmpty() block for batch PT."; // DEBUG 9
                // 將所有命令合併為一個大命令
                QStringList combinedCommands;
                for (const auto& pair : ptCommandsToProcess) {
                    combinedCommands.append(pair.first);
                }
                
                // 合併所有命令（使用 \\ 作為分隔符）
                QString allCommands = combinedCommands.join("\\\\");
                
                // 尋找可用的 ethercat 窗口
                bool commandProcessed = false;
                
                for (ethercat* window : ethercatWindows) {
                    if (window->isInitialized()) {
                        // 找到已初始化的窗口，調用一次 SUPERPT_1 函數處理所有命令
                        qDebug() << "批量處理" << ptCommandsToProcess.size() << "條特殊PT命令";
                        window->SUPERPT_1(allCommands);
                        commandProcessed = true;
                        qDebug() << "已將批量特殊PT命令發送到 ethercat 窗口，調用 SUPERPT_1 函數";
                        break;
                    }
                }
                
                if (commandProcessed) {
                    // 從隊列中移除已處理的命令
                    for (int i = 0; i < ptCommandsToProcess.size(); i++) {
                        sensorCommandQueue.removeFirst();
                    }
                    qDebug() << "批量處理了" << ptCommandsToProcess.size() << "條特殊PT命令，從隊列中移除";
                } else {
                    qDebug() << "警告: 未找到已初始化的 ethercat 窗口來處理特殊PT命令";
                    // 從隊列中移除所有特殊PT命令，避免阻塞
                    for (int i = 0; i < ptCommandsToProcess.size(); i++) {
                        sensorCommandQueue.removeFirst();
                    }
                }
                
                qDebug() << "[PQ_D10] Before isProcessing = false in PT batch. Current isProcessing:" << isProcessing; // DEBUG 10
                isProcessing = false;
                if (!sensorCommandQueue.isEmpty()) {
                    qDebug() << "[PQ_D11] Queue not empty after PT batch, scheduling next."; // DEBUG 11
                    QTimer::singleShot(100, this, &MainWindow::processSensorCommandQueue);
                }
                qDebug() << "[PQ_D12] Returning from PT batch processing."; // DEBUG 12
                return; 
            }
        }
        qDebug() << "[PQ_D13] After hasSpecialPTCommands block. Queue size:" << sensorCommandQueue.size(); // DEBUG 13
        if(sensorCommandQueue.isEmpty()){ qDebug() << "[PQ_D13a] Queue became empty here!"; return;} // Early exit if queue got emptied

        // 檢查隊列中的第一個命令 (這段保留是為了處理舊代碼平滑過渡，可以在確認新批量處理功能正常後移除)
        QPair<QString, int> commandPair = sensorCommandQueue.first();
        qDebug() << "[PQ_D14] Checked first commandPair. Command:" << commandPair.first << "Type:" << commandPair.second; // DEBUG 14
        
        // 處理特殊 PT 命令
        if (commandPair.second == -2 || commandPair.second == -3) {
            qDebug() << "[PQ_D15] Entered single PT command block (type -2 or -3)."; // DEBUG 15
            // 出隊
            sensorCommandQueue.dequeue();
            
            // 處理特殊 PT 命令 (單軸或多軸)
            QString command = commandPair.first;
            qDebug() << "處理特殊 PT 命令:" << command;
            
            // 尋找可用的 ethercat 窗口
            bool commandProcessed = false;
            
            for (ethercat* window : ethercatWindows) {
                if (window->isInitialized()) {
                    // 找到已初始化的窗口，調用 SUPERPT_1 函數
                    window->SUPERPT_1(command);
                    commandProcessed = true;
                    qDebug() << "已將特殊 PT 命令發送到 ethercat 窗口，調用 SUPERPT_1 函數";
                    break;
                }
            }
            
            if (!commandProcessed) {
                qDebug() << "警告: 未找到已初始化的 ethercat 窗口來處理特殊 PT 命令";
            }
            
            // 處理完成，設置標誌並繼續檢查其他命令
            qDebug() << "[PQ_D16] Before isProcessing = false in single PT. Current isProcessing:" << isProcessing; // DEBUG 16
            isProcessing = false;
            if (!sensorCommandQueue.isEmpty()) {
                qDebug() << "[PQ_D17] Queue not empty after single PT, scheduling next."; // DEBUG 17
                QTimer::singleShot(100, this, &MainWindow::processSensorCommandQueue);
            }
            qDebug() << "[PQ_D18] Returning from single PT processing."; // DEBUG 18
            return; 
        }
        qDebug() << "[PQ_D19] After single PT command block. Queue size:" << sensorCommandQueue.size(); // DEBUG 19
        if(sensorCommandQueue.isEmpty()){ qDebug() << "[PQ_D19a] Queue became empty here!"; return;} // Early exit

        // 以下是原有的代碼，處理 PVT 命令
        QList<QPair<QString, int>> pvtCommandsToProcess;
        for (int i = 0; i < sensorCommandQueue.size(); i++) {
            QPair<QString, int> pair = sensorCommandQueue.at(i);
            QString cmd = pair.first;
            if (!cmd.isEmpty() && cmd.contains(":PA")) {
                pvtCommandsToProcess.append(pair);
            } else {
                break;
            }
        }
        qDebug() << "[PQ_D20] After collecting PVT commands. pvtCommandsToProcess count:" << pvtCommandsToProcess.size() << "Queue size:" << sensorCommandQueue.size(); // DEBUG 20

        if (pvtCommandsToProcess.isEmpty()) {
            qDebug() << "[PQ_D21] pvtCommandsToProcess is empty."; // DEBUG 21
            // -------- 新增的 SENSOR485/SENSOR 處理邏輯應該放在這裡 --------
            // 我們先不加新邏輯，只看日誌是否能走到這裡
            qDebug() << "[PQ_D21a] POTENTIAL SPOT FOR SENSOR COMMANDS. Queue size:" << sensorCommandQueue.size();
            if(!sensorCommandQueue.isEmpty()){
                 qDebug() << "[PQ_D21a_Detail] First command:" << sensorCommandQueue.first().first << "Type:" << sensorCommandQueue.first().second;
                 // 這裡就是我們判斷 SENSOR485 的地方
                 if (sensorCommandQueue.first().second == 0 && 
                     (sensorCommandQueue.first().first.startsWith("SENSOR485[") || sensorCommandQueue.first().first.startsWith("SENSOR["))) {
                     qDebug() << "[PQ_D21b_SENSOR_MATCH] Matched SENSOR command! Processing...";
                     QPair<QString, int> sensorCmdPair = sensorCommandQueue.dequeue();
                     processSensorCommand(sensorCmdPair.first);
                     isProcessing = false;
                     if (!sensorCommandQueue.isEmpty()) {
                         QTimer::singleShot(10, this, &MainWindow::processSensorCommandQueue);
                     }
                     return; // 已處理 SENSOR 命令
                 } else {
                    qDebug() << "[PQ_D21c_NO_SENSOR_MATCH] No SENSOR command match here, or type not 0.";
                 }
            } else {
                qDebug() << "[PQ_D21d_QUEUE_EMPTY_UNEXPECTED] Queue empty before SENSOR check after PVT empty.";
            }
            // -------- 結束 SENSOR485/SENSOR 處理邏輯 --------
            isProcessing = false;
            qDebug() << "[PQ_D22] Set isProcessing=false, returning as no PVT."; // DEBUG 22
            // 注意：如果上面 SENSOR 命令被處理並 return，這裡不會執行
            // 如果隊列中還有其他非 SENSOR/非 PVT 命令，它們會被下面的通用 QTimer 重新調度
            if (!sensorCommandQueue.isEmpty()) { // 確保在return前檢查是否需要再次調用
                 QTimer::singleShot(100, this, &MainWindow::processSensorCommandQueue); // 重新調度以處理剩餘命令
            }
            return; 
        }
        qDebug() << "[PQ_D23] After pvtCommandsToProcess.isEmpty() check (PVT commands exist)."; // DEBUG 23
        
        qDebug() << "收集到" << pvtCommandsToProcess.size() << "條PVT命令準備處理"; 
        // 按電機分組所有命令
        QMap<QString, QStringList> allWindowCommands;
        
        // 處理所有收集到的PVT命令
        for (const QPair<QString, int>& commandPair : pvtCommandsToProcess) {
            QString command = commandPair.first;
            
            if (command.contains(",")) {
                // 處理多軸命令
                QStringList commands = command.split(",", Qt::SkipEmptyParts);
                
                // 按窗口分組命令
                for (const QString& cmd : commands) {
                    QString motorName = cmd.left(cmd.indexOf(":")).trimmed();
                    for (ethercat* window : ethercatWindows) {
                        if (window->getName() == motorName && window->isInitialized()) {
                            allWindowCommands[motorName].append(cmd);
                            break;
                        }
                    }
                }
            } else {
                // 處理單軸命令
                QString motorName = command.left(command.indexOf(":")).trimmed();
                for (ethercat* window : ethercatWindows) {
                    if (window->getName() == motorName && window->isInitialized()) {
                        allWindowCommands[motorName].append(command);
                        break;
                    }
                }
            }
        }
        
        // 一次性發送所有分組命令到各個窗口
        bool commandProcessed = false;
        for (auto it = allWindowCommands.begin(); it != allWindowCommands.end(); ++it) {
            const QString& motorName = it.key();
            const QStringList& cmds = it.value();
            
            for (ethercat* window : ethercatWindows) {
                if (window->getName() == motorName && window->isInitialized()) {
                    qDebug() << "發送批量PVT命令到窗口:" << motorName << "命令數量:" << cmds.size();
                    window->sendPVTCommands(cmds);  // 一次發送多行命令
                    commandProcessed = true;
                    break;
                }
            }
        }
        
        // 從隊列中移除已處理的命令
        if (commandProcessed) {
            for (int i = 0; i < pvtCommandsToProcess.size(); i++) {
                sensorCommandQueue.removeFirst();
            }
            qDebug() << "批量處理了" << pvtCommandsToProcess.size() << "條PVT命令，從隊列中移除";
        } else {
            qDebug() << "警告: PVT命令未能成功處理";
        }

        qDebug() << "[PQ_D24] After PVT processing logic, before end of try. Queue size:" << sensorCommandQueue.size(); // DEBUG 24
        if(!sensorCommandQueue.isEmpty()) { 
            qDebug() << "[PQ_D24_Detail] First command:" << sensorCommandQueue.first().first; 
        }

    }
    catch (const std::exception& e) {
        qDebug() << "[PQ_D25] Exception caught:" << e.what(); // DEBUG 25
        qDebug() << "處理命令時發生錯誤:" << e.what();
    }

    qDebug() << "[PQ_D26] Before final isProcessing = false. Current isProcessing:" << isProcessing; // DEBUG 26
    isProcessing = false;

    qDebug() << "[PQ_D27] After final isProcessing = false. Queue size:" << sensorCommandQueue.size(); // DEBUG 27
    if (!sensorCommandQueue.isEmpty()) {
        qDebug() << "[PQ_D28] Queue not empty at end, scheduling next with 100ms delay."; // DEBUG 28
        QTimer::singleShot(100, this, &MainWindow::processSensorCommandQueue);
    }
    qDebug() << "[PQ_D29] processSensorCommandQueue_Exit."; // DEBUG 29
}


void MainWindow::processSensorCommand(const QString& command)
{
    qDebug() << "=== processSensorCommand 開始 ===";
    qDebug() << "接收到的完整命令:" << command;
    
    if (command.contains(":PA")) {
        QStringList commands = command.split(",", Qt::SkipEmptyParts);
        QMap<ethercat*, QStringList> windowCommands;
        
        // 先將命令按窗口分組
        for (const QString& cmd : commands) {
            QString motorName = cmd.left(cmd.indexOf(":")).trimmed();
            for (ethercat* window : ethercatWindows) {
                if (window->getName() == motorName && window->isInitialized()) {
                    windowCommands[window].append(cmd);
                    break;
                }
            }
        }
        
        // 對每個窗口發送其對應的命令
        for (auto it = windowCommands.begin(); it != windowCommands.end(); ++it) {
            it.key()->sendPVTCommands(it.value());
        }
    }

    QString processedCommand = processPreSend1(command);
    processedCommand = processPreSend2(processedCommand);

    // 處理 EtherCAT 命令
    if (command.contains(":")) {
        QStringList parts = command.split(":", Qt::SkipEmptyParts);
        if (parts.size() == 2) {
            QString motorName = parts[0].trimmed();
            QString motorCommand = parts[1].trimmed();
            
            // 找到對應的 EtherCAT 窗口並發送命令
            for (ethercat* window : ethercatWindows) {
                if (window->getName() == motorName) {
                    qDebug() << "Connecting motion completed signal for window:" << motorName;
                    connect(window, &ethercat::motionCompleted, this, [this, motorName]() {
                        qDebug() << "Motion completed signal received for:" << motorName;
                        QTimer::singleShot(0, this, &MainWindow::processSensorCommandQueue);
                    }, Qt::SingleShotConnection);
                    
                    window->sendCommand(motorCommand);
                    qDebug() << "Command sent to window:" << motorName;
                    return;
                }
            }
        }
    }

    if (command.startsWith("SENSOR485[")) {
        process485Command(command);
    } else {
        QRegularExpression rx("SENSOR\\[(.+)\\]:(.+)");
        QRegularExpressionMatch match = rx.match(command);
        if (match.hasMatch()) {
            QString sensorName = match.captured(1).trimmed();
            QString sensorCommand = match.captured(2).trimmed();

            if (sensorInstances.contains(sensorName)) {
                if (sensorCommand.startsWith("0x")) {
                    // 處理十六進制命令
                    sensorCommand = sensorCommand.mid(2); // 移除 "0x" 前綴
                    QByteArray hexData = QByteArray::fromHex(sensorCommand.toUtf8());
                    sensorInstances[sensorName]->sendHexCommand(hexData);
                } else {
                    // 處理 ASCII 命令
                    sensorInstances[sensorName]->sendCommand(sensorCommand);
                }
                qDebug() << "Command sent to sensor:" << sensorName;
            } else {
                qDebug() << "Sensor not found:" << sensorName;
            }
        } else {
            qDebug() << "Invalid command format:" << command;
        }
    }
}


////////////打開485傳感器界面///////////////////////////////////////////////////////////////////////
void MainWindow::on_pushButton_Open_Sensor_485_clicked()
{
    static int callCount = 0;
    qDebug() << "on_pushButton_Open_Sensor_485_clicked called, count:" << ++callCount;

    Sensor_485* newSensor485 = new Sensor_485(this);
    int sensorId = nextSensor485Id++;

    QString defaultName = QString("Sensor_485_%1").arg(sensorId);
    newSensor485->setName(defaultName);

    newSensor485->setWindowTitle(QString("Sensor 485 - %1").arg(sensorId));
    sensor485Instances[sensorId] = newSensor485;

    QString sensorName = newSensor485->getName();
    sensor485Connections[sensorName] = newSensor485;

    connect(newSensor485, &Sensor_485::nameChanged, this, [this, newSensor485](const QString& newName) {
        QString oldName = sensor485Connections.key(newSensor485);
        if (!oldName.isEmpty()) {
            sensor485Connections.remove(oldName);
        }
        sensor485Connections[newName] = newSensor485;
        qDebug() << "Sensor 485 name changed from" << oldName << "to" << newName;
    });

    // 使用 QLineEdit 的 textChanged 信号
    QLineEdit* nameLineEdit = newSensor485->findChild<QLineEdit*>("lineEdit_485Name");
    if (nameLineEdit) {
        connect(nameLineEdit, &QLineEdit::textChanged, this, [this, newSensor485]() {
            QString oldName = sensor485Connections.key(newSensor485);
            if (!oldName.isEmpty()) {
                sensor485Connections.remove(oldName);
            }
            QString newName = newSensor485->getName();
            sensor485Connections[newName] = newSensor485;
            qDebug() << "Sensor 485 name changed from" << oldName << "to" << newName;
        });
    }

    newSensor485->show();
    qDebug() << "New Sensor 485 instance created with ID:" << sensorId << "and name:" << sensorName;
}

void MainWindow::process485Command(const QString& command)
{
    QRegularExpression rx("SENSOR485\\[(.+)\\]:(.+)");
    QRegularExpressionMatch match = rx.match(command);
    if (match.hasMatch()) {
        QString sensorName = match.captured(1).trimmed();
        QString sensorCommand = match.captured(2).trimmed();

        qDebug() << "Processing 485 command for sensor:" << sensorName;
        qDebug() << "Command content:" << sensorCommand;

        if (sensor485Connections.contains(sensorName)) {
            Sensor_485* sensor = sensor485Connections[sensorName];
            if (sensor->isConnected()) {
                bool commandSent = false;
                if (sensorCommand.startsWith("0x")) {
                    // 处理16进制命令
                    sensorCommand = sensorCommand.mid(2).remove(" "); // 移除"0x"前缀和所有空格
                    QByteArray hexData = QByteArray::fromHex(sensorCommand.toUtf8());
                    qDebug() << "Hex command to be sent:" << hexData.toHex();
                    commandSent = sensor->sendHexCommand(hexData);
                } else {
                    // 处理普通文本命令
                    commandSent = sensor->sendCommand(sensorCommand);
                }

                if (commandSent) {
                    qDebug() << "Command sent successfully to 485 sensor:" << sensorName;
                } else {
                    qDebug() << "Failed to send command to 485 sensor:" << sensorName;
                }
            } else {
                qDebug() << "485 sensor not connected:" << sensorName;
            }
        } else {
            qDebug() << "485 sensor not found:" << sensorName;
        }
    } else {
        qDebug() << "Invalid 485 command format:" << command;
    }
}

///////////////////////EtherCat////////////////////////////
void MainWindow::on_pushButton_EtherCAT_clicked()
{
    try {
        ethercat* newEthercatWindow = new ethercat(nullptr);
        
        if (!newEthercatWindow) {
            throw std::runtime_error("Failed to create EtherCAT window");
        }
        
        newEthercatWindow->setWindowFlags(Qt::Window);
        
        connect(newEthercatWindow, &ethercat::windowClosed, this, [this, newEthercatWindow]() {
            ethercatWindows.removeOne(newEthercatWindow);
            if (m_ethercat == newEthercatWindow) {
                m_ethercat = nullptr;  // 如果關閉的是當前使用的實例，清空指針
            }
            newEthercatWindow->deleteLater();
        });
        
        ethercatWindows.append(newEthercatWindow);
        m_ethercat = newEthercatWindow;  // 保存最新創建的EtherCAT實例
        newEthercatWindow->show();
        
        qDebug() << "EtherCAT窗口已創建並保存到m_ethercat實例";
        
    } catch (const std::exception& e) {
        qDebug() << "EtherCAT error:" << e.what();
        QMessageBox::critical(this, "Error", 
            QString("EtherCAT error: %1").arg(e.what()));
    }
}
    
    

void MainWindow::checkEthercatConnection()
{
    if (ethercatWindow && !ethercatWindow->isVisible()) {
        ethercatConnectionTimer->stop();
        if (ethercatWindow) {
            ethercatWindow->disconnect();
            ethercatWindow->deleteLater();
            ethercatWindow = nullptr;
        }
        isEthercatConnected = false;
    }
}

void MainWindow::handleEthercatConnectionStatus(bool connected)
{
    isEthercatConnected = connected;
    if (!connected && ethercatWindow) {
        ethercatWindow->hide();
        ethercatWindow->deleteLater();
        ethercatWindow = nullptr;
    }
}

//主發送命令界面

//發出總訊號到設備PLC
void MainWindow::on_pushButton_sendorder_clicked() {
    qDebug() << "on_pushButton_sendorder_clicked called";


    commandQueue.clear(); // 先清空命令队列以准备新的命令
    qDebug() << "Command queue cleared.";

    QString allCommandsText = ui->textEdit_total_action->toPlainText();
    QStringList lines = allCommandsText.split("\n");

    QString accumulatedCommand; // 用于累积PLC命令块的变量
    bool isAccumulating = false; // 是否正在累积PLC命令块的标志

    foreach (const QString &line, lines) {
        QString trimmedLine = line.trimmed();
        if (trimmedLine.isEmpty()) continue; // 跳过空行

        if (trimmedLine.startsWith("PLC: ")) {
            // 开始累积新的PLC命令块
            isAccumulating = true;
            accumulatedCommand = trimmedLine; // 包含"PLC:"这一行
            qDebug() << "Starting new command block accumulation: " << trimmedLine; // Debug信息：开始累积新的PLC命令块
        } else if (trimmedLine.contains("#end") && isAccumulating) {
            // 命令块结束，包含此行并发送
            accumulatedCommand += "\n" + trimmedLine; // 累积包含"#end"的这一行
            commandQueue.enqueue(qMakePair(accumulatedCommand, 0)); // 将整个PLC命令块加入队列
            qDebug() << "Command block accumulated and enqueued: " << accumulatedCommand; // Debug信息：命令块累积并加入队列
            isAccumulating = false; // 重置累积状态
            accumulatedCommand.clear(); // 清空累积的PLC命令字符串
        } else if (isAccumulating) {
            // 如果正在累积PLC命令块，则继续累积当前行
            accumulatedCommand += "\n" + trimmedLine;
        } else if (trimmedLine.startsWith("延迟:")) {
            // 独立处理"延迟:"命令
            bool ok;
            int delayMs = trimmedLine.mid(3).chopped(2).toInt(&ok);
            if (ok) {
                commandQueue.enqueue(qMakePair(QString(), delayMs)); // 添加延迟命令
                qDebug() << "Delay command processed and enqueued: " << delayMs << "ms"; // Debug信息：处理并加入延迟命令
            } else {
                qDebug() << "Invalid delay command format: " << trimmedLine; // Debug信息：无效的延迟命令格式
            }
        }
    }
    qDebug() << "Preparing to send commands. Total commands in queue: " << commandQueue.size(); // Debug信息：准备发送命令，队列中命令总数
    sendCommands(); // 调用sendCommands函数处理和发送队列中的命令

    // 在处理完上述逻辑之后，直接调用 on_pushButton_sendMain_2_clicked 函数
    on_pushButton_sendMain_2_clicked();
}


//delay命令函數//三個函數的執行順序。需依照此順序:sendCommands()   processNextCommand()    sendCommand
void MainWindow::sendCommands() {
    qDebug() << "sendCommands called, command queue size:" << commandQueue.size() << "at time:" << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");

    qDebug() << "Finished adding commands and delays to queue at time:" << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");

    // 新增代码：在这里遍历commandQueue并打印其内容
    qDebug() << "Command queue contents after processing:";
    for (const auto &commandPair : std::as_const(commandQueue)) {
        if (commandPair.second == 0 && !commandPair.first.isEmpty()) {
            qDebug() << "Command:" << commandPair.first << ", Delay: none";
        } else if (commandPair.first.isEmpty()) {
            qDebug() << "Delay only:" << commandPair.second << "ms";
        }
    }

    processNextCommand(); // 开始处理命令队列
}


void MainWindow::processNextCommand() {



    qDebug() << "processNextCommand called, command queue size:" << commandQueue.size() << "at time:" << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    if (commandQueue.isEmpty()) return; // 如果队列为空，则完成所有命令的处理

    // Debug 输出 commandQueue 的所有内容，使用 std::as_const
    qDebug() << "commandQueue contents:";
    for (const auto &commandPair : std::as_const(commandQueue)) {
        QString command = commandPair.first;
        int delay = commandPair.second;
        if (!command.isEmpty()) {
            qDebug() << "Command:" << command << ", Delay:" << delay << "ms";
        } else {
            qDebug() << "Delay only:" << delay << "ms";
        }
    }

    auto commandPair = commandQueue.dequeue(); // 取出队列中的第一个命令

    if (commandPair.second > 0) {
        qDebug() << "Delay encountered, waiting for:" << commandPair.second << "ms";
        // 如果有延迟，则等待延迟时间后再次调用processNextCommand
        QTimer::singleShot(commandPair.second, this, &MainWindow::processNextCommand);
    } else {
        // 发送命令
        qDebug() << "Processing command:" << commandPair.first;
        sendCommand(commandPair.first);
        QTimer::singleShot(0, this, &MainWindow::processNextCommand); // 立即处理下一个命令，但允许事件循环继续
    }
}



//請注意，在使用16進制格式時，命令應該以 "0x"  開頭，以便系統識別為16進制命令。
void MainWindow::sendCommand(const QString& command) {
    qDebug() << "sendCommand called with command:" << command << "at time:" << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    if (command.isEmpty()) return;

    QString actualCommand = command;
    if (actualCommand.startsWith("PLC: ")) {
        actualCommand = actualCommand.mid(5);
    }

    if (actualCommand.endsWith("\n#end")) {
        actualCommand.chop(5);
    }

    // 檢查串口是否打開，如果沒有則打開
    if (!serialPort->isOpen()) {
        // ... [保持原有的串口打開邏輯不變] ...
    }

    // 檢測命令是否為16進制
    bool isHex = false;
    QByteArray commandData;
    if (actualCommand.startsWith("0x") || actualCommand.startsWith("0X")) {
        actualCommand = actualCommand.mid(2);
        isHex = true;
    }

    if (isHex) {
        // 處理16進制命令
        commandData = QByteArray::fromHex(actualCommand.toUtf8());
        qDebug() << "Sending PLC command in HEX format:" << commandData.toHex() << "at time:" << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    } else {
        // 處理普通文本命令
        commandData = actualCommand.toUtf8();
        qDebug() << "Sending PLC command in text format:" << actualCommand << "at time:" << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    }

    qint64 bytesWritten = serialPort->write(commandData);
    serialPort->flush();
    if (bytesWritten == -1) {
        qDebug() << "Failed to send command to serial port at time:" << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
        serialPort->close();
    } else {
        qDebug() << "Command sent to serial port successfully, bytes written:" << bytesWritten << "at time:" << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    }
}





bool MainWindow::checkTrial() {
    QString inputUser = ui->lineEdit_user->text();
    QString inputKey = ui->lineEdit_2key->text();

    // 预设的正确用户名和密钥
    QString correctUser = "admin";
    QString correctKey = "pass1234";

    QSettings settings("YourCompany", "YourApp");

    // 用户名和密钥匹配时，重置试用期或使用次数
    if (inputUser == correctUser && inputKey == correctKey) {
        // 重置试用期开始日期和使用次数
        settings.setValue("firstRun", QDateTime::currentDateTime());
        settings.setValue("runCount", 0);
        qDebug() << "激活成功，试用期和使用次数已重置。";
        return true; // 激活成功
    }

    const QDateTime firstRun = settings.value("firstRun", QDateTime::currentDateTime()).toDateTime();
    const int runCount = settings.value("runCount", 0).toInt();

    // 更新运行次数
    settings.setValue("runCount", runCount + 1);

    // 计算剩余天数和剩余使用次数
    int daysUsed = firstRun.daysTo(QDateTime::currentDateTime());
    int daysLeft = 5 - daysUsed;
    int runsLeft = 100 - runCount;

    qDebug() << "您已使用软件" << daysUsed << "天，剩余试用天数：" << daysLeft;
    qDebug() << "这是第" << runCount << "次运行软件，剩余使用次数：" << runsLeft;

    // 检查试用期限（15天）
    if (firstRun.addDays(5) < QDateTime::currentDateTime()) {
        return false; // 试用期已结束
    }

    // 检查使用次数（100次）
    if (runCount > 100) {
        return false; // 超过使用次数
    }

    return true; // 试用有效或激活成功
}


void MainWindow::onActivationAttempt() {
    QString inputUser = ui->lineEdit_user->text();
    QString inputKey = ui->lineEdit_2key->text();

    // 假设的正确用户名和密钥
    QString correctUser = "admin";
    QString correctKey = "pass1234";

    if (inputUser == correctUser && inputKey == correctKey) {
        QMessageBox::information(this, "激活成功", "软件激活成功，您现在可以完全访问所有功能。");
        // 激活成功，可以启用所有界面元素
        setUiEnabled(true);
    } else {
        QMessageBox::warning(this, "激活失败", "激活码错误，请重新输入。");
        // 激活失败，禁用除激活相关外的所有界面元素
        setUiEnabled(false);
        // 这里不再直接退出程序
    }
}

void MainWindow::setWidgetEnabled(QWidget *widget, bool enabled) {
    qDebug() << "Starting setWidgetEnabled for widget:" << widget->objectName() << "with enabled:" << enabled;

    foreach(QObject *obj, widget->children()) {
        QWidget *childWidget = qobject_cast<QWidget*>(obj);
        if (childWidget) {
            // 直接跳过lineEdit_user、lineEdit_2key和pushButton_6Active，不更改它们的状态
            if (childWidget == ui->lineEdit_user || childWidget == ui->lineEdit_2key || childWidget == ui->pushButton_6Active) {
                continue;
            }

            childWidget->setEnabled(enabled);
            qDebug() << "Processing child widget:" << childWidget->objectName() << "with enabled:" << enabled;
            // 递归设置子控件，但不改变特定控件状态
            setWidgetEnabled(childWidget, enabled);
        }
    }
    qDebug() << "Finished setWidgetEnabled for widget:" << widget->objectName();
}

void MainWindow::setUiEnabled(bool enabled) {
    // 禁用或启用除激活相关控件外的所有界面元素
    setWidgetEnabled(ui->centralwidget, enabled);

    // 确保激活相关的输入框和按钮始终启用
    ui->lineEdit_user->setEnabled(true);
    ui->lineEdit_2key->setEnabled(true);
    ui->pushButton_6Active->setEnabled(true);
}

//1510A命令發送
// HEX命令发送1510A命令發送
// HEX命令发送
void MainWindow::on_pushButton_sendMain_2_clicked() {
    // 从textEdit_Hex获取命令文本
    QString commandsText = ui->textEdit_Hex->toPlainText();
    QStringList commands = commandsText.split("\n", Qt::SkipEmptyParts);

    for (const QString& commandLine : commands) {
        QString trimmedLine = commandLine.trimmed();
        // 检查是否是延迟命令
        if (trimmedLine.startsWith("延迟:")) {
            // 提取延迟时间（假设格式为 "延迟: 1000ms"）
            bool ok;


            QString delayString = trimmedLine.mid(trimmedLine.indexOf(':') + 1).trimmed().chopped(2);
            int delayMs = delayString.toInt(&ok);


            if (ok) {
                // 将延迟时间加入队列
                robotArmCommandQueue.enqueue(qMakePair(QString(), delayMs));
            } else {
                qDebug() << "[MainWindow] 无效的延迟命令格式：" << trimmedLine;
            }
        } else if (trimmedLine.startsWith("ROBO:")) {
            // 处理ROBO命令
            QString commandWithoutPrefix = trimmedLine.mid(5).trimmed();
            robotArmCommandQueue.enqueue(qMakePair(commandWithoutPrefix, 0));
        }
    }

    // 开始处理队列中的命令
    processRobotArmCommands();
}


void MainWindow::processRobotArmCommands() {
    if (robotArmCommandQueue.isEmpty()) return;

    QPair<QString, int> commandPair = robotArmCommandQueue.dequeue();
    QString command = commandPair.first;
    int delayTime = commandPair.second;

    if (!command.isEmpty()) {
        // 发送命令
        sendRobotArmCommand(command);
    }

    // 如果有延迟，使用定时器来延迟处理下一个命令
    if (delayTime > 0) {
        QTimer::singleShot(delayTime, this, &MainWindow::processRobotArmCommands);
    } else {
        // 无延迟，立即递归处理下一个命令
        QMetaObject::invokeMethod(this, "processRobotArmCommands", Qt::QueuedConnection);
    }
}


void MainWindow::sendRobotArmCommand(const QString& command) {
    QString hexString = command.trimmed();


    QByteArray binaryData = QByteArray::fromHex(hexString.toUtf8());

    // 在发送前输出处理后的命令字符串和即将发送的二进制数据的十六进制表示
    qDebug() << "[MainWindow] 处理后的命令字符串：" << hexString;
    qDebug() << "[MainWindow] 即将发送的二进制数据（十六进制表示）：" << binaryData.toHex();

    // 获取RobotArm类实例的tcpSocket
    QTcpSocket* robotArmTcpSocket = robotArmInstance->getTcpSocket();

    if (robotArmInstance && robotArmTcpSocket->state() == QAbstractSocket::ConnectedState) {
        // 发送二进制数据到服务器
        robotArmTcpSocket->write(binaryData);
        qDebug() << "[MainWindow] 向服务器发送十六进制消息：" << binaryData.toHex();
    } else {
        qDebug() << "[MainWindow] TCP连接未建立或RobotArm实例未初始化";
    }

}

void MainWindow::on_pushButton_Opendsize_clicked()
{
    if (!dSizeDialog) {
        dSizeDialog = new D_size(this);
    }
    dSizeDialog->show();
}


//////////////////////////////////////顯示   打开命令调度器   窗口//////////////////////////////////////////////////////////

void MainWindow::openCommandScheduler() {
    qDebug() << "Attempting to open CommandScheduler";
    // 仅创建并显示 CommandScheduler，不进行任何其他逻辑处理
    if (!commandScheduler) {
        commandScheduler = new CommandScheduler(this);
        qDebug() << "CommandScheduler created:" << commandScheduler;
    }
    commandScheduler->show();
    qDebug() << "CommandScheduler shown";
}

void MainWindow::on_pushButton_Plc_AddToMainwinder_clicked()
{
    try {
        if (!dialogPlc) {
            qDebug() << "Dialog_Plc instance is null";
            return;
        }

        // 获取当前文本编辑框中的内容
        QString existingText = ui->textEdit_total_action->toPlainText();
        
        // 发出信号请求 PLC 命令
        // 注意：这里使用已有的 addPlcOrderToMainWindow 信号
        // 该信号会触发 onPlcOrderAdded 槽函数，我们已经在构造函数中连接了这个信号
        
        // 如果需要在现有文本后添加新行
        if (!existingText.isEmpty() && !existingText.endsWith('\n')) {
            ui->textEdit_total_action->append("");
        }

        qDebug() << "PLC Add To Mainwinder button clicked";

    } catch (const std::exception& e) {
        qDebug() << "Error in on_pushButton_Plc_AddToMainwinder_clicked:" << e.what();
        QMessageBox::critical(this, "错误", QString("添加命令时发生错误: %1").arg(e.what()));
    }
}

void MainWindow::on_pushButton_Open_Sensor_2_clicked()
{
    // 創建新的 Sensor 窗口
    Sensor* newSensor = new Sensor(this);
    
    // 設置窗口屬性
    newSensor->setWindowFlags(Qt::Window | Qt::WindowStaysOnTopHint);
    newSensor->setAttribute(Qt::WA_DeleteOnClose);
    
    // 顯示窗口
    newSensor->show();
}

void MainWindow::processCommand(const QString& command) {
    // 根據命令前綴決定如何處理
    if (command.startsWith("SENSOR:")) {
        processSensorCommand(command);
    } else if (command.startsWith("485:")) {
        process485Command(command);
    } else if (command.startsWith("ROBO:")) {
        sendRobotArmCommand(command);
    } else {
        // 默認處理邏輯
        sendCommand(command);
    }
}

void MainWindow::setupDebugOutput()
{
    debugTextEdit = findChild<QTextEdit*>("textEdit_Debug");
    if (!debugTextEdit) {
        qWarning() << "Debug text edit not found!";
        return;
    }
    
    // 設置全局消息處理器
    qInstallMessageHandler([](QtMsgType type, const QMessageLogContext &context, const QString &msg) {
        QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
        QString formattedMessage = QString("[%1] %2: %3")
                                    .arg(timestamp)
                                    .arg(type == QtDebugMsg ? "Debug" : 
                                         type == QtWarningMsg ? "Warning" : 
                                         type == QtCriticalMsg ? "Critical" : "Fatal")
                                    .arg(msg);
        
        // 使用 QMetaObject::invokeMethod 確保在主線程中更新 UI
        QMetaObject::invokeMethod(qApp->activeWindow(), "handleDebugOutput",
                                Qt::QueuedConnection,
                                Q_ARG(QtMsgType, type),
                                Q_ARG(QString, formattedMessage));
    });
}

void MainWindow::handleDebugOutput(QtMsgType type, const QString &msg)
{
    if (!debugTextEdit) return;
    
    // 設置不同類型消息的顏色
    QColor color;
    switch (type) {
        case QtDebugMsg:
            color = QColor(0, 255, 0);  // 綠色
            break;
        case QtWarningMsg:
            color = QColor(255, 255, 0);  // 黃色
            break;
        case QtCriticalMsg:
        case QtFatalMsg:
            color = QColor(255, 0, 0);  // 紅色
            break;
        default:
            color = QColor(0, 255, 0);  // 綠色
    }
    
    debugTextEdit->setTextColor(color);
    debugTextEdit->append(msg);
    
    //////////////////////////////////////////////// 限制顯示行數
    const int MAX_LINES = 50000;
    QStringList lines = debugTextEdit->toPlainText().split('\n');
    if (lines.size() > MAX_LINES) {
        lines = lines.mid(lines.size() - MAX_LINES);
        debugTextEdit->setPlainText(lines.join('\n'));
    }
    
    // 滾滾動到底部
    QScrollBar *scrollBar = debugTextEdit->verticalScrollBar();
    scrollBar->setValue(scrollBar->maximum());
}

void MainWindow::clearDebugOutput()
{
    if (debugTextEdit) {
        debugTextEdit->clear();
    }
}

// pushButton_go_0 按鈕槽函數 - 觸發EtherCAT歸零功能
void MainWindow::on_pushButton_go_0_clicked()
{
    qDebug() << "MainWindow: pushButton_go_0 按鈕被點擊";
    
    // 檢查是否有EtherCAT實例
    if (!m_ethercat) {
        qDebug() << "錯誤: EtherCAT實例不存在，請先打開EtherCAT窗口";
        return;
    }
    
    // 調用EtherCAT實例的歸零函數
    m_ethercat->executeAxisHoming();
}









