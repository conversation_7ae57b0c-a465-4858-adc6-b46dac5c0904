#include "AxisSynchronizer.h"

AxisSynchronizer::AxisSynchronizer(QObject *parent) 
    : QObject(parent) {
}

AxisSynchronizer::~AxisSynchronizer() {
}

void AxisSynchronizer::addAxis(short axis) {
    QMutexLocker locker(&m_mutex);
    m_pendingAxes.insert(axis);
    emit axisReadyChanged(axis, false);
    
    qDebug() << "同步器: 添加軸" << axis << "到同步集合，當前等待軸數量:" << m_pendingAxes.size();
}

void AxisSynchronizer::resetBarrier() {
    QMutexLocker locker(&m_mutex);
    m_pendingAxes.clear();
    qDebug() << "同步器: 重置同步障壁，清空等待軸集合";
}

int AxisSynchronizer::waitingCount() const {
    QMutexLocker locker(&m_mutex);
    return m_pendingAxes.size();
}

QSet<short> AxisSynchronizer::pendingAxes() const {
    QMutexLocker locker(&m_mutex);
    return m_pendingAxes;
}

void AxisSynchronizer::axisReady(short axis) {
    QMutexLocker locker(&m_mutex);
    
    if (m_pendingAxes.contains(axis)) {
        m_pendingAxes.remove(axis);
        emit axisReadyChanged(axis, true);
        
        qDebug() << "同步器: 軸" << axis << "已就緒，剩餘等待軸數量:" << m_pendingAxes.size();
        
        if (m_pendingAxes.isEmpty()) {
            qDebug() << "同步器: 所有軸都已就緒，發出allAxesReady信號";
            emit allAxesReady();
        }
    } else {
        qDebug() << "同步器: 警告 - 軸" << axis << "不在等待集合中";
    }
} 