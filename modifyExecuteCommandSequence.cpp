// modifyExecuteCommandSequence.cpp - 修改命令序列執行函數
#include "ethercat.h"

// 修改現有的命令序列執行函數，以支持並行處理
void ethercat::executeCommandSequenceNew(const QStringList& commands) {
    // 檢查是否需要啟用並行計算模式
    if (useParallelCalculation) {
        executeParallelCommands(commands);
        return;
    }
    
    // 原有執行邏輯保持不變
    qDebug() << "\n===== [PVT斷點3-命令序列執行開始] =====";
    qDebug() << "- 命令數量：" << commands.size();
    qDebug() << "- 當前時間：" << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    qDebug() << "- 執行狀態：" << isExecutingCommand;
    qDebug() << "- 待處理序列數量：" << pendingSequences.size();
    
    // 【重要】確保表單ID已初始化
    initializeTableIds();
    
    // 清除PVT緩存，但不重置命令隊列（防止丟失命令）
    axisPVTCache.clear();
    
    // 解析命令到隊列
    parseCommandsToQueues(commands);
    
    qDebug() << "\n===== [PVT斷點3.2-命令解析到隊列完成] =====";
    qDebug() << "- 总共有多少根轴的命令在列队：" << axisCommandQueues.size();
    qDebug() << "- 具體隊列狀態：";
    for (auto it = axisCommandQueues.begin(); it != axisCommandQueues.end(); ++it) {
        qDebug() << "  軸" << it.key() << "的隊列長度：" << it.value().size();
    }
    
    // 處理每個軸的第一個命令
    QSet<short> activeAxes;
    for (auto it = axisCommandQueues.begin(); it != axisCommandQueues.end(); ++it) {
        short axis = it.key();
        
        if (!it.value().isEmpty()) {
            QString command = it.value().dequeue();
            parseCommandToCache(command);
            activeAxes.insert(axis);
        }
    }
    
    qDebug() << "\n===== [PVT斷點3.3-處理第一個命令完成] =====";
    qDebug() << "- 已激活的轴数量：" << activeAxes.size();
    
    // 按照廠商流程：先清除錯誤，再使能軸，然後設置PVT模式
    short mask = 0;
    for (short axis : activeAxes) {
        // 1. 先清除軸錯誤
        GTN_ClrSts(cardCore, axis);
        QThread::msleep(50);
        
        // 2. 檢查軸是否已經使能，如果沒有才使能
        long status;
        if (GTN_GetSts(cardCore, axis, &status) == 0) {
            // 檢查使能狀態位 (0x200)
            if (!(status & 0x200)) {
                // 軸未使能，需要使能
                GTN_AxisOn(cardCore, axis);
                qDebug() << "軸" << axis << "未使能，正在使能";
                Sleep(1000);  // 等待使能完成
            }
        }
        
        // 3. 設置為PVT模式
        if (GTN_PrfPvt(cardCore, axis) != 0) {
            qDebug() << "錯誤: 設置軸" << axis << "為PVT模式失敗";
            continue;
        }
        qDebug() << "設置軸" << axis << "為PVT模式成功";
        
        // 4. 上傳PVT數據到當前表單
        short currentTableId = getCurrentTableId(axis);
        
        qDebug() << "\n===== [PVT斷點3.5-獲取當前表單ID] =====";
        qDebug() << "- 軸號：" << axis;
        qDebug() << "- 當前表單ID：" << currentTableId;
        
        // 添加詳細的表單ID信息打印
        if (axisTableIds.contains(axis)) {
            const TableIdPair& pair = axisTableIds[axis];
            qDebug() << "现在使用的表單ID: " << currentTableId;
        }
        
        uploadPVTData(axis, currentTableId);
        
        qDebug() << "\n===== [PVT斷點3.6-PVT數據上傳完成] =====";
        qDebug() << "- 軸號：" << axis;
        qDebug() << "- 上傳到表單ID：" << currentTableId;
        
        // 5. 選擇PVT表
        if (GTN_PvtTableSelect(cardCore, axis, currentTableId) != 0) {
            qDebug() << "錯誤: 軸" << axis << "選擇PVT表格失敗";
            continue;
        }
        
        qDebug() << "\n===== [PVT斷點3.7-選擇PVT表完成] =====";
        qDebug() << "- 軸號：" << axis;
        qDebug() << "- 選擇的表單ID：" << currentTableId;
        
        // 6. 添加到啟動掩碼
        mask |= (1 << (axis - 1));
    }
    
    // 如果沒有可用的軸，則退出
    if (mask == 0) {
        qDebug() << "錯誤: 沒有可用的軸可以啟動";
        return;
    }
    
    qDebug() << "\n===== [PVT斷點3.8-準備啟動PVT運動] =====";
    qDebug() << "- 啟動掩碼：" << QString("0x%1").arg(mask, 0, 16);
    
    // 7. 啟動PVT運動
    int result = GTN_PvtStart(cardCore, mask);
    
    qDebug() << "\n===== [PVT斷點3.9-PVT運動啟動結果] =====";
    qDebug() << "- 啟動結果：" << result;
    
    // 8. 監控運動
    if (result == 0) {
        qDebug() << "\n===== [PVT斷點3.10-開始監控PVT運動] =====";
        monitorAllPVTMotions();
    } else {
        qDebug() << "錯誤: 啟動PVT運動失敗，錯誤碼:" << result;
    }
} 