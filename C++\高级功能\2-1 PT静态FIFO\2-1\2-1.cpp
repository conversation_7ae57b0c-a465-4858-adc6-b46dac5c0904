// 2-1.cpp : 定義控制台應用程序的入口點。
//

#include "stdafx.h"
#include "windows.h"
#include "conio.h"
#include "gts.h"


#define CORE 1			// 控制卡核心編號
#define AXIS 1			// 控制軸編號

// 該函數用於處理GTN指令的執行結果，command為指令名稱，error為指令執行返回值
void commandhandler(char *command, short error)
{
	// 當指令執行返回值為非0，說明指令執行出錯，在屏幕上顯示
	if (error)
	{
		printf("%s = %d\n", command, error);
	}
}

int _tmain(int argc, _TCHAR* argv[])
{
	short sRtn, space;
	short sEcatSts;					// ECAT通訊狀態標誌
	double pos;						// 細分位置
	long time;						// 細分時間
	long lAxisSts;					// 軸狀態
	double prfPos, prfVel;			// 規劃位置,規劃速度

	// 打開運動控制卡
	sRtn = GTN_Open();
	// 指令返回值檢查
	commandhandler("GTN_Open", sRtn);
	if(sRtn)
	{
		printf("Failure to access cord!\n");
		return -1;
	}
	sRtn = GTN_InitEcatComm(CORE);
	commandhandler("GTN_InitEcatComm", sRtn);
	if(sRtn)
	{
		printf("EtherCAT communication error!\n");
		return -1;
	}
	do {// 查詢EtherCAT通訊是否準備就緒
		sRtn = GTN_IsEcatReady(CORE, &sEcatSts);
	} while (sEcatSts != 1 || sRtn != 0);
	// 啟動EtherCAT通訊
	sRtn = GTN_StartEcatComm(CORE);
	commandhandler("GTN_StartEcatComm", sRtn);
	// 系統復位
	sRtn = GTN_Reset(CORE);
	commandhandler("GTN_Reset", sRtn);
	// 加載運動控制卡
	// 注意：配置文件取決於具體的軸驅動位置
	sRtn = GTN_LoadConfig(CORE, "GTS800.cfg");
	commandhandler("GTN_LoadConfig", sRtn);
	// 清除所有軸驅動器報警
	sRtn = GTN_ClrSts(CORE, 1, 8);
	commandhandler("GTN_ClrSts", sRtn);
	// 伺服使能
	sRtn = GTN_AxisOn(CORE, AXIS);
	commandhandler("GTN_AxisOn", sRtn);
	// 位置清零
	sRtn = GTN_ZeroPos(CORE, AXIS);
	commandhandler("GTN_ZeroPos", sRtn);
	// 將AXIS設置為PT模式
	sRtn = GTN_PrfPt(CORE, AXIS);
	commandhandler("GTN_PrfPt", sRtn);
	// 清空AXIS的PT FIFO
	sRtn = GTN_PtClear(CORE, AXIS);
	commandhandler("GTN_PtClear", sRtn);
	// 查詢PT模式FIFO的剩餘空間
	sRtn = GTN_PtSpace(CORE, AXIS, &space);
	commandhandler("GTN_PtSpace", sRtn);
	printf("space=%d\n",space);
	// 向FIFO中添加運動數據
	pos = 1024;
	time = 1024;
	sRtn = GTN_PtData(CORE, AXIS, pos, time);
	commandhandler("GTN_PtData", sRtn);
	// 查詢PT模式FIFO的剩餘空間
	sRtn = GTN_PtSpace(CORE, AXIS, &space);
	commandhandler("GTN_PtSpace", sRtn);
	printf("space=%d\n",space);
	// 向FIFO中添加運動數據
	pos += 2048;
	time += 1024;
	sRtn = GTN_PtData(CORE, AXIS, pos, time);
	commandhandler("GTN_PtData", sRtn);
	// 查詢PT模式FIFO的剩餘空間
	sRtn = GTN_PtSpace(CORE, AXIS, &space);
	commandhandler("GTN_PtSpace", sRtn);
	printf("space=%d\n",space);
	// 向FIFO中添加運動數據
	pos += 1024;
	time += 1024;
	sRtn = GTN_PtData(CORE, AXIS, pos, time);
	commandhandler("GTN_PtData", sRtn);
	// 啟動PT運動
	sRtn = GTN_PtStart(CORE, 1 << (AXIS - 1));
	commandhandler("GTN_PtStart", sRtn);
	
	while (1)
	{
		// 讀取AXIS的軸狀態
		sRtn = GTN_GetSts(CORE, AXIS, &lAxisSts);
		commandhandler("GTN_GetSts", sRtn);
		// 讀取AXIS的規劃位置
		sRtn = GTN_GetPrfPos(CORE, AXIS, &prfPos);
		commandhandler("GTN_GetPrfPos", sRtn);
		// 讀取AXIS的規劃速度
		sRtn = GTN_GetPrfVel(CORE, AXIS, &prfVel);
		commandhandler("GTN_GetPrfVel", sRtn);
		printf("lAxisSts=0x%-10lxprfVel=%-10.2lfprfPos=%-10.1lf\r", lAxisSts, prfVel, prfPos);
		if(~lAxisSts & 0x400)
		{
			printf("\n請按任意鍵停止\n");
			_getch();
			break;
		}
	}

	// 伺服關閉
	sRtn = GTN_AxisOff(CORE, AXIS);
	commandhandler("GTN_AxisOff", sRtn);
	sRtn = GTN_TerminateEcatComm(CORE);
	commandhandler("GTN_TerminateEcatComm", sRtn);
	// 關閉運動控制卡
	sRtn = GTN_Close();
	commandhandler("GTN_Close", sRtn);
	return 0;
}

