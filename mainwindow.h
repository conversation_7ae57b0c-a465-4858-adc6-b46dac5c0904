#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QMap>
#include <QList>
#include <QQueue>
#include <QPair>
#include <QTimer>
#include <QSerialPort>
#include "commandscheduler.h" // 引入新界面的头文件
#include <QElapsedTimer>

// 引入自定义类的头文件
#include "dialog.h"
#include "dialog_plc.h"
#include "sensor.h"
#include "sensor_485.h"
#include "robotarm.h"
#include "ethercat.h"
#include "d_size.h"

QT_BEGIN_NAMESPACE
namespace Ui { class MainWindow; }
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口指针，默认为 nullptr
     */
    MainWindow(QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~MainWindow();

    void sendCommandsFromTextEditPresend2();

private slots:
    // UI 按钮的槽函数
    void on_pushButton_3_clicked();
    void on_pushButton_PLC_clicked();
    void on_pushButton_Logic_clicked();
    void on_pushButton_sendorder_clicked();
    void on_pushButton_Open_Sensor_clicked();
    void onShowRBClicked();
    void on_pushButton_sendMain_3_clicked();
    void on_pushButton_Open_Sensor_485_clicked();
    void on_pushButton_EtherCAT_clicked();
    void on_pushButton_sendMain_2_clicked();
    void on_pushButton_Opendsize_clicked();
    void on_pushButton_Plc_AddToMainwinder_clicked();
    void on_pushButton_Open_Sensor_2_clicked();
    void on_pushButton_go_0_clicked();  // 歸零按鈕槽函數

    // 激活相关槽函数
    void onActivationAttempt();

    // 信号处理槽函数
    void handleUpdateTextSummary(const QString &summary);
    void onPlcOrderAdded(QPair<QString, QString> order);
    void handleCommandIssued(QPair<QString, QString> order);

    // 命令发送相关槽函数
    void processSensorCommandQueue();
    void processSensorCommand(const QString& command);
    void process485Command(const QString& command);
    void processRobotArmCommands();
    void sendRobotArmCommand(const QString& command);

    // 设置界面槽函数
    void setWidgetEnabled(QWidget *widget, bool enabled);
    void setUiEnabled(bool enabled);

    void openCommandScheduler();

    void checkEthercatConnection();
    void handleEthercatConnectionStatus(bool connected);

    void processCommand(const QString& command);

    void setupDebugOutput();
    void handleDebugOutput(QtMsgType type, const QString &msg);
    void clearDebugOutput();

private:
    
    Ui::MainWindow *ui;

    // 对话框和子窗口指针
    D_size* dSizeDialog;
    ethercat* ethercatWindow;
    Dialog* dialogInstance;
    Dialog_Plc* dialogPlc;
    RobotArm* robotArmInstance;

    // 串口对象
    QSerialPort* serialPort;

    // 定时器对象
    QTimer* sensorCommandTimer;

    // 传感器和 EtherCAT 窗口的容器
    QMap<int, Sensor_485*> sensor485Instances;           // 存储 Sensor_485 实例，键为传感器ID
    QMap<QString, Sensor_485*> sensor485Connections;     // 存储 Sensor_485 实例，键为传感器名称
    QMap<QString, Sensor*> sensorInstances;               // 存储 Sensor 实例，键为传感器名称
    QList<ethercat*> ethercatWindows;                     // 存储 EtherCAT 窗口实例

    // 命令队列
    QQueue<QPair<QString, int>> commandQueue;             // PLC 命令队列
    QQueue<QPair<QString, int>> robotArmCommandQueue;     // 机器人手臂命令队列
    QQueue<QPair<QString, int>> sensorCommandQueue;       // 传感器命令队列

    // 其他成员变量
    int nextSensor485Id;                                   // 下一个 Sensor_485 的ID

    // 帮助函数
    QString processPreSend1(const QString& command);
    QString processPreSend2(const QString& command);

    bool checkTrial();  // 确保有这行声明
    int nextAvailablePort;  // 添加这个成员变量
    void sendCommands();
    void sendCommand(const QString& command);
    void processNextCommand();

    CommandScheduler* commandScheduler;
    QElapsedTimer timer;  // 添加计时器对象
    int accumulatedTime = 0; // 累计时间

    bool isEthercatInitialized;
    bool isEthercatConnected;
    QTimer* ethercatConnectionTimer;

    QTextEdit* debugTextEdit;

    ethercat* m_ethercat;  // ethercat實例的指針

signals:
    /**
     * @brief 转发文本摘要信号
     * @param summary 文本摘要内容
     */
    void forwardUpdateTextSummary(const QString &summary);
};

#endif // MAINWINDOW_H
