<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1920</width>
    <height>1080</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <property name="styleSheet">
   <string notr="true">
    /* 主窗口背景使用漸變色 */
    QMainWindow {
        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                  stop:0 #4a90e2, stop:1 #357abd);
    }
    
    /* 按鈕統一樣式 */
    QPushButton {
        border: 2px solid #2c3e50;
        border-radius: 6px;
        padding: 5px 15px;
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                  stop:0 #3498db, stop:1 #2980b9);
        color: white;
        font-weight: bold;
    }
    
    QPushButton:hover {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                  stop:0 #3cb0fd, stop:1 #3498db);
    }
    
    QPushButton:pressed {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                  stop:0 #2980b9, stop:1 #2472a4);
    }
    
    /* 特殊按鈕樣式 */
    #pushButton_Open_Sensor_2, #pushButton_Open_Sensor_485 {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                  stop:0 #f1c40f, stop:1 #f39c12);
        color: #2c3e50;
    }
    
    #pushButton_EtherCAT {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                  stop:0 #e74c3c, stop:1 #c0392b);
    }
    
    /* 標籤樣式 */
    QLabel {
        color: white;
        font-weight: bold;
    }
    
    /* 文本框樣式 */
    QLineEdit {
        border: 2px solid #bdc3c7;
        border-radius: 4px;
        padding: 3px;
        background: white;
        selection-background-color: #3498db;
    }
    
    /* 滾動區域樣式 */
    QScrollArea {
        border: 2px solid #34495e;
        border-radius: 6px;
        background: rgba(255, 255, 255, 0.1);
    }
    
    /* 下拉框樣式 */
    QComboBox {
        border: 2px solid #bdc3c7;
        border-radius: 4px;
        padding: 3px;
        background: white;
    }
    
    QComboBox::drop-down {
        border: none;
        width: 20px;
    }
    
    QComboBox::down-arrow {
        image: url(:/images/down_arrow.png);
    }
   </string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <widget class="QWidget" name="horizontalLayoutWidget">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>50</y>
      <width>1351</width>
      <height>51</height>
     </rect>
    </property>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QPushButton" name="pushButton_5">
       <property name="styleSheet">
        <string notr="true">background-color: rgb(208, 208, 208);
color: rgb(255, 255, 255);</string>
       </property>
       <property name="text">
        <string>存档</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_4">
       <property name="styleSheet">
        <string notr="true">background-color: rgb(208, 208, 208);
color: rgb(255, 255, 255);</string>
       </property>
       <property name="text">
        <string>打开档</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_Open_Sensor_2">
       <property name="styleSheet">
        <string notr="true">background-color: rgb(255, 255, 127);
color: rgb(0, 0, 0);</string>
       </property>
       <property name="text">
        <string>多線程Sensor/TPC</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_Open_Sensor_485">
       <property name="styleSheet">
        <string notr="true">background-color: rgb(255, 255, 127);
color: rgb(0, 0, 0);</string>
       </property>
       <property name="text">
        <string>多線程Sensor/485</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_2Logic">
       <property name="styleSheet">
        <string notr="true">background-color: rgb(208, 208, 208);
color: rgb(255, 255, 255);</string>
       </property>
       <property name="text">
        <string>Logic</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_Opendsize">
       <property name="styleSheet">
        <string notr="true">background-color: rgb(202, 202, 202);
color: rgb(255, 85, 127);</string>
       </property>
       <property name="text">
        <string>指定尺寸路径模型</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_EtherCAT">
       <property name="styleSheet">
        <string notr="true">background-color: rgb(202, 202, 202);</string>
       </property>
       <property name="text">
        <string>EtherCAT</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="openCommandSchedulerButton">
       <property name="styleSheet">
        <string notr="true">background-color: rgb(202, 202, 202);</string>
       </property>
       <property name="text">
        <string>打开命令调度器</string>
       </property>
      </widget>
     </item>
    </layout>
   </widget>
   <widget class="QWidget" name="horizontalLayoutWidget_2">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>0</y>
      <width>341</width>
      <height>51</height>
     </rect>
    </property>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <widget class="QLabel" name="label">
       <property name="font">
        <font>
         <pointsize>22</pointsize>
         <bold>true</bold>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">color: rgb(255, 255, 255);</string>
       </property>
       <property name="text">
        <string>SEWING_UPC_SYS</string>
       </property>
      </widget>
     </item>
    </layout>
   </widget>
   <widget class="QWidget" name="horizontalLayoutWidget_3">
    <property name="geometry">
     <rect>
      <x>380</x>
      <y>10</y>
      <width>131</width>
      <height>31</height>
     </rect>
    </property>
    <layout class="QHBoxLayout" name="horizontalLayout_3">
     <item>
      <widget class="QLabel" name="label_4">
       <property name="font">
        <font>
         <bold>true</bold>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">color: rgb(240, 240, 240);</string>
       </property>
       <property name="text">
        <string>          USER:</string>
       </property>
      </widget>
     </item>
    </layout>
   </widget>
   <widget class="QWidget" name="horizontalLayoutWidget_5">
    <property name="geometry">
     <rect>
      <x>640</x>
      <y>10</y>
      <width>131</width>
      <height>31</height>
     </rect>
    </property>
    <layout class="QHBoxLayout" name="horizontalLayout_5">
     <item>
      <widget class="QLabel" name="label_5">
       <property name="font">
        <font>
         <bold>true</bold>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">color: rgb(240, 240, 240);</string>
       </property>
       <property name="text">
        <string>        KEYS:</string>
       </property>
      </widget>
     </item>
    </layout>
   </widget>
   <widget class="QWidget" name="horizontalLayoutWidget_7">
    <property name="geometry">
     <rect>
      <x>900</x>
      <y>10</y>
      <width>131</width>
      <height>31</height>
     </rect>
    </property>
    <layout class="QHBoxLayout" name="horizontalLayout_7"/>
   </widget>
   <widget class="QPushButton" name="pushButton_6Active">
    <property name="geometry">
     <rect>
      <x>1040</x>
      <y>10</y>
      <width>129</width>
      <height>28</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <bold>true</bold>
     </font>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(0, 85, 255);
color: rgb(255, 255, 255);
</string>
    </property>
    <property name="text">
     <string>ACTIVE</string>
    </property>
   </widget>
   <widget class="QScrollArea" name="scrollArea">
    <property name="enabled">
     <bool>true</bool>
    </property>
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>160</y>
      <width>671</width>
      <height>71</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(120, 120, 120);</string>
    </property>
    <property name="widgetResizable">
     <bool>true</bool>
    </property>
    <widget class="QWidget" name="scrollAreaWidgetContents">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>0</y>
       <width>646</width>
       <height>2022</height>
      </rect>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout">
      <item>
       <widget class="QWidget" name="widget" native="true">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>2000</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">background-color: rgb(239, 239, 239);</string>
        </property>
        <widget class="QTextEdit" name="textEdit_total_action">
         <property name="geometry">
          <rect>
           <x>20</x>
           <y>10</y>
           <width>791</width>
           <height>551</height>
          </rect>
         </property>
        </widget>
       </widget>
      </item>
     </layout>
    </widget>
   </widget>
   <widget class="QPushButton" name="pushButton_sendorder">
    <property name="geometry">
     <rect>
      <x>580</x>
      <y>240</y>
      <width>111</width>
      <height>31</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(255, 85, 0);
color: rgb(255, 255, 255);</string>
    </property>
    <property name="text">
     <string>同步发送</string>
    </property>
   </widget>
   <widget class="QScrollArea" name="scrollArea_2">
    <property name="geometry">
     <rect>
      <x>700</x>
      <y>160</y>
      <width>791</width>
      <height>71</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(229, 229, 229);</string>
    </property>
    <property name="widgetResizable">
     <bool>true</bool>
    </property>
    <widget class="QWidget" name="scrollAreaWidgetContents_2">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>0</y>
       <width>822</width>
       <height>2022</height>
      </rect>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <item>
       <widget class="QWidget" name="widget_2" native="true">
        <property name="minimumSize">
         <size>
          <width>800</width>
          <height>2000</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">background-color: rgb(255, 255, 255);</string>
        </property>
        <widget class="QTextEdit" name="textEdit_Hex">
         <property name="geometry">
          <rect>
           <x>13</x>
           <y>16</y>
           <width>731</width>
           <height>531</height>
          </rect>
         </property>
        </widget>
       </widget>
      </item>
     </layout>
    </widget>
   </widget>
   <widget class="QPushButton" name="pushButton_8">
    <property name="geometry">
     <rect>
      <x>1920</x>
      <y>760</y>
      <width>93</width>
      <height>28</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(255, 85, 0);</string>
    </property>
    <property name="text">
     <string>COPY</string>
    </property>
   </widget>
   <widget class="QPushButton" name="pushButton_sendMain_2">
    <property name="geometry">
     <rect>
      <x>1400</x>
      <y>240</y>
      <width>93</width>
      <height>31</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(255, 85, 255);</string>
    </property>
    <property name="text">
     <string>发送HEX</string>
    </property>
   </widget>
   <widget class="QLineEdit" name="lineEdit_user">
    <property name="geometry">
     <rect>
      <x>510</x>
      <y>10</y>
      <width>129</width>
      <height>25</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(255, 255, 255);</string>
    </property>
   </widget>
   <widget class="QLineEdit" name="lineEdit_2key">
    <property name="geometry">
     <rect>
      <x>770</x>
      <y>10</y>
      <width>129</width>
      <height>25</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(255, 255, 255);</string>
    </property>
   </widget>
   <widget class="QLabel" name="label_6">
    <property name="geometry">
     <rect>
      <x>30</x>
      <y>120</y>
      <width>141</width>
      <height>19</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <pointsize>12</pointsize>
      <bold>true</bold>
     </font>
    </property>
    <property name="styleSheet">
     <string notr="true">color: rgb(255, 255, 255);</string>
    </property>
    <property name="text">
     <string>多线程命令区</string>
    </property>
   </widget>
   <widget class="QScrollArea" name="scrollArea_3">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>150</y>
      <width>1461</width>
      <height>331</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(120, 120, 120);</string>
    </property>
    <property name="widgetResizable">
     <bool>true</bool>
    </property>
    <widget class="QWidget" name="scrollAreaWidgetContents_3">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>0</y>
       <width>1457</width>
       <height>327</height>
      </rect>
     </property>
     <widget class="QTextEdit" name="textEdit_Muti">
      <property name="geometry">
       <rect>
        <x>13</x>
        <y>16</y>
        <width>1431</width>
        <height>301</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
     </widget>
    </widget>
   </widget>
   <widget class="QPushButton" name="pushButton_sendMain_3">
    <property name="geometry">
     <rect>
      <x>1350</x>
      <y>660</y>
      <width>93</width>
      <height>31</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(255, 85, 255);</string>
    </property>
    <property name="text">
     <string>SEND</string>
    </property>
   </widget>
   <widget class="QLabel" name="label_7">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>500</y>
      <width>69</width>
      <height>19</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <bold>true</bold>
     </font>
    </property>
    <property name="text">
     <string>预发射区1</string>
    </property>
   </widget>
   <widget class="QTextEdit" name="textEdit_presend1">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>530</y>
      <width>1321</width>
      <height>31</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(255, 255, 255);</string>
    </property>
   </widget>
   <widget class="QLabel" name="label_8">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>580</y>
      <width>69</width>
      <height>19</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <bold>true</bold>
     </font>
    </property>
    <property name="text">
     <string>预发射区2</string>
    </property>
   </widget>
   <widget class="QTextEdit" name="textEdit_presend2">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>610</y>
      <width>1321</width>
      <height>31</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(255, 255, 255);</string>
    </property>
   </widget>
   <widget class="QLabel" name="label_9">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>660</y>
      <width>69</width>
      <height>19</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <bold>true</bold>
     </font>
    </property>
    <property name="text">
     <string>总用时长</string>
    </property>
   </widget>
   <widget class="QLineEdit" name="lineEdit_mainwindow_totaltimes">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>690</y>
      <width>221</width>
      <height>25</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(255, 255, 255);</string>
    </property>
   </widget>
   <widget class="QTextEdit" name="textEdit_Debug">
    <property name="geometry">
     <rect>
      <x>250</x>
      <y>660</y>
      <width>1071</width>
      <height>121</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">
      background-color: rgb(0, 0, 0);
      color: rgb(0, 255, 0);
      font-family: 'Courier New';
      font-size: 12px;
     </string>
    </property>
    <property name="readOnly">
     <bool>true</bool>
    </property>
    <property name="placeholderText">
     <string>Debug Output Window</string>
    </property>
   </widget>
   <widget class="QPushButton" name="pushButton_ClearDebug">
    <property name="geometry">
     <rect>
      <x>1350</x>
      <y>710</y>
      <width>91</width>
      <height>41</height>
     </rect>
    </property>
    <property name="text">
     <string>Clear Log</string>
    </property>
   </widget>
   <widget class="QPushButton" name="pushButton_go_0">
    <property name="geometry">
     <rect>
      <x>1060</x>
      <y>110</y>
      <width>121</width>
      <height>28</height>
     </rect>
    </property>
    <property name="text">
     <string>回零位</string>
    </property>
   </widget>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1920</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
