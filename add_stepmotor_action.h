#ifndef ADD_STEPMOTOR_ACTION_H
#define ADD_STEPMOTOR_ACTION_H

#include <QDialog>
#include <QSerialPort>

namespace Ui {
class add_stepMotor_action;
}

//motorCommand命令结构体
struct MotorCommand {
    int speed;
    int duration;
    bool start;
    QString commandString; // 新增加的成员，用于存储命令字符串


};

class add_stepMotor_action : public QDialog
{
    Q_OBJECT

    //存储motor命令的列表
    QList<MotorCommand> commands;

    void addCommand(int speed, int duration, bool start);



signals:
    void requestUpdateTextSummary(const QString &summary);



public:
    explicit add_stepMotor_action(QWidget *parent = nullptr);
    ~add_stepMotor_action();

    void setDeviceName(const QString &name);
    void setDeviceType(const QString &type);

    void setSerialPortSettings(const QString &portName, qint32 baudRate, QSerialPort::DataBits dataBits, QSerialPort::Parity parity, QSerialPort::StopBits stopBits, QSerialPort::FlowControl flowControl);


    void sendCommand(int speed, int duration, bool start); // 确保此函数在类内声明



private:
    Ui::add_stepMotor_action *ui;
    QSerialPort serial;
    void setupSerialPort();
    QString deviceName; // 声明存储设备名称的变量
    QString deviceType; // 声明存储设备类型的变量
    //void on_pushButton_Addaction_clicked();

public slots:
    void on_pushButton_Addaction_clicked();
    void on_pushButton_add_to_mainwindow_clicked(); // 新增槽函数声明


};

#endif // ADD_STEPMOTOR_ACTION_H
