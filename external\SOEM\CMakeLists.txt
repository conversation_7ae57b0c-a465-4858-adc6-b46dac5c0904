cmake_minimum_required(VERSION 3.5)

# 重置所有可能影響項目配置的變量
unset(CMAKE_PROJECT_INCLUDE_BEFORE CACHE)
unset(CMAKE_PROJECT_INCLUDE CACHE)
unset(CMAKE_PROJECT_INCLUDE_DIRECTORIES CACHE)

# 設置基本項目信息
project(SOEM C)

set(CMAKE_C_STANDARD 99)
set(CMAKE_C_STANDARD_REQUIRED ON)

if(WIN32)
    if(CMAKE_SIZEOF_VOID_P EQUAL 8)
        set(NPCAP_SDK_DIR "C:/Program Files/Npcap/SDK" CACHE PATH "Path to Npcap SDK")
    else()
        set(NPCAP_SDK_DIR "C:/Program Files (x86)/Npcap/SDK" CACHE PATH "Path to Npcap SDK")
    endif()

    # 簡化宏定義，避免衝突
    add_definitions(
        -DHAVE_REMOTE
        -DWPCAP
        -DPCAP_DONT_INCLUDE_PCAP_BPF_H
        -D_NO_W32_PSEUDO_MODIFIERS
    )
endif()

# 添加所有必要的包含路徑
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/soem
    ${CMAKE_CURRENT_SOURCE_DIR}/osal
    ${CMAKE_CURRENT_SOURCE_DIR}/osal/win32
    ${CMAKE_CURRENT_SOURCE_DIR}/oshw/win32
)

add_library(soem STATIC
    soem/ethercatbase.c
    soem/ethercatcoe.c
    soem/ethercatconfig.c
    soem/ethercatdc.c
    soem/ethercatfoe.c
    soem/ethercatmain.c
    soem/ethercatprint.c
    soem/ethercatsoe.c
    osal/win32/osal.c
    oshw/win32/nicdrv.c
    oshw/win32/oshw.c
)

if(WIN32)
    if(CMAKE_SIZEOF_VOID_P EQUAL 8)
        set(NPCAP_LIB_DIR "${NPCAP_SDK_DIR}/Lib/x64")
    else()
        set(NPCAP_LIB_DIR "${NPCAP_SDK_DIR}/Lib")
    endif()

    target_include_directories(soem 
        PUBLIC
            ${CMAKE_CURRENT_SOURCE_DIR}
            ${CMAKE_CURRENT_SOURCE_DIR}/soem
            ${CMAKE_CURRENT_SOURCE_DIR}/osal
            ${CMAKE_CURRENT_SOURCE_DIR}/osal/win32
            ${CMAKE_CURRENT_SOURCE_DIR}/oshw/win32
        PRIVATE 
            ${NPCAP_SDK_DIR}/Include
    )

    target_link_directories(soem PRIVATE ${NPCAP_LIB_DIR})
    
    target_link_libraries(soem PRIVATE 
        "${NPCAP_LIB_DIR}/wpcap.lib"
        "${NPCAP_LIB_DIR}/Packet.lib"
        iphlpapi
        ws2_32 
        winmm
    )
endif()