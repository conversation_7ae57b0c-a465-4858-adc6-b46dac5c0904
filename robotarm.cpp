#include "robotarm.h"
#include "ui_robotarm.h"
#include <QPair>
#include <QDebug>


QString RobotArm::getROBOIpAddress() const {
    return ui->lineEdit_RIP->text();
}

quint16 RobotArm::getROBOPort() const {
    return static_cast<quint16>(ui->lineEdit_RPORT->text().toInt());
}

// 构造函数
RobotArm::RobotArm(QWidget *parent)
    : QDialog(parent)
    , ui(new Ui::RobotArm)
{

    qDebug() << "RobotArm 构造函数被调用";
    ui->setupUi(this);

    tcpSocket = new QTcpSocket(this);
    connect(tcpSocket, &QTcpSocket::readyRead, this, &RobotArm::onReadyRead);
    connect(ui->pushButton_RConnect, &QPushButton::clicked, this, &RobotArm::on_pushButton_RConnect_clicked);
    connect(ui->pushButton_RSendTest_Common, &QPushButton::clicked, this, &RobotArm::pushButton_RSendTest_Common_clicked);





}



// 析构函数
RobotArm::~RobotArm()
{

    qDebug() << "RobotArm 析构函数被调用";
    delete ui;
}
// TCP連接
void RobotArm::on_pushButton_RConnect_clicked() {
    QString ip = ui->lineEdit_RIP->text();
    int port = ui->lineEdit_RPORT->text().toInt();

    tcpSocket->connectToHost(ip, port);

    if (tcpSocket->waitForConnected(3000)) { // 等待3秒尝试连接
        ui->label_RChec_Connect->setStyleSheet("QLabel { color : green; }");
        ui->label_RChec_Connect->setText("连接成功");
    } else {
        ui->label_RChec_Connect->setStyleSheet("QLabel { color : red; }");
        ui->label_RChec_Connect->setText("连接失败：" + tcpSocket->errorString());
    }
}
void RobotArm::onReadyRead() {
    QByteArray data = tcpSocket->readAll();

    ui->textEdit_R_Back_Massage->setText(QString::fromUtf8(data));
}

//向服務器發射測試信號
void RobotArm::pushButton_RSendTest_Common_clicked() {
    // 从文本编辑框获取文本，假设这是16进制表示的字符串
    QString hexString = ui->textEdit_Rbsend->toPlainText();

    // 将16进制字符串转换为二进制数据
    QByteArray binaryData = QByteArray::fromHex(hexString.toUtf8());

    // 发送转换后的二进制数据
    tcpSocket->write(binaryData);

    qDebug() << "[RobotArm] 向服务器发送16进制消息：" << binaryData.toHex();
}


//signals:
//void addROBOrderToMainWindow(QPair<QString, QString> ROBOrder);

// RobotArm.cpp

//在機械臂組控制命令文本發射組命令到主要動作列表。
void RobotArm::on_pushButton_sendMain_clicked() {
    // 获取文本编辑框中的文本
    QString RoboOrderText = ui->textEdit_RealCommonRb->toPlainText();
    QPair<QString, QString> RoboOrder = qMakePair(RoboOrderText, "ROBO");
    //QString text = ui->textEdit_RealCommonRb->toPlainText();

    //emit addPlcOrderToMainWindow(plcOrder);
    emit commandIssued(RoboOrder);
}






