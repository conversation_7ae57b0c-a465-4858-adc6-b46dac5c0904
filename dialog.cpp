#include "dialog.h"
#include "ui_dialog.h"
#include <QSerialPort>
#include <QMessageBox>
#include <QDebug>



// 构造函数
Dialog::Dialog(QWidget *parent)
    : QDialog(parent)
    , ui(new Ui::Dialog)
{
    ui->setupUi(this);  // 初始化用户界面
    populateSerialPortNames(); // 确保可用的串行端口在UI显示前被检测和填充

    // 向界面中的第一个下拉菜单（comboBox）添加选项，这些选项代表不同的设备类型
    ui->comboBox->addItem("步进电机");
    ui->comboBox->addItem("伺服电机");
    //ui->comboBox->addItem("传感器");
    //ui->comboBox->addItem("六轴机械臂勃朗特1510a");
    //ui->comboBox->addItem("PLC");

    // 向 UI 中的 QComboBox2 添加项目
     ui->comboBox_2->addItem("UART");
     //ui->comboBox_2->addItem("TCP");
     //ui->comboBox_2->addItem("Profibus");
     //ui->comboBox_2->addItem("Canopen");
     //ui->comboBox_2->addItem("Ethernet/IP");
     //ui->comboBox_2->addItem("CC-Link");

     // 为波特率输入框（lineEditBaudRate）设置一个验证器，确保用户只能输入介于300到4000000之间的数值
     ui->lineEditBaudRate->setValidator(new QIntValidator(300, 4000000, this));

     // 连接“设置”按钮（handleSetBaudRateButton）的 clicked() 信号到槽函数 void Dialog::handleSetBaudRateButtonClick()
     // 当按钮被点击时，执行 handleSetBaudRateButtonClick() 中的代码
     // 使用显式连接代替自动连接

     //当用户点击 handleSetBaudRateButton 按钮时，自动调用当前 Dialog 实例中的 handleSetBaudRateButtonClick 函数。
     connect(ui->handleSetBaudRateButton, &QPushButton::clicked, this, &Dialog::handleSetBaudRateButtonClick);
     connect(ui->pushButton_action, &QPushButton::clicked, this, &Dialog::showStepMotorAction);


     //add_stepMotor_action:

     // 实例化 add_stepMotor_action 类
     myStepMotorAction = new add_stepMotor_action(this);// 实例化第三层

     connect(myStepMotorAction, &add_stepMotor_action::requestUpdateTextSummary,
             this, &Dialog::relayUpdateTextSummary); // 将第三层信号连接到第二层的信号上进行转发











}


// 析构函数
Dialog::~Dialog()
{
    delete ui;  // 删除用户界面，释放资源
}


//自動尋找本機可用接口
void Dialog::populateSerialPortNames() {
    qDebug() << "开始填充串行端口到下拉菜单";
    ui->PortNamecomboBox_3->clear(); // 首先清空下拉菜单中的现有项

    const auto serialPortInfos = QSerialPortInfo::availablePorts();

    for (const QSerialPortInfo &serialPortInfo : serialPortInfos) {
        QString portItem = serialPortInfo.portName() + " - " + serialPortInfo.description();
        qDebug() << "Adding serial port to the list:" << portItem;
        ui->PortNamecomboBox_3->addItem(portItem, serialPortInfo.portName());

    }
    qDebug() << "完成填充串行端口到下拉菜单";


}

void Dialog::handleConfigureSerialPort() {
    // 函数体实现，根据需要进行串行端口配置
    // 例如，您可以在这里根据 UI 控件的值来配置串行端口
}






// 用于处理从串行端口接收到的数据的槽函数
void Dialog::handleReadyRead() {
    QByteArray data = serialPort.readAll();// 读取所有可用的数据
    // 在这里处理接收到的数据，例如显示在 UI 上或者进行其他处理
}


// 当“handleSetBaudRateButtonClick”按钮被点击时执行的槽函数
void Dialog::handleSetBaudRateButtonClick() {
    bool ok;

    // 构建信息字符串，用于之后显示在 textEdit_2
    QString infoString;
    // 读取用户输入和配置
    QString deviceName = ui->lineEdit_Name->text(); // 设备名称
    QString portName = ui->PortNamecomboBox_3->currentText(); // 端口名称
    QString deviceType = ui->comboBox->currentText(); // 设备类型
    int baudRate = ui->lineEditBaudRate->text().toInt(&ok); // 波特率
    int dataBits = ui->lineEditDataBit->text().toInt(); // 数据位
    int stopBits = ui->lineEditStopBit->text().toInt(); // 停止位
    QSerialPort::Parity parity = static_cast<QSerialPort::Parity>(ui->lineEditParity->text().toInt()); // 奇偶校验
    QSerialPort::FlowControl flowControl = static_cast<QSerialPort::FlowControl>(ui->lineEditFlowContronl->text().toInt()); // 流控制



    if (ok && baudRate > 0) {
        // 将获取的设置值赋给串行端口对象
        serialPort.setPortName(portName);
        serialPort.setBaudRate(baudRate);
        serialPort.setDataBits(static_cast<QSerialPort::DataBits>(dataBits));
        serialPort.setStopBits(static_cast<QSerialPort::StopBits>(stopBits));
        serialPort.setParity(parity);
        serialPort.setFlowControl(flowControl);

        // 构建要显示的信息字符串
        infoString = QString("设备名称: %1\n设备类型: %2\n端口名称: %3\n波特率: %4\n数据位: %5\n停止位: %6\n奇偶校验: %7\n流控制: %8")
                         .arg(deviceName).arg(deviceType).arg(portName).arg(baudRate).arg(dataBits).arg(stopBits)
                         .arg(parity == QSerialPort::NoParity ? "无" : parity == QSerialPort::EvenParity ? "偶校验" : "奇校验")
                         .arg(flowControl == QSerialPort::NoFlowControl ? "无" : flowControl == QSerialPort::HardwareControl ? "硬件" : "软件");

        // 尝试打开串行端口
        if (!serialPort.open(QIODevice::ReadWrite)) {
            // 如果打开失败，显示一个错误消息框
            QMessageBox::critical(this, "Error", "Failed to open serial port.");
            infoString += "\n打开串行端口失败！";
        } else {
            // 打开成功
            infoString += "\n串行端口打开成功！";
            // 在配置完串行端口的基本参数后，立即连接 readyRead() 信号到 handleReadyRead() 槽
            connect(&serialPort, &QSerialPort::readyRead, this, &Dialog::handleReadyRead);
        }
    } else {
        // 如果输入无效，显示一个警告消息框提示用户输入有效的波特率
        QMessageBox::warning(this, "Invalid Baud Rate", "Please enter a valid baud rate.");
        return;
    }
    // 将信息显示在 textEdit_2
    ui->textEdit_2->setText(infoString);
}

// 配置串行端口的函数，这里不再设置参数，只负责打开串行端口
void Dialog::configureSerialPort()
{
    // 在配置完串行端口的基本参数后，立即连接 readyRead() 信号到 handleReadyRead() 槽
    connect(&serialPort, &QSerialPort::readyRead, this, &Dialog::handleReadyRead);

    // 尝试打开串行端口
    if (!serialPort.open(QIODevice::ReadWrite)) {
        // 如果打开失败，显示一个错误消息框
        QMessageBox::critical(this, "Error", "Failed to open serial port.");
    } else {
        // 打开成功后的操作
    }

}
void Dialog::showStepMotorAction() {
    if (!myStepMotorAction) {
        myStepMotorAction = new add_stepMotor_action(this);
    }
    // 获取设备名称和类型并设置
    QString currentName = ui->lineEdit_Name->text();  // 获取当前lineEdit_Name的文本
    QString currentType = ui->comboBox->currentText(); // 获取comboBox当前选中的文本
    myStepMotorAction->setDeviceName(currentName);  // 设置到弹出窗口的device_name中
    myStepMotorAction->setDeviceType(currentType);  // 设置到弹出窗口的device_name_2中

    // 获取并传递串行端口配置
    QString portName = ui->PortNamecomboBox_3->currentText(); // 端口名称
    qint32 baudRate = ui->lineEditBaudRate->text().toInt(); // 波特率
    QSerialPort::DataBits dataBits = static_cast<QSerialPort::DataBits>(ui->lineEditDataBit->text().toInt()); // 数据位
    QSerialPort::Parity parity = static_cast<QSerialPort::Parity>(ui->lineEditParity->text().toInt()); // 奇偶校验
    QSerialPort::StopBits stopBits = static_cast<QSerialPort::StopBits>(ui->lineEditStopBit->text().toInt()); // 停止位
    QSerialPort::FlowControl flowControl = static_cast<QSerialPort::FlowControl>(ui->lineEditFlowContronl->text().toInt()); // 流控制

    myStepMotorAction->setSerialPortSettings(portName, baudRate, dataBits, parity, stopBits, flowControl);


    myStepMotorAction->show();  // 显示窗口
}

// 在 dialog.cpp 中实现槽函数
void Dialog::relayUpdateTextSummary(const QString &summary) {
    // 在这里，你可以进行一些处理，如果需要的话
    // 然后发出 relayUpdateTextSummary 信号，将 summary 传递给下一个接收者
    qDebug() << "2step_Received and relaying summary:" << summary; // 在转发信号之前添加调试输出
    emit forwardUpdateTextSummary(summary);
}
